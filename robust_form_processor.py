"""
ROBUST FORM PROCESSOR - Works without field coordinates
Final solution for production PA form automation
"""

import json
import hashlib
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
from PyPDF2 import PdfReader, PdfWriter
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RobustFieldMatcher:
    """Robust field matching without coordinate dependency"""
    
    def __init__(self):
        # Enhanced field mapping patterns
        self.field_patterns = {
            # Patient name patterns
            'patient_first_name': [
                'first', 'fname', 'given', 'T14', 'patient_first', 'name_first'
            ],
            'patient_last_name': [
                'last', 'lname', 'surname', 'T15', 'patient_last', 'name_last'
            ],
            'patient_full_name': [
                'full_name', 'patient_name', 'name', 'T18'
            ],
            
            # Demographics
            'patient_dob': [
                'dob', 'birth', 'T16', 'date_of_birth', 'birthdate'
            ],
            'patient_address': [
                'address', 'street', 'T17', 'T19', 'residence', 'home_address'
            ],
            'patient_city': [
                'city', 'town', 'T20'
            ],
            'patient_state': [
                'state', 'st', 'T20', 'province'  # Often shares field with city
            ],
            'patient_zip': [
                'zip', 'postal', 'T21', 'zipcode', 'post_code'
            ],
            
            # Contact info
            'patient_phone': [
                'phone', 'tel', 'T21C', 'telephone', 'cell', 'mobile'
            ],
            'patient_home_phone': [
                'home_phone', 'phone_home', 'home_tel'
            ],
            'patient_work_phone': [
                'work_phone', 'phone_work', 'business_phone'
            ],
            
            # Insurance
            'insurance_member_id': [
                'member', 'insurance_id', 'T11', 'policy', 'member_id'
            ],
            'insurance_group': [
                'group', 'plan_id', 'group_number'
            ],
            
            # Provider
            'provider_npi': [
                'npi', 'provider_id', 'T12', 'physician_id'
            ],
            'provider_name': [
                'provider', 'physician', 'doctor', 'prescriber'
            ]
        }
        
        # Common field name patterns for PA forms
        self.pa_form_mappings = {
            # Aetna-style mappings (T-prefix fields)
            'T11': 'insurance_member_id',
            'T12': 'provider_npi', 
            'T14': 'patient_first_name',
            'T15': 'patient_last_name',
            'T16': 'patient_dob',
            'T17': 'patient_address',
            'T18': 'patient_full_name',
            'T19': 'patient_address',
            'T20': 'patient_state',
            'T21': 'patient_zip',
            'T21C': 'patient_phone',
            
            # Common descriptive field names
            'Phone T': 'patient_phone',
            'Fax T': 'provider_fax',
            'Request by T': 'provider_name',
            'Insurance Info T.7': 'insurance_member_id'
        }

    def map_fields_to_canonical(self, form_fields: Dict) -> Dict[str, str]:
        """Map PDF field names to canonical data keys"""
        mappings = {}
        
        for field_name in form_fields.keys():
            canonical_key = self._find_canonical_key(field_name)
            if canonical_key:
                mappings[field_name] = canonical_key
                logger.info(f"📍 {field_name:20s} → {canonical_key}")
        
        logger.info(f"🎯 Created {len(mappings)} field mappings")
        return mappings

    def _find_canonical_key(self, field_name: str) -> Optional[str]:
        """Find canonical key for field name"""
        field_lower = field_name.lower()
        
        # Direct mapping check
        if field_name in self.pa_form_mappings:
            return self.pa_form_mappings[field_name]
        
        # Pattern matching
        best_match = None
        best_score = 0
        
        for canonical_key, patterns in self.field_patterns.items():
            score = 0
            for pattern in patterns:
                if pattern.lower() in field_lower:
                    score += len(pattern) / len(field_lower)  # Longer matches score higher
            
            if score > best_score and score > 0.3:  # Minimum threshold
                best_score = score
                best_match = canonical_key
        
        return best_match

class RobustFormProcessor:
    """Robust form processor for production PA automation"""
    
    def __init__(self):
        self.matcher = RobustFieldMatcher()
        self.templates_dir = Path("robust_templates")
        self.templates_dir.mkdir(exist_ok=True)

    def process_form(self, pdf_path: Path, patient_data: Dict, output_path: Path) -> Dict:
        """Process PA form robustly"""
        logger.info(f"🚀 ROBUST FORM PROCESSING: {pdf_path}")
        
        try:
            # Get PDF fingerprint for template caching
            fingerprint = self._get_pdf_fingerprint(pdf_path)
            
            # Get or create field mappings
            field_mappings = self._get_or_create_mappings(pdf_path, fingerprint)
            
            # Extract patient values
            patient_values = self._extract_patient_values(patient_data)
            
            # Create form field values
            form_values = {}
            filled_fields = []
            
            for pdf_field, canonical_key in field_mappings.items():
                value = patient_values.get(canonical_key)
                if value and self._validate_field_value(canonical_key, value):
                    form_values[pdf_field] = value
                    filled_fields.append({
                        'field': pdf_field,
                        'canonical_key': canonical_key,
                        'value': value
                    })
            
            # Fill the PDF
            if form_values:
                filled_count = self._fill_pdf_form(pdf_path, form_values, output_path)
                
                return {
                    'success': True,
                    'fingerprint': fingerprint,
                    'total_fields': len(field_mappings),
                    'filled_count': filled_count,
                    'output_path': str(output_path),
                    'filled_fields': filled_fields
                }
            else:
                return {
                    'success': False,
                    'error': 'No valid field values found'
                }
                
        except Exception as e:
            logger.error(f"❌ Robust processing failed: {e}")
            return {'success': False, 'error': str(e)}

    def _get_pdf_fingerprint(self, pdf_path: Path) -> str:
        """Get PDF fingerprint for template identification"""
        with open(pdf_path, 'rb') as f:
            sample = f.read(1024*1024)  # First 1MB
        return hashlib.sha256(sample).hexdigest()[:12]

    def _get_or_create_mappings(self, pdf_path: Path, fingerprint: str) -> Dict[str, str]:
        """Get existing mappings or create new ones"""
        template_path = self.templates_dir / f"mappings_{fingerprint}.json"
        
        if template_path.exists():
            logger.info(f"✅ Loading cached mappings: {template_path}")
            with open(template_path, 'r') as f:
                template = json.load(f)
                return template.get('field_mappings', {})
        
        # Create new mappings
        logger.info(f"🔧 Creating new field mappings")
        reader = PdfReader(str(pdf_path))
        form_fields = reader.get_fields() or {}
        
        # Filter to text fields only
        text_fields = {}
        for field_name, field_obj in form_fields.items():
            field_type = field_obj.get("/FT", "")
            if "/Tx" in str(field_type):
                text_fields[field_name] = field_obj
        
        logger.info(f"📊 Found {len(text_fields)} text fields out of {len(form_fields)} total")
        
        # Create mappings
        mappings = self.matcher.map_fields_to_canonical(text_fields)
        
        # Save template
        template = {
            'fingerprint': fingerprint,
            'pdf_name': pdf_path.name,
            'created': datetime.now().isoformat(),
            'total_fields': len(form_fields),
            'text_fields': len(text_fields),
            'mapped_fields': len(mappings),
            'field_mappings': mappings
        }
        
        with open(template_path, 'w') as f:
            json.dump(template, f, indent=2)
        
        logger.info(f"💾 Saved mappings template: {template_path}")
        return mappings

    def _extract_patient_values(self, patient_data: Dict) -> Dict[str, str]:
        """Extract patient values in flat format"""
        values = {}
        
        if 'tier_1_mandatory_fields' in patient_data:
            tier1 = patient_data['tier_1_mandatory_fields']
            
            # Patient demographics
            if 'patient_demographics' in tier1:
                demo = tier1['patient_demographics']
                
                # Full name and parts
                full_name = demo.get('full_name', {}).get('value', '')
                values['patient_full_name'] = full_name
                
                if full_name:
                    parts = full_name.split()
                    values['patient_first_name'] = parts[0] if parts else ''
                    values['patient_last_name'] = parts[-1] if len(parts) > 1 else ''
                
                # Other demographics
                values['patient_dob'] = demo.get('date_of_birth', {}).get('value', '')
                
                # Address
                address = demo.get('address', {}).get('value', '')
                values['patient_address'] = address
                
                # Parse address components
                if address:
                    # "789 Oak Street, Dallas, TX-75201"
                    addr_match = re.match(r'(.+),\s*(.+),\s*([A-Z]{2})[- ]?(\d{5})', address)
                    if addr_match:
                        values['patient_city'] = addr_match.group(2).strip()
                        values['patient_state'] = addr_match.group(3).strip()
                        values['patient_zip'] = addr_match.group(4).strip()
                
                # Phone numbers
                phones = demo.get('phone_numbers', {}).get('value', [])
                if phones:
                    values['patient_phone'] = phones[0]
                    values['patient_home_phone'] = phones[0]
                    values['patient_work_phone'] = phones[0]
            
            # Insurance
            if 'insurance_information' in tier1:
                ins = tier1['insurance_information'].get('primary_insurance', {})
                values['insurance_member_id'] = ins.get('member_id', {}).get('value', '')
                values['insurance_group'] = ins.get('group_number', {}).get('value', '')
            
            # Provider
            if 'prescriber_information' in tier1:
                prov = tier1['prescriber_information']
                values['provider_name'] = prov.get('ordering_physician', {}).get('value', '')
                values['provider_npi'] = prov.get('npi', {}).get('value', '')
        
        # Clean up values
        clean_values = {}
        for key, value in values.items():
            if value and str(value).strip():
                clean_values[key] = str(value).strip()
        
        logger.info(f"📋 Extracted {len(clean_values)} patient values")
        for key, value in clean_values.items():
            logger.info(f"  {key:25s} = {value}")
        
        return clean_values

    def _validate_field_value(self, canonical_key: str, value: str) -> bool:
        """Validate field value format"""
        if not value or not value.strip():
            return False
        
        # DOB validation
        if canonical_key == 'patient_dob':
            return bool(re.match(r'\d{4}-\d{2}-\d{2}', value))
        
        # NPI validation
        if canonical_key == 'provider_npi':
            return bool(re.match(r'\d{10}', value.replace('-', '')))
        
        # Name validation
        if 'name' in canonical_key:
            return bool(re.match(r'^[A-Za-z\s\'\-\.]+$', value))
        
        return True

    def _fill_pdf_form(self, pdf_path: Path, form_values: Dict[str, str], output_path: Path) -> int:
        """Fill PDF form with field values"""
        reader = PdfReader(str(pdf_path))
        writer = PdfWriter()
        
        # Copy all pages
        for page in reader.pages:
            writer.add_page(page)
        
        # Fill form fields
        logger.info("✍️ Filling PDF fields:")
        for field_name, value in form_values.items():
            logger.info(f"  ✅ {field_name:20s} = {value}")
        
        # Update form fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
        
        # Save filled PDF
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        return len(form_values)

def test_robust_processor():
    """Test robust processor with all patients"""
    print("🛡️  TESTING ROBUST FORM PROCESSOR")
    print("="*60)
    print("✅ Coordinate-independent field mapping")
    print("✅ Enhanced pattern matching")
    print("✅ Template caching with fingerprints")
    print("✅ Comprehensive validation")
    print("-"*60)
    
    patients = [
        ("Abdullah", "Input Data/Extracted_ground_truth/abdullah_structured.json", "Input Data/Adbulla/PA.pdf"),
        ("Akshay", "Input Data/Extracted_ground_truth/akshay_structured.json", "Input Data/Akshay/pa.pdf")
    ]
    
    processor = RobustFormProcessor()
    results = []
    
    for patient_name, data_path, pdf_path in patients:
        print(f"\n👤 {patient_name.upper()}")
        print("-" * 40)
        
        if not Path(data_path).exists() or not Path(pdf_path).exists():
            print(f"❌ Files not found for {patient_name}")
            continue
        
        # Load patient data
        with open(data_path, 'r') as f:
            patient_data = json.load(f)
        
        # Process form
        output_path = Path(f"{patient_name.lower()}_robust_filled.pdf")
        result = processor.process_form(Path(pdf_path), patient_data, output_path)
        
        print(f"Success: {result['success']}")
        if result['success']:
            print(f"Fields Mapped: {result['total_fields']}")
            print(f"Fields Filled: {result['filled_count']}")
            print(f"Success Rate: {result['filled_count']}/{result['total_fields']} ({result['filled_count']/result['total_fields']*100:.1f}%)")
            print(f"Output: {result['output_path']}")
            
            # Show filled fields
            print("Filled fields:")
            for field in result['filled_fields']:
                print(f"  {field['field']:15s} → {field['canonical_key']:20s} = {field['value']}")
        else:
            print(f"Error: {result['error']}")
        
        results.append(result)
    
    # Summary
    successful = [r for r in results if r.get('success', False)]
    total_filled = sum(r.get('filled_count', 0) for r in successful)
    total_possible = sum(r.get('total_fields', 0) for r in successful)
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"Patients Processed: {len(results)}")
    print(f"Success Rate: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.1f}%)")
    print(f"Overall Fill Rate: {total_filled}/{total_possible} ({total_filled/total_possible*100:.1f}%)")
    print(f"🎉 ROBUST PROCESSING COMPLETE!")
    
    return results

if __name__ == "__main__":
    test_robust_processor()