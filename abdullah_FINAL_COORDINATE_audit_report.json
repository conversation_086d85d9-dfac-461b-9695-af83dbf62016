{"timestamp": "2025-06-13T12:47:51.369490", "input_form": "Input Data/Adbulla/PA.pdf", "output_form": "ab<PERSON><PERSON>_FINAL_COORDINATE.pdf", "form_type": "medicare_pa_rituxan", "filled_fields_count": 11, "filled_fields": [{"field": "insurance_member_id", "value": "A987654321", "position": [150, 250]}, {"field": "patient_first_name", "value": "<PERSON>", "position": [150, 280]}, {"field": "patient_last_name", "value": "<PERSON>", "position": [350, 280]}, {"field": "patient_dob", "value": "1975-08-23", "position": [150, 310]}, {"field": "patient_address", "value": "789 Oak Street", "position": [150, 340]}, {"field": "patient_city", "value": "Dallas", "position": [150, 370]}, {"field": "patient_state", "value": "TX", "position": [350, 370]}, {"field": "patient_zip", "value": "75201", "position": [450, 370]}, {"field": "patient_phone", "value": "************", "position": [150, 400]}, {"field": "provider_name", "value": "<PERSON>, MD", "position": [150, 450]}, {"field": "provider_npi", "value": "**********", "position": [150, 480]}], "errors_count": 0, "errors": [], "available_patient_data": ["patient_first_name", "patient_last_name", "patient_dob", "patient_address", "patient_city", "patient_state", "patient_zip", "patient_phone", "insurance_member_id", "provider_name", "provider_npi"], "success_rate": "100.0%"}