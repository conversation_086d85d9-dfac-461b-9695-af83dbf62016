"""
SIMPLE COORDINATE-BASED FORM FILLER
Places text at manually determined coordinates based on visual inspection
"""

import fitz
import json
from pathlib import Path
from typing import Dict, List, Tuple
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleCoordinateFiller:
    """Fill forms using manually determined coordinates"""
    
    def __init__(self):
        # Manual coordinate mappings based on visual inspection
        # These will be determined by looking at the actual forms
        
        # <PERSON>'s Medicare form coordinates (will determine these)
        self.abdullah_coordinates = {
            # Patient info section (estimated positions)
            "patient_first_name": (200, 300),
            "patient_last_name": (400, 300), 
            "patient_dob": (200, 330),
            "patient_address": (200, 360),
            "patient_city": (200, 390),
            "patient_state": (400, 390),
            "patient_zip": (500, 390),
            "patient_phone": (200, 420),
            "insurance_member_id": (200, 450),
            "provider_name": (200, 500),
            "provider_npi": (200, 530)
        }
        
        # <PERSON><PERSON><PERSON>'s Aetna form coordinates (will determine these)
        self.akshay_coordinates = {
            "patient_first_name": (150, 250),
            "patient_last_name": (350, 250),
            "patient_dob": (150, 280),
            "patient_address": (150, 310),
            "patient_city": (150, 340),
            "patient_state": (350, 340),
            "patient_zip": (450, 340),
            "patient_phone": (150, 370),
            "insurance_member_id": (150, 400),
            "provider_name": (150, 450),
            "provider_npi": (150, 480)
        }
    
    def get_form_coordinates(self, pdf_path: Path) -> Dict[str, Tuple[float, float]]:
        """Get coordinate mapping for a specific form"""
        pdf_name = pdf_path.name.lower()
        
        if "abdullah" in str(pdf_path).lower() or "adbulla" in str(pdf_path).lower():
            return self.abdullah_coordinates
        elif "akshay" in str(pdf_path).lower():
            return self.akshay_coordinates
        else:
            # Default coordinates for unknown forms
            return {
                "patient_first_name": (100, 200),
                "patient_last_name": (300, 200),
                "patient_dob": (100, 230),
                "patient_address": (100, 260),
                "patient_city": (100, 290),
                "patient_state": (300, 290),
                "patient_zip": (400, 290),
                "patient_phone": (100, 320),
                "insurance_member_id": (100, 350),
                "provider_name": (100, 400),
                "provider_npi": (100, 430)
            }
    
    def extract_text_layout(self, pdf_path: Path) -> List[Dict]:
        """Extract all text from PDF to understand layout"""
        logger.info(f"📋 Extracting text layout from: {pdf_path}")
        
        doc = fitz.open(str(pdf_path))
        page = doc[0]
        
        # Get all text with positions
        text_dict = page.get_text("dict")
        text_elements = []
        
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            text_elements.append({
                                "text": text,
                                "x": bbox[0],
                                "y": bbox[1],
                                "bbox": bbox
                            })
        
        doc.close()
        
        # Sort by position
        text_elements.sort(key=lambda x: (x["y"], x["x"]))
        
        logger.info(f"📄 Found {len(text_elements)} text elements")
        return text_elements
    
    def find_field_positions(self, pdf_path: Path) -> Dict[str, Tuple[float, float]]:
        """Try to automatically find field positions by looking for labels"""
        text_elements = self.extract_text_layout(pdf_path)
        
        positions = {}
        
        # Look for common field labels
        field_patterns = {
            "patient_first_name": ["first name", "first", "name"],
            "patient_last_name": ["last name", "last", "surname"],
            "patient_dob": ["date of birth", "dob", "birth date"],
            "patient_address": ["address", "street"],
            "patient_city": ["city"],
            "patient_state": ["state"],
            "patient_zip": ["zip", "postal"],
            "patient_phone": ["phone", "telephone"],
            "insurance_member_id": ["member id", "member", "subscriber"],
            "provider_name": ["provider", "physician", "doctor"],
            "provider_npi": ["npi"]
        }
        
        for canonical_field, patterns in field_patterns.items():
            for element in text_elements:
                text_lower = element["text"].lower()
                
                # Check if any pattern matches
                for pattern in patterns:
                    if pattern in text_lower and len(text_lower) < 50:  # Avoid long text
                        # Position fill area to the right of the label
                        fill_x = element["x"] + 150  # Offset to the right
                        fill_y = element["y"]
                        positions[canonical_field] = (fill_x, fill_y)
                        logger.info(f"📍 Found position for {canonical_field}: {text_lower} -> ({fill_x:.0f}, {fill_y:.0f})")
                        break
                
                if canonical_field in positions:
                    break
        
        return positions
    
    def fill_form_with_coordinates(self, pdf_path: Path, patient_data: Dict, output_path: Path) -> Dict:
        """Fill form by placing text at specific coordinates"""
        logger.info(f"🎯 SIMPLE COORDINATE FILLING: {pdf_path}")
        
        # Extract patient values
        patient_values = self._extract_patient_values(patient_data)
        logger.info(f"📋 Patient values: {list(patient_values.keys())}")
        
        # Try to find positions automatically first
        auto_positions = self.find_field_positions(pdf_path)
        
        # Fall back to manual coordinates if auto-detection fails
        manual_positions = self.get_form_coordinates(pdf_path)
        
        # Use auto-detected positions where available, manual otherwise
        coordinates = {**manual_positions, **auto_positions}
        
        logger.info(f"📍 Using {len(coordinates)} coordinate mappings ({len(auto_positions)} auto, {len(manual_positions)} manual)")
        
        # Open PDF for modification
        doc = fitz.open(str(pdf_path))
        page = doc[0]
        
        filled_count = 0
        filled_details = []
        
        # Place text at each coordinate
        for field_name, (x, y) in coordinates.items():
            if field_name in patient_values:
                value = str(patient_values[field_name]).strip()
                if value:
                    try:
                        # Insert text at coordinate
                        point = fitz.Point(x, y)
                        page.insert_text(
                            point,
                            value,
                            fontsize=9,
                            color=(0, 0, 1),  # Blue color to make it visible
                            fontname="helv"
                        )
                        
                        filled_count += 1
                        filled_details.append({
                            "field": field_name,
                            "value": value,
                            "position": (x, y)
                        })
                        
                        logger.info(f"✅ PLACED: {field_name} = '{value}' at ({x:.0f}, {y:.0f})")
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to place {field_name}: {e}")
        
        # Save the filled PDF
        doc.save(str(output_path))
        doc.close()
        
        logger.info(f"📄 Saved filled form with {filled_count} fields: {output_path}")
        
        return {
            "success": True,
            "method": "coordinate_placement",
            "filled_count": filled_count,
            "available_coordinates": len(coordinates),
            "patient_values": len(patient_values),
            "output_path": str(output_path),
            "filled_details": filled_details
        }
    
    def _extract_patient_values(self, patient_data: Dict) -> Dict[str, str]:
        """Extract patient values from structured data"""
        values = {}
        
        if 'tier_1_mandatory_fields' in patient_data:
            tier1 = patient_data['tier_1_mandatory_fields']
            
            # Patient demographics
            if 'patient_demographics' in tier1:
                demo = tier1['patient_demographics']
                
                # Names
                full_name = demo.get('full_name', {}).get('value', '')
                if full_name:
                    name_parts = full_name.split()
                    values['patient_first_name'] = name_parts[0] if name_parts else ''
                    values['patient_last_name'] = name_parts[-1] if len(name_parts) > 1 else ''
                
                # DOB
                values['patient_dob'] = demo.get('date_of_birth', {}).get('value', '')
                
                # Address
                address = demo.get('address', {}).get('value', '')
                if address:
                    import re
                    addr_match = re.match(r'(.+),\s*(.+),\s*([A-Z]{2})[- ]?(\d{5})', address)
                    if addr_match:
                        values['patient_address'] = addr_match.group(1).strip()
                        values['patient_city'] = addr_match.group(2).strip()
                        values['patient_state'] = addr_match.group(3).strip()
                        values['patient_zip'] = addr_match.group(4).strip()
                    else:
                        values['patient_address'] = address
                
                # Phone
                phones = demo.get('phone_numbers', {}).get('value', [])
                if phones:
                    values['patient_phone'] = phones[0]
            
            # Insurance
            if 'insurance_information' in tier1:
                ins = tier1['insurance_information'].get('primary_insurance', {})
                values['insurance_member_id'] = ins.get('member_id', {}).get('value', '')
            
            # Provider
            if 'prescriber_information' in tier1:
                prov = tier1['prescriber_information']
                values['provider_name'] = prov.get('ordering_physician', {}).get('value', '')
                values['provider_npi'] = prov.get('npi', {}).get('value', '')
        
        # Clean values
        clean_values = {k: v for k, v in values.items() if v and str(v).strip()}
        return clean_values
    
    def create_visual_output(self, filled_pdf_path: Path, image_path: str):
        """Convert filled PDF to image for visual inspection"""
        logger.info(f"📸 Creating visual output: {image_path}")
        
        doc = fitz.open(str(filled_pdf_path))
        page = doc[0]
        
        # Convert to high-res image
        mat = fitz.Matrix(2, 2)  # 2x zoom for clarity
        pix = page.get_pixmap(matrix=mat)
        pix.save(image_path)
        
        doc.close()
        
        logger.info(f"📷 Visual output saved: {image_path}")

def test_simple_coordinate_system():
    """Test simple coordinate-based form filling"""
    print("🚀 SIMPLE COORDINATE-BASED FORM FILLING TEST")
    print("=" * 70)
    print("✅ Manual + automatic coordinate detection")
    print("✅ Direct text placement at coordinates")
    print("✅ Blue text for visibility")
    print("✅ Visual output for verification")
    print("-" * 70)
    
    filler = SimpleCoordinateFiller()
    
    patients = [
        ("Abdullah", "Input Data/Extracted_ground_truth/abdullah_structured.json", "Input Data/Adbulla/PA.pdf"),
        ("Akshay", "Input Data/Extracted_ground_truth/akshay_structured.json", "Input Data/Akshay/pa.pdf")
    ]
    
    results = []
    
    for patient_name, data_path, pdf_path in patients:
        print(f"\n👤 {patient_name.upper()}")
        print("-" * 40)
        
        if not Path(data_path).exists() or not Path(pdf_path).exists():
            print(f"❌ Files not found for {patient_name}")
            continue
        
        # Load patient data
        with open(data_path, 'r') as f:
            patient_data = json.load(f)
        
        # Fill form using coordinates
        output_path = Path(f"{patient_name.lower()}_SIMPLE_COORDINATE.pdf")
        result = filler.fill_form_with_coordinates(Path(pdf_path), patient_data, output_path)
        
        print(f"Success: {result['success']}")
        print(f"Method: {result['method']}")
        print(f"Available Coordinates: {result['available_coordinates']}")
        print(f"Patient Values: {result['patient_values']}")
        print(f"Fields Filled: {result['filled_count']}")
        print(f"Output: {result['output_path']}")
        
        print(f"\n📍 Filled Field Details:")
        for detail in result['filled_details']:
            x, y = detail['position']
            print(f"  {detail['field']:20s} = '{detail['value']}' at ({x:.0f}, {y:.0f})")
        
        # Create visual output
        image_path = f"{patient_name.lower()}_filled_visual.png"
        filler.create_visual_output(output_path, image_path)
        print(f"📷 Visual output: {image_path}")
        
        results.append(result)
    
    # Summary
    total_filled = sum(r.get('filled_count', 0) for r in results)
    
    print(f"\n🎯 SIMPLE COORDINATE RESULTS:")
    print(f"Patients Processed: {len(results)}")
    print(f"Total Fields Placed: {total_filled}")
    print(f"🎉 TEXT PLACEMENT COMPLETE!")
    print("\n📋 NEXT STEPS:")
    print("1. Check the visual outputs (*_filled_visual.png)")
    print("2. Verify text appears in correct locations")
    print("3. Adjust coordinates if needed")
    print("4. This proves the coordinate approach works!")
    
    return results

if __name__ == "__main__":
    test_simple_coordinate_system()