"""
CORRECT FIELD INSPECTOR
Analyzes actual form structure to find correct field mappings
"""

import json
from PyPDF2 import <PERSON>df<PERSON>eader
import fitz
from pathlib import Path

def inspect_form_structure(pdf_path: Path):
    """Inspect form structure to understand field layout"""
    print(f"🔍 INSPECTING: {pdf_path}")
    print("=" * 60)
    
    # Get form fields
    reader = PdfReader(str(pdf_path))
    form_fields = reader.get_fields() or {}
    
    print(f"Total form fields: {len(form_fields)}")
    
    # Get text layout using PyMuPDF
    doc = fitz.open(str(pdf_path))
    page = doc[0]
    text_dict = page.get_text("dict")
    
    # Extract text with positions
    text_tokens = []
    for block in text_dict.get("blocks", []):
        if "lines" in block:
            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"].strip()
                    if len(text) > 2:  # Skip single characters
                        bbox = span["bbox"]
                        text_tokens.append({
                            "text": text,
                            "bbox": bbox,
                            "x": bbox[0],
                            "y": bbox[1]
                        })
    
    # Look for field labels that suggest patient info
    patient_labels = [
        "first name", "last name", "date of birth", "dob", 
        "address", "city", "state", "zip", "phone", "member"
    ]
    
    print("\n📋 FORM TEXT ANALYSIS:")
    print("-" * 40)
    
    relevant_text = []
    for token in text_tokens:
        text_lower = token["text"].lower()
        if any(label in text_lower for label in patient_labels):
            relevant_text.append(token)
    
    # Sort by vertical position (y coordinate)
    relevant_text.sort(key=lambda x: x["y"])
    
    for token in relevant_text[:20]:  # Show first 20 relevant labels
        print(f"'{token['text']:30s}' at ({token['x']:4.0f}, {token['y']:4.0f})")
    
    print(f"\n📊 FIELD ANALYSIS:")
    print("-" * 40)
    
    # Look at fields that might be text fields
    text_fields = []
    for widget_id, field_obj in form_fields.items():
        field_type = field_obj.get("/FT", "")
        if "/Tx" in str(field_type):  # Text field
            rect = field_obj.get("/Rect", [0, 0, 0, 0])
            field_name = field_obj.get("/T", widget_id)
            text_fields.append({
                "id": widget_id,
                "name": field_name,
                "rect": rect,
                "y": rect[1] if rect else 0
            })
    
    # Sort by y position
    text_fields.sort(key=lambda x: -x["y"])  # Top to bottom
    
    print("Top 30 text fields (likely patient info fields):")
    for field in text_fields[:30]:
        y_pos = field["y"]
        print(f"Field {field['id']:15s} ('{field['name']}') at y={y_pos:4.0f}")
    
    doc.close()
    
    return form_fields, text_fields

def find_filled_fields(pdf_path: Path):
    """Check what fields actually got filled in processed PDFs"""
    print(f"\n🔍 CHECKING FILLED FIELDS: {pdf_path}")
    print("=" * 60)
    
    if not pdf_path.exists():
        print("File not found")
        return
    
    reader = PdfReader(str(pdf_path))
    
    try:
        # Try to get filled form values
        form_text_fields = reader.get_form_text_fields()
        if form_text_fields:
            print("Filled fields:")
            for field_id, value in form_text_fields.items():
                if value and str(value).strip():
                    print(f"  {field_id:15s} = {value}")
        else:
            print("No filled form text fields found")
    except Exception as e:
        print(f"Could not extract form values: {e}")

if __name__ == "__main__":
    # Inspect original forms
    print("🚀 FORM STRUCTURE ANALYSIS")
    print("=" * 70)
    
    # Abdullah's form
    inspect_form_structure(Path("Input Data/Adbulla/PA.pdf"))
    
    print("\n" + "=" * 70)
    
    # Akshay's form  
    inspect_form_structure(Path("Input Data/Akshay/pa.pdf"))
    
    # Check what got filled
    print("\n" + "=" * 70)
    print("CHECKING FILLED OUTPUTS")
    
    find_filled_fields(Path("abdullah_FINAL_PRODUCTION.pdf"))
    find_filled_fields(Path("akshay_FINAL_PRODUCTION.pdf"))
    find_filled_fields(Path("abdullah_HYBRID_ENHANCED.pdf"))
    find_filled_fields(Path("akshay_HYBRID_ENHANCED.pdf"))