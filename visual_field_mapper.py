"""
VISUAL FIELD MAPPER
Analyzes form visually to find correct field coordinates
"""

import fitz
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VisualFieldMapper:
    """Map form fields visually to find correct coordinates"""
    
    def analyze_form_structure(self, pdf_path: Path) -> Dict:
        """Analyze form structure to identify field locations"""
        logger.info(f"🔍 VISUAL FORM ANALYSIS: {pdf_path}")
        
        doc = fitz.open(str(pdf_path))
        page = doc[0]
        
        # Get page dimensions
        page_rect = page.rect
        page_width = page_rect.width
        page_height = page_rect.height
        
        logger.info(f"📏 Page dimensions: {page_width:.0f} x {page_height:.0f}")
        
        # Extract all text with positions
        text_dict = page.get_text("dict")
        all_text = []
        
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            all_text.append({
                                "text": text,
                                "x": bbox[0],
                                "y": bbox[1],
                                "x2": bbox[2],
                                "y2": bbox[3],
                                "width": bbox[2] - bbox[0],
                                "height": bbox[3] - bbox[1],
                                "font_size": span.get("size", 10),
                                "flags": span.get("flags", 0)
                            })
        
        # Sort by position (top to bottom, left to right)
        all_text.sort(key=lambda x: (x["y"], x["x"]))
        
        # Identify form sections and fields
        field_mappings = self._identify_field_locations(all_text, pdf_path.name)
        
        # Create visual debug image
        self._create_field_visualization(page, field_mappings, f"{pdf_path.stem}_field_map.png")
        
        doc.close()
        
        return {
            "page_dimensions": (page_width, page_height),
            "text_elements": all_text,
            "field_mappings": field_mappings
        }
    
    def _identify_field_locations(self, text_elements: List[Dict], pdf_name: str) -> Dict:
        """Identify field locations based on text patterns"""
        logger.info("📍 Identifying field locations...")
        
        field_mappings = {}
        
        # Different patterns for different forms
        if "abdullah" in pdf_name.lower() or "adbulla" in pdf_name.lower():
            # Medicare/Aetna form patterns
            field_mappings = self._find_medicare_fields(text_elements)
        elif "akshay" in pdf_name.lower():
            # Aetna form patterns  
            field_mappings = self._find_aetna_fields(text_elements)
        else:
            # Generic patterns
            field_mappings = self._find_generic_fields(text_elements)
        
        return field_mappings
    
    def _find_medicare_fields(self, text_elements: List[Dict]) -> Dict:
        """Find fields in Medicare-style forms"""
        field_mappings = {}
        
        # Look for patient information section
        for i, element in enumerate(text_elements):
            text_lower = element["text"].lower()
            
            # Patient section indicators
            if "patient" in text_lower and "information" in text_lower:
                # Look for fields below this section
                section_y = element["y"]
                
                # Search for fields in the next ~200 pixels
                for j in range(i+1, min(i+50, len(text_elements))):
                    nearby = text_elements[j]
                    if nearby["y"] - section_y > 200:
                        break
                    
                    nearby_text = nearby["text"].lower()
                    
                    # First/Last name fields (often on same line)
                    if "name" in nearby_text:
                        if "first" in nearby_text:
                            field_mappings["patient_first_name"] = self._find_fill_position(nearby, text_elements, "right")
                        elif "last" in nearby_text:
                            field_mappings["patient_last_name"] = self._find_fill_position(nearby, text_elements, "right")
                        elif "patient" in nearby_text:
                            # Generic name field - assume first name is left, last name is right
                            pos = self._find_fill_position(nearby, text_elements, "right")
                            field_mappings["patient_first_name"] = pos
                            field_mappings["patient_last_name"] = (pos[0] + 200, pos[1])
                    
                    # Date of birth
                    elif "birth" in nearby_text or "dob" in nearby_text:
                        field_mappings["patient_dob"] = self._find_fill_position(nearby, text_elements, "right")
                    
                    # Address
                    elif "address" in nearby_text and "email" not in nearby_text:
                        field_mappings["patient_address"] = self._find_fill_position(nearby, text_elements, "right")
                    
                    # City/State/ZIP (often on same line)
                    elif "city" in nearby_text:
                        city_pos = self._find_fill_position(nearby, text_elements, "right")
                        field_mappings["patient_city"] = city_pos
                        # State and ZIP are usually to the right
                        field_mappings["patient_state"] = (city_pos[0] + 150, city_pos[1])
                        field_mappings["patient_zip"] = (city_pos[0] + 250, city_pos[1])
                    
                    # Phone
                    elif "phone" in nearby_text or "telephone" in nearby_text:
                        field_mappings["patient_phone"] = self._find_fill_position(nearby, text_elements, "right")
            
            # Insurance section
            elif "insurance" in text_lower or "member" in text_lower:
                if "id" in text_lower or "number" in text_lower:
                    field_mappings["insurance_member_id"] = self._find_fill_position(element, text_elements, "right")
            
            # Provider section
            elif "provider" in text_lower or "physician" in text_lower or "prescriber" in text_lower:
                section_y = element["y"]
                
                for j in range(i+1, min(i+20, len(text_elements))):
                    nearby = text_elements[j]
                    if nearby["y"] - section_y > 150:
                        break
                    
                    nearby_text = nearby["text"].lower()
                    
                    if "name" in nearby_text:
                        field_mappings["provider_name"] = self._find_fill_position(nearby, text_elements, "right")
                    elif "npi" in nearby_text:
                        field_mappings["provider_npi"] = self._find_fill_position(nearby, text_elements, "right")
        
        logger.info(f"📋 Found {len(field_mappings)} Medicare form fields")
        return field_mappings
    
    def _find_aetna_fields(self, text_elements: List[Dict]) -> Dict:
        """Find fields in Aetna-style forms"""
        field_mappings = {}
        
        # Aetna forms often have a different layout
        # Look for specific patterns
        for i, element in enumerate(text_elements):
            text = element["text"]
            text_lower = text.lower()
            
            # Member information section
            if "member" in text_lower and "information" in text_lower:
                section_y = element["y"]
                field_mappings["insurance_member_id"] = (element["x"] + 150, section_y + 25)
            
            # Patient name (often just "Name:")
            elif text == "Name:" or "patient name" in text_lower:
                pos = self._find_fill_position(element, text_elements, "right")
                field_mappings["patient_first_name"] = pos
                field_mappings["patient_last_name"] = (pos[0] + 150, pos[1])
            
            # DOB
            elif "date of birth" in text_lower or text == "DOB:":
                field_mappings["patient_dob"] = self._find_fill_position(element, text_elements, "right")
            
            # Address fields
            elif text == "Address:" or "patient address" in text_lower:
                field_mappings["patient_address"] = self._find_fill_position(element, text_elements, "right")
            
            elif text == "City:" or "city" in text_lower:
                field_mappings["patient_city"] = self._find_fill_position(element, text_elements, "right")
            
            elif text == "State:" or ("state" in text_lower and len(text) < 20):
                field_mappings["patient_state"] = self._find_fill_position(element, text_elements, "right")
            
            elif text == "ZIP:" or "zip" in text_lower:
                field_mappings["patient_zip"] = self._find_fill_position(element, text_elements, "right")
            
            # Provider fields
            elif "prescriber" in text_lower or "provider" in text_lower:
                if "npi" in text_lower:
                    field_mappings["provider_npi"] = self._find_fill_position(element, text_elements, "right")
                elif "name" in text_lower:
                    field_mappings["provider_name"] = self._find_fill_position(element, text_elements, "right")
        
        logger.info(f"📋 Found {len(field_mappings)} Aetna form fields")
        return field_mappings
    
    def _find_generic_fields(self, text_elements: List[Dict]) -> Dict:
        """Find fields using generic patterns"""
        field_mappings = {}
        
        for element in text_elements:
            text_lower = element["text"].lower()
            
            # Skip very long text (paragraphs)
            if len(element["text"]) > 50:
                continue
            
            # Patient fields
            if "first name" in text_lower:
                field_mappings["patient_first_name"] = self._find_fill_position(element, text_elements, "right")
            elif "last name" in text_lower:
                field_mappings["patient_last_name"] = self._find_fill_position(element, text_elements, "right")
            elif ("date" in text_lower and "birth" in text_lower) or "dob" in text_lower:
                field_mappings["patient_dob"] = self._find_fill_position(element, text_elements, "right")
            elif "address" in text_lower and "email" not in text_lower:
                field_mappings["patient_address"] = self._find_fill_position(element, text_elements, "right")
            elif "city" in text_lower and len(text_lower) < 20:
                field_mappings["patient_city"] = self._find_fill_position(element, text_elements, "right")
            elif "state" in text_lower and len(text_lower) < 20:
                field_mappings["patient_state"] = self._find_fill_position(element, text_elements, "right")
            elif "zip" in text_lower or "postal" in text_lower:
                field_mappings["patient_zip"] = self._find_fill_position(element, text_elements, "right")
            elif "phone" in text_lower or "telephone" in text_lower:
                field_mappings["patient_phone"] = self._find_fill_position(element, text_elements, "right")
            
            # Insurance fields
            elif "member" in text_lower and ("id" in text_lower or "number" in text_lower):
                field_mappings["insurance_member_id"] = self._find_fill_position(element, text_elements, "right")
            
            # Provider fields
            elif "npi" in text_lower:
                field_mappings["provider_npi"] = self._find_fill_position(element, text_elements, "right")
            elif ("provider" in text_lower or "physician" in text_lower) and "name" in text_lower:
                field_mappings["provider_name"] = self._find_fill_position(element, text_elements, "right")
        
        logger.info(f"📋 Found {len(field_mappings)} generic form fields")
        return field_mappings
    
    def _find_fill_position(self, label_element: Dict, all_text: List[Dict], direction: str = "right") -> Tuple[float, float]:
        """Find the position where text should be placed relative to a label"""
        
        if direction == "right":
            # Look for blank space to the right of the label
            x_offset = label_element["width"] + 10
            
            # Check if there's already text to the right
            label_end_x = label_element["x2"]
            label_y = label_element["y"]
            
            # Find the next text element on the same line
            next_text_x = label_element["x"] + 500  # Default far right
            
            for other in all_text:
                if (abs(other["y"] - label_y) < 5 and  # Same line
                    other["x"] > label_end_x and  # To the right
                    other["x"] < next_text_x):  # Closer than previous
                    next_text_x = other["x"]
            
            # Place text between label and next element
            fill_x = label_end_x + 10
            if next_text_x - label_end_x > 50:  # Enough space
                fill_x = label_end_x + 20
            
            return (fill_x, label_y)
        
        elif direction == "below":
            # Place text below the label
            return (label_element["x"], label_element["y"] + 20)
        
        else:
            # Default to right
            return (label_element["x2"] + 10, label_element["y"])
    
    def _create_field_visualization(self, page: fitz.Page, field_mappings: Dict, output_path: str):
        """Create a visual map showing where fields will be placed"""
        logger.info(f"🎨 Creating field visualization: {output_path}")
        
        # Draw rectangles around identified field positions
        for field_name, (x, y) in field_mappings.items():
            # Draw a small rectangle where text will be placed
            rect = fitz.Rect(x, y-5, x+150, y+15)
            page.draw_rect(rect, color=(0, 0, 1), width=1)
            
            # Add field name label
            page.insert_text(fitz.Point(x, y-7), field_name, fontsize=8, color=(1, 0, 0))
        
        # Save as image
        mat = fitz.Matrix(2, 2)  # 2x zoom
        pix = page.get_pixmap(matrix=mat)
        pix.save(output_path)
        
        logger.info(f"📷 Field map saved: {output_path}")
    
    def create_coordinate_template(self, pdf_path: Path) -> Dict:
        """Create a coordinate template for a form"""
        analysis = self.analyze_form_structure(pdf_path)
        
        template = {
            "form_name": pdf_path.name,
            "page_dimensions": analysis["page_dimensions"],
            "field_coordinates": {}
        }
        
        # Convert field mappings to template format
        for field_name, (x, y) in analysis["field_mappings"].items():
            template["field_coordinates"][field_name] = {
                "x": round(x, 1),
                "y": round(y, 1),
                "confidence": "auto_detected"
            }
        
        # Save template
        template_path = f"{pdf_path.stem}_coordinate_template.json"
        with open(template_path, 'w') as f:
            json.dump(template, f, indent=2)
        
        logger.info(f"💾 Saved coordinate template: {template_path}")
        
        return template

def test_visual_field_mapping():
    """Test visual field mapping on forms"""
    print("🚀 VISUAL FIELD MAPPING TEST")
    print("=" * 70)
    print("✅ Analyzing form structure")
    print("✅ Identifying field locations")
    print("✅ Creating visual field maps")
    print("✅ Generating coordinate templates")
    print("-" * 70)
    
    mapper = VisualFieldMapper()
    
    forms = [
        ("Abdullah", "Input Data/Adbulla/PA.pdf"),
        ("Akshay", "Input Data/Akshay/pa.pdf")
    ]
    
    templates = []
    
    for patient_name, pdf_path in forms:
        print(f"\n👤 {patient_name.upper()} FORM ANALYSIS")
        print("-" * 40)
        
        if not Path(pdf_path).exists():
            print(f"❌ Form not found: {pdf_path}")
            continue
        
        # Create coordinate template
        template = mapper.create_coordinate_template(Path(pdf_path))
        templates.append(template)
        
        print(f"Form: {template['form_name']}")
        print(f"Page size: {template['page_dimensions'][0]:.0f} x {template['page_dimensions'][1]:.0f}")
        print(f"Fields found: {len(template['field_coordinates'])}")
        
        print("\n📍 Field Coordinates:")
        for field_name, coord_info in template['field_coordinates'].items():
            print(f"  {field_name:20s} -> ({coord_info['x']:6.1f}, {coord_info['y']:6.1f})")
    
    print("\n🎯 VISUAL MAPPING COMPLETE!")
    print(f"Generated {len(templates)} coordinate templates")
    print("\n📋 NEXT STEPS:")
    print("1. Review field map images (*_field_map.png)")
    print("2. Verify field positions are correct")
    print("3. Use templates for accurate form filling")
    
    return templates

if __name__ == "__main__":
    test_visual_field_mapping()