"""
Corrected PA Form Filler - Fills all fields with correct data in correct locations
"""

import json
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

from PyPDF2 import PdfReader, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject


class CorrectedPAFiller:
    """Fill PA form with all data in correct fields"""
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.data = json.load(open(json_path))
        self.reader = PdfReader(str(pdf_path))
        
    def extract_all_data(self) -> Dict[str, Any]:
        """Extract ALL data from JSON accurately"""
        values = {}
        
        # Patient Demographics
        demo = self.data['tier_1_mandatory_fields']['patient_demographics']
        
        # Name
        full_name = demo['full_name']['value']  # "Akshay H. chaudhari"
        name_parts = full_name.split()
        values['first_name'] = name_parts[0]  # "Akshay"
        if len(name_parts) > 2:
            values['middle_initial'] = name_parts[1]  # "H."
            values['last_name'] = name_parts[2]  # "chaudhari"
        else:
            values['last_name'] = name_parts[-1] if len(name_parts) > 1 else ''
            
        # DOB
        values['dob'] = demo['date_of_birth']['value']  # "1987-02-17"
        
        # Address
        full_address = demo['address']['value']  # "1460 El Camino Real, Arlington, VA-22407"
        addr_parts = full_address.split(',')
        values['address'] = addr_parts[0].strip()  # "1460 El Camino Real"
        
        # Parse city, state, zip correctly
        if len(addr_parts) >= 2:
            values['city'] = addr_parts[1].strip()  # "Arlington"
            
            if len(addr_parts) >= 3:
                state_zip = addr_parts[2].strip()  # "VA-22407"
                if '-' in state_zip:
                    state_parts = state_zip.split('-')
                    values['state'] = state_parts[0].strip()  # "VA"
                    values['zip'] = state_parts[1].strip()  # "22407"
                else:
                    # Try space separation
                    parts = state_zip.split()
                    if len(parts) >= 2:
                        values['state'] = parts[0]
                        values['zip'] = parts[1]
                        
        # Phone
        phone = demo['phone_numbers']['value'][0]  # "************"
        values['home_phone'] = phone
        values['work_phone'] = phone
        values['cell_phone'] = phone
        
        # Email - not in data but required
        values['email'] = ''
        
        # Physical measurements
        weight_full = demo['physical_measurements']['weight']['value']  # "190 lbs, 86.18 kg"
        weight_parts = weight_full.split()
        values['weight_lbs'] = weight_parts[0]  # "190"
        # Extract kg value
        if 'kg' in weight_full:
            kg_index = weight_full.find('kg')
            kg_value = weight_full[:kg_index].split()[-1]
            values['weight_kgs'] = kg_value  # "86.18"
            
        height_full = demo['physical_measurements']['height']['value']  # "73 in"
        values['height_inches'] = height_full.split()[0]  # "73"
        
        # Calculate height in cm (73 inches * 2.54)
        height_cm = int(values['height_inches']) * 2.54
        values['height_cms'] = str(int(height_cm))  # "185"
        
        # Allergies from treatment history
        if 'tier_2_clinical_justification' in self.data:
            clinical = self.data['tier_2_clinical_justification']
            if 'treatment_history' in clinical and 'contraindications' in clinical['treatment_history']:
                allergies = clinical['treatment_history']['contraindications']['value']
                values['allergies'] = ', '.join(allergies) if allergies else 'None'
            else:
                values['allergies'] = 'None'
                
        # Insurance Information
        insurance = self.data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
        values['member_id'] = insurance['member_id']['value']  # "14866-38657882"
        values['group_number'] = insurance.get('group_number', {}).get('value', '')
        values['insured_name'] = full_name  # Same as patient
        values['payer_name'] = insurance['payer_name']['value']  # "Aetna Better Health of Virginia"
        
        # Prescriber Information
        prescriber = self.data['tier_1_mandatory_fields']['prescriber_information']
        
        # Parse prescriber name
        prescriber_full = prescriber['physician_name']['value']  # "Timothy Adam, MD"
        prescriber_parts = prescriber_full.replace(', MD', '').split()
        values['prescriber_first_name'] = prescriber_parts[0] if prescriber_parts else ''  # "Timothy"
        values['prescriber_last_name'] = prescriber_parts[1] if len(prescriber_parts) > 1 else ''  # "Adam"
        
        values['prescriber_npi'] = prescriber['npi_number']['value']  # "**********"
        values['facility_name'] = prescriber['facility_name']['value']  # "Extraodinary Gastroenterology"
        values['facility_address'] = prescriber['facility_address']['value']  # "2755 College Ave Ste. 100, Leesburg VA 20176"
        
        # Parse facility address
        fac_addr_parts = values['facility_address'].split(',')
        if fac_addr_parts:
            values['facility_street'] = fac_addr_parts[0].strip()
            if len(fac_addr_parts) > 1:
                city_state_zip = fac_addr_parts[1].strip()
                # Extract facility city, state, zip
                parts = city_state_zip.split()
                if len(parts) >= 3:
                    values['facility_city'] = parts[0]  # "Leesburg"
                    values['facility_state'] = parts[1]  # "VA"
                    values['facility_zip'] = parts[2]  # "20176"
        
        # Phone/Fax
        if 'phone_fax' in prescriber:
            phone_fax = prescriber['phone_fax']['value']
            values['facility_phone'] = phone_fax.get('phone', '')  # "************"
            values['facility_fax'] = phone_fax.get('fax', '')  # "************"
            
        # Provider speciality
        values['specialty'] = 'Gastroenterologist'
        
        # Clinical Information
        if 'primary_diagnosis' in clinical:
            values['icd_code'] = clinical['primary_diagnosis']['icd_code']['value']  # "K50.111"
            values['diagnosis'] = clinical['primary_diagnosis']['diagnosis_description']['value']
            
        # Medication Information - check both locations
        med_data = None
        if 'requested_medication' in self.data:
            med_data = self.data['requested_medication']
        elif 'tier_1_mandatory_fields' in self.data and 'requested_medication' in self.data['tier_1_mandatory_fields']:
            med_data = self.data['tier_1_mandatory_fields']['requested_medication']
            
        if med_data:
            values['medication_name'] = med_data['drug_name']['value']  # "Skyrizi"
            values['medication_dosage'] = med_data['dosage']['value']
            values['medication_frequency'] = med_data['frequency']['value']
            values['medication_route'] = med_data['route']['value']  # "IV and SQ"
            
        # Additional clinical data
        if 'labs_imaging' in clinical:
            labs = clinical['labs_imaging']
            if 'cbc' in labs:
                values['hemoglobin'] = labs['cbc']['hemoglobin']['value']
                values['wbc'] = labs['cbc']['wbc']['value']
                values['platelets'] = labs['cbc']['platelets']['value']
                
        return values
        
    def create_field_mapping(self) -> Dict[str, str]:
        """Create comprehensive field mapping"""
        return {
            # Header
            'Request by T': 'facility_name',
            'Phone T': 'facility_phone',
            'Fax T': 'facility_fax',
            
            # Section A - Patient Information
            'T14': 'first_name',      # First Name
            'T15': 'last_name',       # Last Name  
            'T16': 'dob',             # DOB
            'T17': 'address',         # Address
            'T19': 'city',            # City
            'T20': 'state',           # State
            'T21': 'zip',             # ZIP
            'T21B': 'home_phone',     # Home Phone
            'T21C': 'work_phone',     # Work Phone
            'T21D': 'cell_phone',     # Cell Phone
            'T21E': 'email',          # Email
            'T21F': 'weight_lbs',     # Weight in lbs
            'Insurance Info T.1': 'weight_kgs',    # Weight in kgs
            'Insurance Info T.2': 'height_inches', # Height in inches
            'Insurance Info T.6': 'height_cms',    # Height in cms
            'Insurance Info T.7': 'allergies',     # Allergies
            
            # Section B - Insurance Information  
            'T11': 'member_id',       # Aetna Member ID
            'T12': 'group_number',    # Group #
            'T18': 'insured_name',    # Insured
            'T13': 'insured_name',    # Insured (alt field)
            
            # Section C - Prescriber Information
            'C.Prescriber Info T.1': 'prescriber_first_name',
            'C.Prescriber Info T.2': 'prescriber_last_name',
            'C.Prescriber Info T.3': 'facility_street',
            'C.Prescriber Info T.4': 'facility_city',
            'C.Prescriber Info T.5': 'facility_state',
            'C.Prescriber Info T.6': 'facility_zip',
            'C.Prescriber Info T.7': 'facility_phone',
            'C.Prescriber Info T.8': 'facility_fax',
            'C.Prescriber Info T.11': 'prescriber_npi',
            
            # Diagnosis fields
            'Primary ICD Code': 'icd_code',
            'Diagnosis Text': 'diagnosis',
            'Diagnosis T.2': 'diagnosis',
            
            # Additional fields from form inspection
            'Insurance Info T.3': 'medication_name',
            'Insurance Info T.8': 'medication_dosage'
        }
        
    def fill_form(self, output_path: Path):
        """Fill form with all data correctly mapped"""
        print("📊 Extracting all data from JSON...")
        all_data = self.extract_all_data()
        
        print("🗺️ Creating comprehensive field mapping...")
        field_mapping = self.create_field_mapping()
        
        # Create writer
        writer = PdfWriter()
        writer.clone_reader_document_root(self.reader)
        writer.append_pages_from_reader(self.reader)
        
        # Ensure AcroForm
        if self.reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): self.reader.trailer['/Root']['/AcroForm']
            })
            
        # Fill all fields
        form_values = {}
        filled_count = 0
        missing_data = []
        
        print("\n✅ Filling form fields:")
        print("-" * 80)
        
        # First, fill known mappings
        for pdf_field, data_key in field_mapping.items():
            if data_key in all_data and all_data[data_key]:
                value = str(all_data[data_key])
                form_values[pdf_field] = value
                print(f"✓ {pdf_field:<25} → {data_key:<25} = {value}")
                filled_count += 1
            else:
                missing_data.append(f"{pdf_field} (needs: {data_key})")
                
        # Get all form fields to check what we missed
        all_form_fields = self.reader.get_form_text_fields()
        
        # Try to fill any remaining empty fields
        print("\n🔍 Checking for additional fields to fill...")
        for field_name in all_form_fields:
            if field_name not in form_values:
                # Try to match by name similarity
                field_lower = field_name.lower()
                
                # Special cases and additional mappings
                if 'prescriber' in field_lower and 'first' in field_lower:
                    if 'prescriber_first_name' in all_data:
                        form_values[field_name] = all_data['prescriber_first_name']
                        print(f"✓ {field_name} → prescriber_first_name = {all_data['prescriber_first_name']}")
                        filled_count += 1
                elif 'prescriber' in field_lower and 'last' in field_lower:
                    if 'prescriber_last_name' in all_data:
                        form_values[field_name] = all_data['prescriber_last_name']
                        print(f"✓ {field_name} → prescriber_last_name = {all_data['prescriber_last_name']}")
                        filled_count += 1
                        
        # Update form
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
            
        # Force appearance
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
            
        # Generate comprehensive report
        print("\n" + "=" * 80)
        print("📋 FORM FILLING SUMMARY")
        print("=" * 80)
        print(f"Total form fields: {len(all_form_fields)}")
        print(f"Fields filled: {filled_count}")
        print(f"Fields empty: {len(all_form_fields) - filled_count}")
        
        print("\n📄 PATIENT INFORMATION FILLED:")
        print(f"  Name: {all_data['first_name']} {all_data.get('middle_initial', '')} {all_data['last_name']}")
        print(f"  DOB: {all_data['dob']}")
        print(f"  Address: {all_data['address']}, {all_data['city']}, {all_data['state']} {all_data['zip']}")
        print(f"  Phone: {all_data['home_phone']}")
        print(f"  Weight: {all_data['weight_lbs']} lbs / {all_data.get('weight_kgs', 'N/A')} kg")
        print(f"  Height: {all_data['height_inches']} inches / {all_data.get('height_cms', 'N/A')} cm")
        print(f"  Allergies: {all_data.get('allergies', 'None')}")
        
        print("\n🏥 INSURANCE INFORMATION FILLED:")
        print(f"  Member ID: {all_data['member_id']}")
        print(f"  Payer: {all_data['payer_name']}")
        print(f"  Insured: {all_data['insured_name']}")
        
        print("\n👨‍⚕️ PRESCRIBER INFORMATION FILLED:")
        print(f"  Provider: {all_data.get('prescriber_first_name', '')} {all_data.get('prescriber_last_name', '')}")
        print(f"  NPI: {all_data['prescriber_npi']}")
        print(f"  Facility: {all_data['facility_name']}")
        print(f"  Address: {all_data['facility_address']}")
        print(f"  Phone: {all_data['facility_phone']}")
        print(f"  Fax: {all_data['facility_fax']}")
        
        print("\n🩺 CLINICAL INFORMATION FILLED:")
        print(f"  ICD Code: {all_data.get('icd_code', 'N/A')}")
        print(f"  Diagnosis: {all_data.get('diagnosis', 'N/A')}")
        print(f"  Medication: {all_data.get('medication_name', 'N/A')}")
        print(f"  Dosage: {all_data.get('medication_dosage', 'N/A')}")
        
        if missing_data:
            print(f"\n⚠️ FIELDS THAT COULD NOT BE FILLED ({len(missing_data)}):")
            for field in missing_data[:10]:  # Show first 10
                print(f"  - {field}")
                
        print(f"\n✅ Output saved to: {output_path}")
        
        # Save detailed report
        report = {
            'timestamp': datetime.now().isoformat(),
            'form_type': 'Aetna Skyrizi PA',
            'total_fields': len(all_form_fields),
            'filled_fields': filled_count,
            'data_extracted': all_data,
            'fields_filled': form_values,
            'missing_fields': missing_data
        }
        
        report_path = output_path.with_suffix('.complete_report.json')
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
            
        print(f"📊 Detailed report saved: {report_path}")
        
        return filled_count


if __name__ == "__main__":
    filler = CorrectedPAFiller(
        Path("test_o3/pa.pdf"),
        Path("test_o3/akshey_extracted.json")
    )
    filler.fill_form(Path("test_o3/pa_corrected_final.pdf"))