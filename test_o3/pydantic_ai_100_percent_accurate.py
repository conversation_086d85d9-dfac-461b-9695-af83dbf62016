"""
100% Accurate PDF Form Filling with PydanticAI
This implementation uses OpenAI GPT-4o for maximum accuracy and comprehensive field mapping
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Literal
from datetime import date, datetime
from pydantic import BaseModel, Field, field_validator
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
from pydantic_ai import Agent, RunContext
import re


# Set OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"


class PDFFieldInfo(BaseModel):
    """Information about a PDF form field"""
    field_name: str = Field(description="The exact name of the PDF form field")
    field_type: str = Field(description="Type of field (text, checkbox, etc.)")
    likely_purpose: str = Field(description="What this field is likely meant to contain")
    confidence: float = Field(description="Confidence in the purpose identification (0-1)")


class ExtractedDataPoint(BaseModel):
    """A piece of extracted data with metadata"""
    value: str = Field(description="The actual data value")
    data_type: str = Field(description="Type of data (name, date, address, etc.)")
    source_path: str = Field(description="Path in the JSON where this data was found")
    confidence: float = Field(description="Confidence in the data accuracy (0-1)")


class FieldMapping(BaseModel):
    """A mapping between a PDF field and extracted data"""
    pdf_field_name: str = Field(description="Name of the PDF form field")
    extracted_value: str = Field(description="Value to fill in the field")
    data_source: str = Field(description="Source path of the data in JSON")
    mapping_confidence: float = Field(description="Confidence in this mapping (0-1)")
    validation_status: Literal["valid", "needs_review", "invalid"] = Field(description="Validation status")
    reasoning: str = Field(description="Why this mapping was chosen")


class FormAnalysisResult(BaseModel):
    """Complete analysis and mapping result"""
    pdf_fields_analyzed: List[PDFFieldInfo] = Field(description="All PDF fields found and analyzed")
    extracted_data_points: List[ExtractedDataPoint] = Field(description="All relevant data extracted")
    field_mappings: List[FieldMapping] = Field(description="Final field mappings")
    unmapped_fields: List[str] = Field(description="PDF fields that couldn't be mapped")
    missing_data: List[str] = Field(description="Required data that wasn't found")
    overall_accuracy_score: float = Field(description="Overall confidence score (0-1)")
    recommendations: List[str] = Field(description="Recommendations for improving accuracy")


class FormFillerDependencies(BaseModel):
    """Dependencies for the PydanticAI agent"""
    pdf_path: Path
    json_path: Path
    raw_json_data: Dict[str, Any]
    pdf_fields: Dict[str, Any]


# Create the PydanticAI agent for 100% accurate form filling
form_analysis_agent = Agent(
    'openai:gpt-4o',  # Use the most capable model
    deps_type=FormFillerDependencies,
    output_type=FormAnalysisResult,
    system_prompt="""
    You are an expert medical form analysis AI with 100% accuracy requirements. Your task is to:
    
    1. Analyze PDF form fields to understand their exact purpose
    2. Extract all relevant data from the provided JSON
    3. Create precise mappings between fields and data
    4. Ensure 100% accuracy by being conservative - only map when certain
    5. Provide detailed reasoning for each mapping decision
    
    CRITICAL REQUIREMENTS:
    - Only create mappings when confidence is >95%
    - For medical forms, accuracy is more important than completeness
    - Provide detailed reasoning for every decision
    - Flag any uncertain mappings for human review
    - Consider medical context and terminology
    
    You have access to tools to analyze the PDF structure and extract data systematically.
    """,
)


@form_analysis_agent.tool
async def analyze_pdf_form_structure(ctx: RunContext[FormFillerDependencies]) -> List[PDFFieldInfo]:
    """Analyze the PDF form structure to understand each field's purpose"""
    pdf_fields = ctx.deps.pdf_fields
    field_info = []
    
    for field_name, field_obj in pdf_fields.items():
        field_type = field_obj.get('/FT', 'Unknown')
        
        # Analyze field name to determine purpose with high confidence
        field_lower = field_name.lower()
        
        # High confidence mappings based on common medical form patterns
        if field_name == 'T14' or 'first' in field_lower:
            purpose = "patient_first_name"
            confidence = 0.98
        elif field_name == 'T15' or 'last' in field_lower:
            purpose = "patient_last_name"
            confidence = 0.98
        elif field_name == 'T16' or any(term in field_lower for term in ['dob', 'birth', 'born']):
            purpose = "date_of_birth"
            confidence = 0.98
        elif field_name == 'T19' or any(term in field_lower for term in ['address', 'street']):
            purpose = "street_address"
            confidence = 0.95
        elif field_name == 'T20' or 'city' in field_lower:
            purpose = "city"
            confidence = 0.95
        elif field_name == 'T21' or 'state' in field_lower:
            purpose = "state"
            confidence = 0.95
        elif field_name == 'T21B' or any(term in field_lower for term in ['zip', 'postal']):
            purpose = "zip_code"
            confidence = 0.95
        elif field_name in ['T21C', 'T21D', 'T21E'] or 'phone' in field_lower:
            purpose = "phone_number"
            confidence = 0.90
        elif field_name == 'T11' or any(term in field_lower for term in ['member', 'id']):
            purpose = "insurance_member_id"
            confidence = 0.98
        elif field_name == 'T12' or 'group' in field_lower:
            purpose = "insurance_group_number"
            confidence = 0.90
        elif field_name == 'T13' or 'insured' in field_lower:
            purpose = "insured_name"
            confidence = 0.95
        elif field_name in ['T17', 'T18'] or 'weight' in field_lower:
            purpose = "weight"
            confidence = 0.90
        elif field_name == 'Request by T' or 'request' in field_lower:
            purpose = "requesting_facility"
            confidence = 0.95
        elif field_name == 'Phone T' or ('phone' in field_lower and 't' in field_lower):
            purpose = "facility_phone"
            confidence = 0.95
        elif field_name == 'Fax T' or 'fax' in field_lower:
            purpose = "facility_fax"
            confidence = 0.95
        else:
            purpose = "unknown"
            confidence = 0.0
        
        field_info.append(PDFFieldInfo(
            field_name=field_name,
            field_type=str(field_type),
            likely_purpose=purpose,
            confidence=confidence
        ))
    
    return field_info


@form_analysis_agent.tool
async def extract_all_relevant_data(ctx: RunContext[FormFillerDependencies]) -> List[ExtractedDataPoint]:
    """Extract all relevant data points from the JSON with high precision"""
    raw_data = ctx.deps.raw_json_data
    data_points = []
    
    try:
        # Patient Demographics
        demo = raw_data['tier_1_mandatory_fields']['patient_demographics']
        
        # Extract name with careful parsing
        full_name = demo['full_name']['value']
        name_parts = full_name.split()
        if len(name_parts) >= 2:
            first_name = name_parts[0]
            last_name = name_parts[-1]
            
            data_points.extend([
                ExtractedDataPoint(
                    value=first_name,
                    data_type="first_name",
                    source_path="tier_1_mandatory_fields.patient_demographics.full_name",
                    confidence=1.0
                ),
                ExtractedDataPoint(
                    value=last_name,
                    data_type="last_name", 
                    source_path="tier_1_mandatory_fields.patient_demographics.full_name",
                    confidence=1.0
                )
            ])
        
        # Date of birth
        dob = demo['date_of_birth']['value']
        data_points.append(ExtractedDataPoint(
            value=dob,
            data_type="date_of_birth",
            source_path="tier_1_mandatory_fields.patient_demographics.date_of_birth",
            confidence=1.0
        ))
        
        # Address parsing with regex
        address = demo['address']['value']
        addr_match = re.match(r'(.+?),\s*(.+?),\s*([A-Z]{2})-?(\d{5})', address)
        if addr_match:
            street, city, state, zip_code = addr_match.groups()
            data_points.extend([
                ExtractedDataPoint(value=street.strip(), data_type="street_address", 
                                 source_path="tier_1_mandatory_fields.patient_demographics.address", confidence=0.98),
                ExtractedDataPoint(value=city.strip(), data_type="city",
                                 source_path="tier_1_mandatory_fields.patient_demographics.address", confidence=0.98),
                ExtractedDataPoint(value=state.strip(), data_type="state",
                                 source_path="tier_1_mandatory_fields.patient_demographics.address", confidence=1.0),
                ExtractedDataPoint(value=zip_code.strip(), data_type="zip_code",
                                 source_path="tier_1_mandatory_fields.patient_demographics.address", confidence=1.0)
            ])
        
        # Phone numbers
        phones = demo['phone_numbers']['value']
        if phones:
            data_points.append(ExtractedDataPoint(
                value=phones[0],
                data_type="phone_number",
                source_path="tier_1_mandatory_fields.patient_demographics.phone_numbers",
                confidence=1.0
            ))
        
        # Medical record number
        mrn = demo.get('medical_record_number', {}).get('value')
        if mrn:
            data_points.append(ExtractedDataPoint(
                value=mrn,
                data_type="medical_record_number",
                source_path="tier_1_mandatory_fields.patient_demographics.medical_record_number",
                confidence=1.0
            ))
        
        # Weight parsing
        weight_info = demo.get('physical_measurements', {}).get('weight', {}).get('value', '')
        if weight_info:
            lbs_match = re.search(r'(\d+(?:\.\d+)?)\s*lbs?', weight_info)
            kg_match = re.search(r'(\d+(?:\.\d+)?)\s*kg', weight_info)
            
            if lbs_match:
                data_points.append(ExtractedDataPoint(
                    value=lbs_match.group(1),
                    data_type="weight_lbs",
                    source_path="tier_1_mandatory_fields.patient_demographics.physical_measurements.weight",
                    confidence=0.95
                ))
            if kg_match:
                data_points.append(ExtractedDataPoint(
                    value=kg_match.group(1),
                    data_type="weight_kg",
                    source_path="tier_1_mandatory_fields.patient_demographics.physical_measurements.weight",
                    confidence=0.95
                ))
        
        # Insurance Information
        ins = raw_data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
        
        data_points.extend([
            ExtractedDataPoint(
                value=ins['member_id']['value'],
                data_type="insurance_member_id",
                source_path="tier_1_mandatory_fields.insurance_information.primary_insurance.member_id",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=ins['payer_name']['value'],
                data_type="insurance_payer_name",
                source_path="tier_1_mandatory_fields.insurance_information.primary_insurance.payer_name",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=full_name,  # Insured name is typically the patient name
                data_type="insured_name",
                source_path="tier_1_mandatory_fields.patient_demographics.full_name",
                confidence=0.98
            )
        ])
        
        # Group number if available
        group_num = ins.get('group_number', {}).get('value')
        if group_num:
            data_points.append(ExtractedDataPoint(
                value=group_num,
                data_type="insurance_group_number",
                source_path="tier_1_mandatory_fields.insurance_information.primary_insurance.group_number",
                confidence=1.0
            ))
        
        # Prescriber Information
        pres = raw_data['tier_1_mandatory_fields']['prescriber_information']

        data_points.extend([
            ExtractedDataPoint(
                value=pres['facility_name']['value'],
                data_type="requesting_facility",
                source_path="tier_1_mandatory_fields.prescriber_information.facility_name",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=pres['phone_fax']['value']['phone'],
                data_type="facility_phone",
                source_path="tier_1_mandatory_fields.prescriber_information.phone_fax.phone",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=pres['phone_fax']['value']['fax'],
                data_type="facility_fax",
                source_path="tier_1_mandatory_fields.prescriber_information.phone_fax.fax",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=pres['physician_name']['value'],
                data_type="physician_name",
                source_path="tier_1_mandatory_fields.prescriber_information.physician_name",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=pres['npi_number']['value'],
                data_type="npi_number",
                source_path="tier_1_mandatory_fields.prescriber_information.npi_number",
                confidence=1.0
            )
        ])

        # Clinical Information
        clin = raw_data['tier_2_clinical_justification']

        data_points.extend([
            ExtractedDataPoint(
                value=clin['primary_diagnosis']['icd_code']['value'],
                data_type="diagnosis_code",
                source_path="tier_2_clinical_justification.primary_diagnosis.icd_code",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=clin['primary_diagnosis']['diagnosis_description']['value'],
                data_type="diagnosis_description",
                source_path="tier_2_clinical_justification.primary_diagnosis.diagnosis_description",
                confidence=1.0
            ),
            ExtractedDataPoint(
                value=clin['requested_medication']['drug_name']['value'],
                data_type="medication_name",
                source_path="tier_2_clinical_justification.requested_medication.drug_name",
                confidence=1.0
            )
        ])
        
    except Exception as e:
        print(f"Error extracting data: {e}")
    
    return data_points


@form_analysis_agent.tool
async def create_precise_field_mappings(
    ctx: RunContext[FormFillerDependencies],
    pdf_fields: List[PDFFieldInfo],
    data_points: List[ExtractedDataPoint]
) -> List[FieldMapping]:
    """Create precise field mappings with detailed reasoning"""
    mappings = []
    
    # Create lookup dictionaries for efficient matching
    data_by_type = {dp.data_type: dp for dp in data_points}
    fields_by_purpose = {pf.likely_purpose: pf for pf in pdf_fields if pf.confidence > 0.8}
    
    # Define high-confidence mapping rules
    mapping_rules = [
        ("patient_first_name", "first_name", 0.98),
        ("patient_last_name", "last_name", 0.98),
        ("date_of_birth", "date_of_birth", 0.98),
        ("street_address", "street_address", 0.95),
        ("city", "city", 0.95),
        ("state", "state", 0.95),
        ("zip_code", "zip_code", 0.95),
        ("phone_number", "phone_number", 0.90),
        ("insurance_member_id", "insurance_member_id", 0.98),
        ("insurance_group_number", "insurance_group_number", 0.85),
        ("insured_name", "insured_name", 0.95),
        ("weight", "weight_lbs", 0.90),
        ("requesting_facility", "requesting_facility", 0.95),
        ("facility_phone", "facility_phone", 0.95),
        ("facility_fax", "facility_fax", 0.95)
    ]
    
    for field_purpose, data_type, base_confidence in mapping_rules:
        if field_purpose in fields_by_purpose and data_type in data_by_type:
            field_info = fields_by_purpose[field_purpose]
            data_point = data_by_type[data_type]
            
            # Calculate final confidence
            final_confidence = min(base_confidence, field_info.confidence, data_point.confidence)
            
            # Only create mapping if confidence is high enough
            if final_confidence >= 0.85:
                validation_status = "valid" if final_confidence >= 0.95 else "needs_review"
                
                mappings.append(FieldMapping(
                    pdf_field_name=field_info.field_name,
                    extracted_value=data_point.value,
                    data_source=data_point.source_path,
                    mapping_confidence=final_confidence,
                    validation_status=validation_status,
                    reasoning=f"High confidence mapping: {field_purpose} -> {data_type} (confidence: {final_confidence:.2f})"
                ))
    
    return mappings


class AccuratePDFFormFiller:
    """100% Accurate PDF Form Filler using PydanticAI"""
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.json_path = json_path
        
        # Load data
        with open(json_path, 'r') as f:
            self.raw_json_data = json.load(f)
        
        # Get PDF fields
        reader = PdfReader(str(pdf_path))
        self.pdf_fields = reader.get_fields() or {}
        
        # Create dependencies
        self.deps = FormFillerDependencies(
            pdf_path=pdf_path,
            json_path=json_path,
            raw_json_data=self.raw_json_data,
            pdf_fields=self.pdf_fields
        )
    
    async def analyze_and_fill_form(self) -> FormAnalysisResult:
        """Use PydanticAI to analyze and create accurate mappings"""
        result = await form_analysis_agent.run(
            "Analyze this medical form and create 100% accurate field mappings. "
            "Be extremely conservative - only map fields when you are certain. "
            "Provide detailed reasoning for each mapping decision.",
            deps=self.deps
        )
        return result.output
    
    def fill_pdf_with_analysis(self, analysis: FormAnalysisResult, output_path: Path):
        """Fill PDF using the analysis results"""
        reader = PdfReader(str(self.pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm is copied
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
        
        # Create field values from valid mappings only
        field_values = {}
        for mapping in analysis.field_mappings:
            if mapping.validation_status == "valid":
                field_values[mapping.pdf_field_name] = mapping.extracted_value
        
        # Update fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, field_values)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        return len(field_values)


async def main():
    """Main function to demonstrate 100% accurate form filling"""
    print("=== 100% ACCURATE PDF FORM FILLING WITH PYDANTIC AI ===\n")
    
    filler = AccuratePDFFormFiller(
        pdf_path=Path("pa.pdf"),
        json_path=Path("akshey_extracted.json")
    )
    
    print("Analyzing form and data with PydanticAI...")
    analysis = await filler.analyze_and_fill_form()
    
    print(f"\n=== ANALYSIS RESULTS ===")
    print(f"PDF fields analyzed: {len(analysis.pdf_fields_analyzed)}")
    print(f"Data points extracted: {len(analysis.extracted_data_points)}")
    print(f"High-confidence mappings created: {len([m for m in analysis.field_mappings if m.validation_status == 'valid'])}")
    print(f"Mappings needing review: {len([m for m in analysis.field_mappings if m.validation_status == 'needs_review'])}")
    print(f"Overall accuracy score: {analysis.overall_accuracy_score:.2f}")
    
    if analysis.unmapped_fields:
        print(f"Unmapped fields: {analysis.unmapped_fields}")
    
    if analysis.missing_data:
        print(f"Missing data: {analysis.missing_data}")
    
    print(f"\n=== FIELD MAPPINGS ===")
    for mapping in analysis.field_mappings:
        status_icon = "✅" if mapping.validation_status == "valid" else "⚠️"
        print(f"{status_icon} {mapping.pdf_field_name}: '{mapping.extracted_value}' (confidence: {mapping.mapping_confidence:.2f})")
        print(f"   Reasoning: {mapping.reasoning}")
    
    print(f"\n=== RECOMMENDATIONS ===")
    for rec in analysis.recommendations:
        print(f"• {rec}")
    
    # Fill the PDF
    filled_count = filler.fill_pdf_with_analysis(analysis, Path("pa_100_percent_accurate.pdf"))
    print(f"\n✅ PDF filled with {filled_count} high-confidence fields")
    print(f"Output saved to: pa_100_percent_accurate.pdf")
    
    return analysis


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
