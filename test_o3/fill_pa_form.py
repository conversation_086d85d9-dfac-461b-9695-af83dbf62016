import argparse
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple

from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, TextStringObject, BooleanObject
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from pdfrw import PdfReader as PdfRwReader, PdfWriter as PdfRwWriter, PageMerge

SCHEMA_ALIASES = {
    # canonical_schema_key : [possible pdf field substrings]
    "patient_demographics.full_name": ["first name", "last name", "name"],
    "patient_demographics.date_of_birth": ["dob", "birth"],
    "patient_demographics.address": ["address"],
    "patient_demographics.phone_numbers": ["phone", "cell"],
    "insurance_information.primary_insurance.member_id": ["member id", "policy"],
    "prescriber_information.physician_name": ["prescriber", "physician"],
    "prescriber_information.npi_number": ["npi"],
    "tier_2_clinical_justification.primary_diagnosis.icd_code": ["icd", "diagnosis code"],
    "tier_2_clinical_justification.requested_medication.drug_name": ["drug", "medication name"],
    "tier_2_clinical_justification.requested_medication.dosage": ["dosage"],
}


class PAFormFiller:
    """Light-weight algorithm to fill interactive or static PDF forms."""

    def __init__(self, json_path: Path, pdf_path: Path):
        self.data = self._load_json(json_path)
        self.pdf_path = pdf_path

    @staticmethod
    def _load_json(path: Path) -> Dict[str, Any]:
        with path.open() as f:
            return json.load(f)

    # ───────────────────────── PDF TYPE ────────────────────────── #

    def _is_interactive(self) -> bool:
        try:
            reader = PdfReader(str(self.pdf_path))
            return "/AcroForm" in reader.trailer["/Root"] and bool(reader.get_fields())
        except Exception:
            return False

    # ─────────────────────────── MAPPING ─────────────────────────── #

    def _flatten_data(self) -> Dict[str, Any]:
        """Flatten nested JSON using dotted keys so we can easily lookup."""
        flat: Dict[str, Any] = {}

        def _walk(prefix: str, obj: Any):
            if isinstance(obj, dict):
                for k, v in obj.items():
                    _walk(f"{prefix}.{k}" if prefix else k, v)
            else:
                flat[prefix] = obj

        _walk("", self.data)
        return flat

    @staticmethod
    def _normalize(txt: str) -> str:
        return re.sub(r"[^a-z0-9]", "", txt.lower())

    def _auto_map(self, pdf_fields: List[str]) -> Dict[str, Any]:
        """Return mapping {pdf_field: value} using heuristics + alias table."""
        flat_data = self._flatten_data()
        mapping = {}
        for f in pdf_fields:
            norm = self._normalize(f)
            # first pass: alias table
            for key, aliases in SCHEMA_ALIASES.items():
                if any(a.replace(" ", "") in norm for a in aliases):
                    if key in flat_data and isinstance(flat_data[key], (str, int, float)):
                        mapping[f] = flat_data[key]
                        break
            # direct key match
            if f not in mapping:
                for k, v in flat_data.items():
                    if self._normalize(k).endswith(norm):
                        mapping[f] = v
                        break
        return mapping

    # ────────────────── INTERACTIVE FILLER ────────────────── #

    def _fill_interactive(self, out_pdf: Path):
        reader = PdfReader(str(self.pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)

        pdf_fields = list(reader.get_fields().keys())
        form_values = self._auto_map(pdf_fields)

        # update first page fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)

        # rebuild appearances so filled values render everywhere
        writer._root_object.update({NameObject("/NeedAppearances"): BooleanObject(True)})

        with out_pdf.open("wb") as f:
            writer.write(f)

    # ──────────────────── STATIC FILLER ───────────────────── #

    def _fill_static(self, out_pdf: Path):
        # naive overlay: write all flattened values into first page top-left for demo
        overlay = self.pdf_path.with_suffix(".overlay.pdf")
        c = canvas.Canvas(str(overlay), pagesize=letter)
        flat = self._flatten_data()
        y = 750
        for k, v in list(flat.items())[:40]:  # cap to 40 lines to avoid huge overlay
            c.drawString(40, y, f"{k}: {v}")
            y -= 14
        c.save()

        # merge
        base = PdfRwReader(str(self.pdf_path))
        over = PdfRwReader(str(overlay))
        for page_num, page in enumerate(base.pages):
            if page_num < len(over.pages):
                PageMerge(page).add(over.pages[page_num]).render()
        PdfRwWriter(str(out_pdf), trailer=base).write()
        overlay.unlink()

    # ────────────────────────── PUBLIC ─────────────────────────── #

    def fill(self, output: Path):
        if self._is_interactive():
            self._fill_interactive(output)
        else:
            self._fill_static(output)


# ────────────────────────── CLI ──────────────────────────── #

def main():
    p = argparse.ArgumentParser(description="Fill a PA PDF using extracted JSON data")
    p.add_argument("--json", required=True, type=Path, help="Path to extracted JSON")
    p.add_argument("--pdf", required=True, type=Path, help="Path to blank PA form PDF")
    p.add_argument("--out", required=True, type=Path, help="Destination for filled PDF")
    args = p.parse_args()

    filler = PAFormFiller(args.json, args.pdf)
    filler.fill(args.out)
    print(f"✅ Filled form saved to {args.out}")


if __name__ == "__main__":
    main() 