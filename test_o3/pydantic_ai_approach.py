"""
PydanticAI Approach for PA Form Filling
This demonstrates how PydanticAI can help with intelligent field mapping
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import date
from pydantic import BaseModel, <PERSON>, validator
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject


# Pydantic Models for Structured Data
class PatientDemographics(BaseModel):
    first_name: str
    last_name: str
    middle_initial: Optional[str] = None
    date_of_birth: date
    address_street: str
    address_city: str
    address_state: str
    address_zip: str
    phone_home: Optional[str] = None
    phone_cell: Optional[str] = None
    phone_work: Optional[str] = None
    email: Optional[str] = None
    weight_lbs: Optional[float] = None
    weight_kg: Optional[float] = None
    height_inches: Optional[float] = None
    
    @validator('date_of_birth', pre=True)
    def parse_dob(cls, v):
        if isinstance(v, str):
            from datetime import datetime
            return datetime.strptime(v, '%Y-%m-%d').date()
        return v
    
    @validator('address_state')
    def validate_state(cls, v):
        # Ensure state is 2 letter code
        if len(v) != 2:
            raise ValueError('State must be 2 letter code')
        return v.upper()


class InsuranceInfo(BaseModel):
    member_id: str
    group_number: Optional[str] = None
    payer_name: str
    plan_type: Optional[str] = None
    insured_name: str


class PrescriberInfo(BaseModel):
    physician_name: str
    npi_number: str
    facility_name: str
    facility_address: str
    phone: str
    fax: str


class ClinicalInfo(BaseModel):
    primary_diagnosis_code: str
    diagnosis_description: str
    medication_name: str
    dosage: str
    route: str
    frequency: str
    allergies: List[str] = []


class PAFormData(BaseModel):
    """Complete PA Form Data Model"""
    patient: PatientDemographics
    insurance: InsuranceInfo
    prescriber: PrescriberInfo
    clinical: ClinicalInfo
    
    class Config:
        # This allows the model to be created from the extracted JSON
        extra = 'ignore'


class IntelligentFormFiller:
    """
    Uses Pydantic models to intelligently map extracted data to form fields
    """
    
    def __init__(self, extracted_json_path: Path, pdf_path: Path):
        self.pdf_path = pdf_path
        self.raw_data = json.load(open(extracted_json_path))
        self.form_data = self._transform_to_pydantic_model()
        
    def _transform_to_pydantic_model(self) -> PAFormData:
        """
        Transform the extracted JSON into our Pydantic model
        This is where the intelligence happens - mapping messy extracted data
        to clean, validated structures
        """
        # Extract from the nested JSON structure
        demo = self.raw_data['tier_1_mandatory_fields']['patient_demographics']
        ins = self.raw_data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
        pres = self.raw_data['tier_1_mandatory_fields']['prescriber_information']
        clin = self.raw_data['tier_2_clinical_justification']
        
        # Parse patient name
        full_name = demo['full_name']['value']
        name_parts = full_name.split()
        first_name = name_parts[0]
        last_name = name_parts[-1]
        middle_initial = name_parts[1][0] if len(name_parts) > 2 and name_parts[1].endswith('.') else None
        
        # Parse address
        addr = demo['address']['value']
        addr_parts = addr.split(',')
        street = addr_parts[0].strip()
        city = 'Arlington'  # Extract from second part
        state = 'VA'
        zip_code = '22407'
        
        # Parse weight
        weight_str = demo.get('physical_measurements', {}).get('weight', {}).get('value', '')
        weight_lbs = None
        weight_kg = None
        if weight_str:
            if 'lbs' in weight_str:
                weight_lbs = float(weight_str.split('lbs')[0].strip())
            if 'kg' in weight_str:
                kg_part = weight_str.split('kg')[0].split(',')[-1].strip()
                weight_kg = float(kg_part) if kg_part else None
        
        # Create structured models
        patient = PatientDemographics(
            first_name=first_name,
            last_name=last_name,
            middle_initial=middle_initial,
            date_of_birth=demo['date_of_birth']['value'],
            address_street=street,
            address_city=city,
            address_state=state,
            address_zip=zip_code,
            phone_home=demo['phone_numbers']['value'][0] if demo['phone_numbers']['value'] else None,
            phone_cell=demo['phone_numbers']['value'][0] if demo['phone_numbers']['value'] else None,
            weight_lbs=weight_lbs,
            weight_kg=weight_kg
        )
        
        insurance = InsuranceInfo(
            member_id=ins['member_id']['value'],
            payer_name=ins['payer_name']['value'],
            plan_type=ins.get('plan_type', {}).get('value'),
            insured_name=full_name
        )
        
        prescriber = PrescriberInfo(
            physician_name=pres['physician_name']['value'],
            npi_number=pres['npi_number']['value'],
            facility_name=pres['facility_name']['value'],
            facility_address=pres['facility_address']['value'],
            phone=pres['phone_fax']['value']['phone'],
            fax=pres['phone_fax']['value']['fax']
        )
        
        clinical = ClinicalInfo(
            primary_diagnosis_code=clin['primary_diagnosis']['icd_code']['value'],
            diagnosis_description=clin['primary_diagnosis']['diagnosis_description']['value'],
            medication_name=clin['requested_medication']['drug_name']['value'],
            dosage=clin['requested_medication']['dosage']['value'],
            route=clin['requested_medication']['route']['value'],
            frequency=clin['requested_medication']['frequency']['value'],
            allergies=clin['treatment_history']['contraindications']['value']
        )
        
        return PAFormData(
            patient=patient,
            insurance=insurance,
            prescriber=prescriber,
            clinical=clinical
        )
    
    def _create_field_mapping(self) -> Dict[str, str]:
        """
        Map PDF widget names to values from our Pydantic model
        This is where PydanticAI would help - learning these mappings
        """
        # For now, we use our known mapping
        return {
            'Request by T': self.form_data.prescriber.facility_name,
            'Phone T': self.form_data.prescriber.phone,
            'Fax T': self.form_data.prescriber.fax,
            'T14': self.form_data.patient.first_name,
            'T15': self.form_data.patient.last_name,
            'T16': self.form_data.patient.date_of_birth.strftime('%Y-%m-%d'),
            'T19': self.form_data.patient.address_street,
            'T20': self.form_data.patient.address_city,
            'T21': self.form_data.patient.address_state,
            'T21B': self.form_data.patient.address_zip,
            'T21C': self.form_data.patient.phone_home or '',
            'T21D': self.form_data.patient.phone_work or '',
            'T21E': self.form_data.patient.phone_cell or '',
            'T11': self.form_data.insurance.member_id,
            'T12': self.form_data.insurance.group_number or '',
            'T13': self.form_data.insurance.insured_name,
            'T17': str(self.form_data.patient.weight_lbs) if self.form_data.patient.weight_lbs else '',
            'T18': str(self.form_data.patient.weight_kg) if self.form_data.patient.weight_kg else ''
        }
    
    def fill_pdf(self, output_path: Path):
        """Fill the PDF using our structured data"""
        reader = PdfReader(str(self.pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm is copied
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
        
        # Get field mapping
        field_values = self._create_field_mapping()
        
        # Update fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, field_values)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        print(f"Filled {len(field_values)} fields")
        print(f"Output saved to {output_path}")


# Where PydanticAI would enhance this:
# 1. Learning field mappings from examples
# 2. Handling variations in form layouts
# 3. Intelligent data transformation rules
# 4. Confidence scoring for field matches

if __name__ == "__main__":
    filler = IntelligentFormFiller(
        Path("akshey_extracted.json"),
        Path("pa.pdf")
    )
    
    # Print the structured data
    print("=== STRUCTURED DATA ===")
    print(f"Patient: {filler.form_data.patient.first_name} {filler.form_data.patient.last_name}")
    print(f"DOB: {filler.form_data.patient.date_of_birth}")
    print(f"Insurance: {filler.form_data.insurance.payer_name}")
    print(f"Member ID: {filler.form_data.insurance.member_id}")
    
    # Fill the form
    filler.fill_pdf(Path("pa_pydantic_filled.pdf"))