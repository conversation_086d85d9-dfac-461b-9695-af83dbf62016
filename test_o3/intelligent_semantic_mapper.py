"""
Intelligent Semantic Mapping Solution for PA Form Filling
This solves the REAL challenge: mapping structured extracted data to arbitrary PDF form fields
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from PyPDF2 import PdfReader, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
import re


# Set OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"


class ExtractedDataSchema(BaseModel):
    """Schema representing the structure of extracted data"""
    field_path: str = Field(description="JSON path to the data (e.g., 'patient_demographics.full_name.value')")
    data_value: str = Field(description="The actual extracted value")
    data_type: str = Field(description="Semantic type (e.g., 'patient_name', 'date_of_birth', 'address')")
    confidence: float = Field(description="Confidence in the extracted data")


class PDFFormField(BaseModel):
    """Schema representing a PDF form field"""
    field_name: str = Field(description="Exact PDF field name (e.g., 'T14', 'Patient Name')")
    field_type: str = Field(description="PDF field type (/Tx, /Btn, etc.)")
    likely_purpose: str = Field(description="What this field likely represents")


class SemanticMapping(BaseModel):
    """A semantic mapping between extracted data and PDF field"""
    pdf_field_name: str = Field(description="PDF form field name")
    extracted_data_path: str = Field(description="Path to data in extracted JSON")
    mapped_value: str = Field(description="Value to fill in the field")
    confidence_score: float = Field(description="Confidence in this mapping (0-1)")
    reasoning: str = Field(description="Why this mapping was chosen")
    transformation_applied: Optional[str] = Field(None, description="Any data transformation applied")


class MappingResult(BaseModel):
    """Complete mapping result"""
    successful_mappings: List[SemanticMapping] = Field(description="All successful mappings")
    unmapped_pdf_fields: List[str] = Field(description="PDF fields that couldn't be mapped")
    unused_extracted_data: List[str] = Field(description="Extracted data that wasn't used")
    overall_coverage: float = Field(description="Percentage of PDF fields mapped")
    recommendations: List[str] = Field(description="Recommendations for improvement")


class MappingDependencies(BaseModel):
    """Dependencies for the semantic mapping agent"""
    extracted_data: Dict[str, Any]
    pdf_fields: Dict[str, Any]
    pdf_path: str
    json_path: str


# Create the intelligent semantic mapping agent
semantic_mapping_agent = Agent(
    'openai:gpt-4o',
    deps_type=MappingDependencies,
    output_type=MappingResult,
    system_prompt="""
    You are an expert AI system for intelligent semantic mapping between extracted medical data and PDF form fields.
    
    Your task is to analyze:
    1. Structured extracted data (with paths like 'patient_demographics.full_name.value')
    2. PDF form fields (with arbitrary names like 'T14', 'Patient Name', etc.)
    
    Then create intelligent mappings by understanding the SEMANTIC MEANING of both sides.
    
    Key principles:
    - Focus on semantic meaning, not exact name matches
    - A field named 'T14' could be for 'first_name' if context suggests it
    - Handle data transformations (e.g., split 'full_name' into 'first_name' and 'last_name')
    - Consider medical form conventions and patterns
    - Be conservative - only map when confident
    - Provide clear reasoning for each mapping decision
    
    You have access to tools to analyze the data structure and form fields systematically.
    """,
)


@semantic_mapping_agent.tool
async def analyze_extracted_data_structure(ctx: RunContext[MappingDependencies]) -> List[ExtractedDataSchema]:
    """Analyze the structure of extracted data to understand what's available"""
    extracted_data = ctx.deps.extracted_data
    data_items = []
    
    def extract_values(obj, path=""):
        """Recursively extract all values with their paths"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                new_path = f"{path}.{key}" if path else key
                if key == "value" and isinstance(value, (str, int, float)):
                    # This is an actual data value
                    parent_path = path
                    data_type = _infer_data_type(parent_path, str(value))
                    confidence = obj.get("confidence", 1.0) if isinstance(obj.get("confidence"), (int, float)) else 1.0
                    
                    data_items.append(ExtractedDataSchema(
                        field_path=parent_path,
                        data_value=str(value),
                        data_type=data_type,
                        confidence=float(confidence)
                    ))
                elif isinstance(value, (dict, list)):
                    extract_values(value, new_path)
                elif isinstance(value, (str, int, float)) and key not in ["confidence", "reasoning"]:
                    # Direct value
                    data_type = _infer_data_type(new_path, str(value))
                    data_items.append(ExtractedDataSchema(
                        field_path=new_path,
                        data_value=str(value),
                        data_type=data_type,
                        confidence=1.0
                    ))
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                extract_values(item, f"{path}[{i}]")
    
    def _infer_data_type(path: str, value: str) -> str:
        """Infer the semantic data type from path and value"""
        path_lower = path.lower()
        value_lower = value.lower()
        
        # Name-related
        if "full_name" in path_lower:
            return "patient_full_name"
        elif "first" in path_lower or "given" in path_lower:
            return "patient_first_name"
        elif "last" in path_lower or "family" in path_lower:
            return "patient_last_name"
        elif "physician_name" in path_lower:
            return "physician_name"
        
        # Date-related
        elif "date_of_birth" in path_lower or "dob" in path_lower:
            return "patient_date_of_birth"
        elif re.match(r'\d{4}-\d{2}-\d{2}', value):
            return "date"
        
        # Address-related
        elif "address" in path_lower and "facility" not in path_lower:
            return "patient_address"
        elif "facility_address" in path_lower:
            return "facility_address"
        
        # Phone-related
        elif "phone" in path_lower and re.match(r'[\d\-\(\)\s]+', value):
            if "facility" in path_lower:
                return "facility_phone"
            else:
                return "patient_phone"
        elif "fax" in path_lower:
            return "facility_fax"
        
        # Medical-related
        elif "member_id" in path_lower:
            return "insurance_member_id"
        elif "group_number" in path_lower:
            return "insurance_group_number"
        elif "payer_name" in path_lower:
            return "insurance_payer_name"
        elif "weight" in path_lower:
            return "patient_weight"
        elif "height" in path_lower:
            return "patient_height"
        elif "icd_code" in path_lower:
            return "diagnosis_code"
        elif "drug_name" in path_lower:
            return "medication_name"
        elif "npi_number" in path_lower:
            return "physician_npi"
        elif "facility_name" in path_lower:
            return "facility_name"
        
        # Default
        else:
            return "unknown"
    
    extract_values(extracted_data)
    return data_items


@semantic_mapping_agent.tool
async def analyze_pdf_form_fields(ctx: RunContext[MappingDependencies]) -> List[PDFFormField]:
    """Analyze PDF form fields to understand their likely purposes"""
    pdf_fields = ctx.deps.pdf_fields
    form_fields = []
    
    for field_name, field_obj in pdf_fields.items():
        field_type = field_obj.get('/FT', 'Unknown')
        
        # Infer purpose from field name using semantic analysis
        purpose = _infer_field_purpose(field_name)
        
        form_fields.append(PDFFormField(
            field_name=field_name,
            field_type=str(field_type),
            likely_purpose=purpose
        ))
    
    return form_fields


def _infer_field_purpose(field_name: str) -> str:
    """Infer the semantic purpose of a PDF field from its name"""
    name_lower = field_name.lower()
    
    # Common patterns in medical forms
    patterns = {
        # Patient info
        r'(first|fname|given)': 'patient_first_name',
        r'(last|lname|surname|family)': 'patient_last_name',
        r'(patient.*name|name.*patient)': 'patient_full_name',
        r'(dob|birth|born)': 'patient_date_of_birth',
        r'(address|addr)': 'patient_address',
        r'(city)': 'patient_city',
        r'(state)': 'patient_state',
        r'(zip|postal)': 'patient_zip',
        r'(phone|tel)': 'patient_phone',
        r'(weight)': 'patient_weight',
        r'(height)': 'patient_height',
        
        # Insurance
        r'(member|id)': 'insurance_member_id',
        r'(group)': 'insurance_group_number',
        r'(insured|subscriber)': 'insurance_subscriber_name',
        r'(payer|insurance.*name)': 'insurance_payer_name',
        
        # Provider
        r'(physician|doctor|provider|prescriber)': 'physician_name',
        r'(npi)': 'physician_npi',
        r'(facility|clinic|practice)': 'facility_name',
        r'(request.*by|requesting)': 'facility_name',
        r'(fax)': 'facility_fax',
        
        # Clinical
        r'(diagnosis|icd)': 'diagnosis_code',
        r'(medication|drug)': 'medication_name',
        r'(dosage|dose)': 'medication_dosage',
        
        # Checkboxes
        r'(indicate|clinical|cb)': 'checkbox'
    }
    
    # Check patterns
    for pattern, purpose in patterns.items():
        if re.search(pattern, name_lower):
            return purpose
    
    # Special handling for numbered fields (T14, T15, etc.)
    if re.match(r't\d+', name_lower):
        # These are often sequential form fields
        # T14, T15 are commonly first/last name
        # T16 is often DOB
        # T19-T21 are often address fields
        field_num = re.search(r't(\d+)', name_lower)
        if field_num:
            num = int(field_num.group(1))
            if num == 14:
                return 'patient_first_name'
            elif num == 15:
                return 'patient_last_name'
            elif num == 16:
                return 'patient_date_of_birth'
            elif num == 19:
                return 'patient_address'
            elif num == 20:
                return 'patient_city'
            elif num == 21:
                return 'patient_state'
            elif num in [11, 12, 13]:
                return 'insurance_field'
            elif num in [17, 18]:
                return 'patient_weight'
    
    return 'unknown'


@semantic_mapping_agent.tool
async def create_intelligent_mappings(
    ctx: RunContext[MappingDependencies],
    extracted_data: List[ExtractedDataSchema],
    pdf_fields: List[PDFFormField]
) -> List[SemanticMapping]:
    """Create intelligent semantic mappings between data and form fields"""
    mappings = []
    
    # Create lookup dictionaries
    data_by_type = {}
    for data_item in extracted_data:
        if data_item.data_type not in data_by_type:
            data_by_type[data_item.data_type] = []
        data_by_type[data_item.data_type].append(data_item)
    
    fields_by_purpose = {}
    for field in pdf_fields:
        if field.likely_purpose not in fields_by_purpose:
            fields_by_purpose[field.likely_purpose] = []
        fields_by_purpose[field.likely_purpose].append(field)
    
    # Create mappings based on semantic matching
    mapping_rules = [
        ('patient_full_name', 'patient_first_name', 'extract_first_name'),
        ('patient_full_name', 'patient_last_name', 'extract_last_name'),
        ('patient_full_name', 'patient_full_name', 'direct'),
        ('patient_date_of_birth', 'patient_date_of_birth', 'direct'),
        ('patient_address', 'patient_address', 'extract_street'),
        ('patient_address', 'patient_city', 'extract_city'),
        ('patient_address', 'patient_state', 'extract_state'),
        ('patient_address', 'patient_zip', 'extract_zip'),
        ('patient_phone', 'patient_phone', 'direct'),
        ('patient_weight', 'patient_weight', 'extract_weight'),
        ('insurance_member_id', 'insurance_member_id', 'direct'),
        ('insurance_group_number', 'insurance_group_number', 'direct'),
        ('insurance_payer_name', 'insurance_payer_name', 'direct'),
        ('physician_name', 'physician_name', 'direct'),
        ('physician_npi', 'physician_npi', 'direct'),
        ('facility_name', 'facility_name', 'direct'),
        ('facility_phone', 'patient_phone', 'direct'),
        ('facility_fax', 'facility_fax', 'direct'),
    ]
    
    for data_type, field_purpose, transformation in mapping_rules:
        if data_type in data_by_type and field_purpose in fields_by_purpose:
            data_item = data_by_type[data_type][0]  # Take first match
            field_item = fields_by_purpose[field_purpose][0]  # Take first match
            
            # Apply transformation
            mapped_value, transform_desc = _apply_transformation(
                data_item.data_value, transformation
            )
            
            if mapped_value:  # Only create mapping if transformation succeeded
                confidence = min(0.95, data_item.confidence)
                
                mappings.append(SemanticMapping(
                    pdf_field_name=field_item.field_name,
                    extracted_data_path=data_item.field_path,
                    mapped_value=mapped_value,
                    confidence_score=confidence,
                    reasoning=f"Semantic mapping: {data_type} -> {field_purpose}",
                    transformation_applied=transform_desc
                ))
    
    return mappings


def _apply_transformation(value: str, transformation: str) -> tuple:
    """Apply data transformation and return (result, description)"""
    if transformation == 'direct':
        return value, 'No transformation'
    
    elif transformation == 'extract_first_name':
        parts = value.split()
        return parts[0] if parts else '', 'Extracted first name from full name'
    
    elif transformation == 'extract_last_name':
        parts = value.split()
        return parts[-1] if len(parts) > 1 else '', 'Extracted last name from full name'
    
    elif transformation == 'extract_street':
        # Extract street from full address
        match = re.match(r'([^,]+)', value)
        return match.group(1).strip() if match else '', 'Extracted street from address'
    
    elif transformation == 'extract_city':
        # Extract city from address
        match = re.search(r',\s*([^,]+),\s*[A-Z]{2}', value)
        return match.group(1).strip() if match else '', 'Extracted city from address'
    
    elif transformation == 'extract_state':
        # Extract state from address
        match = re.search(r',\s*([A-Z]{2})', value)
        return match.group(1).strip() if match else '', 'Extracted state from address'
    
    elif transformation == 'extract_zip':
        # Extract ZIP from address
        match = re.search(r'(\d{5})', value)
        return match.group(1) if match else '', 'Extracted ZIP from address'
    
    elif transformation == 'extract_weight':
        # Extract weight in lbs
        match = re.search(r'(\d+(?:\.\d+)?)\s*lbs?', value, re.IGNORECASE)
        return match.group(1) if match else value, 'Extracted weight in lbs'
    
    else:
        return value, f'Unknown transformation: {transformation}'


class IntelligentSemanticMapper:
    """Main class for intelligent semantic mapping"""
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.json_path = json_path
        
        # Load data
        with open(json_path, 'r') as f:
            self.extracted_data = json.load(f)
        
        # Get PDF fields
        reader = PdfReader(str(pdf_path))
        self.pdf_fields = reader.get_fields() or {}
        
        # Create dependencies
        self.deps = MappingDependencies(
            extracted_data=self.extracted_data,
            pdf_fields=self.pdf_fields,
            pdf_path=str(pdf_path),
            json_path=str(json_path)
        )
    
    async def create_semantic_mappings(self) -> MappingResult:
        """Use AI to create intelligent semantic mappings"""
        result = await semantic_mapping_agent.run(
            "Analyze the extracted data and PDF form fields, then create intelligent semantic mappings. "
            "Focus on understanding the meaning behind field names and data paths, not just exact matches. "
            "Handle data transformations as needed (e.g., splitting full names, parsing addresses).",
            deps=self.deps
        )
        return result.output
    
    def fill_pdf_with_mappings(self, mappings: MappingResult, output_path: Path):
        """Fill PDF using the intelligent mappings"""
        reader = PdfReader(str(self.pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm is copied
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
        
        # Create field values from mappings
        field_values = {}
        for mapping in mappings.successful_mappings:
            if mapping.confidence_score >= 0.8:  # Only use high-confidence mappings
                field_values[mapping.pdf_field_name] = mapping.mapped_value
        
        # Update fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, field_values)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        return len(field_values)


async def main():
    """Main function to demonstrate intelligent semantic mapping"""
    print("🧠 INTELLIGENT SEMANTIC MAPPING FOR PA FORM FILLING")
    print("=" * 60)
    print("Solving the REAL challenge: mapping structured data to arbitrary form fields")
    print()
    
    mapper = IntelligentSemanticMapper(
        pdf_path=Path("pa.pdf"),
        json_path=Path("akshey_extracted.json")
    )
    
    print("🔍 Creating intelligent semantic mappings...")
    mappings = await mapper.create_semantic_mappings()
    
    print(f"\n📊 MAPPING RESULTS:")
    print(f"   Successful mappings: {len(mappings.successful_mappings)}")
    print(f"   Overall coverage: {mappings.overall_coverage:.1%}")
    print(f"   Unmapped PDF fields: {len(mappings.unmapped_pdf_fields)}")
    print(f"   Unused extracted data: {len(mappings.unused_extracted_data)}")
    
    print(f"\n✅ SUCCESSFUL MAPPINGS:")
    for mapping in mappings.successful_mappings:
        print(f"   {mapping.pdf_field_name} ← {mapping.extracted_data_path}")
        print(f"      Value: '{mapping.mapped_value}' (confidence: {mapping.confidence_score:.2f})")
        print(f"      Reasoning: {mapping.reasoning}")
        if mapping.transformation_applied:
            print(f"      Transformation: {mapping.transformation_applied}")
        print()
    
    if mappings.unmapped_pdf_fields:
        print(f"⚠️  UNMAPPED PDF FIELDS:")
        for field in mappings.unmapped_pdf_fields:
            print(f"   {field}")
    
    if mappings.recommendations:
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in mappings.recommendations:
            print(f"   • {rec}")
    
    # Fill the PDF
    filled_count = mapper.fill_pdf_with_mappings(mappings, Path("pa_semantic_mapped.pdf"))
    print(f"\n✅ PDF filled with {filled_count} semantic mappings")
    print(f"Output saved to: pa_semantic_mapped.pdf")
    
    return mappings


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
