"""
PHASE 0: Canonical Pydantic Schema
Freeze the schema that all downstream code must use
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class PatientDemographics(BaseModel):
    first_name: str
    last_name: str
    middle_initial: Optional[str] = None
    date_of_birth: str
    address: str
    city: str
    state: str
    zip_code: str
    phone_number: str
    email: Optional[str] = None
    weight_lbs: Optional[str] = None
    weight_kg: Optional[str] = None
    height_inches: Optional[str] = None
    height_cm: Optional[str] = None
    allergies: Optional[str] = None


class InsuranceInformation(BaseModel):
    payer_name: str
    member_id: str
    group_number: Optional[str] = None
    plan_type: Optional[str] = None
    insured_name: str
    has_other_coverage: Optional[bool] = None
    other_coverage_id: Optional[str] = None
    carrier_name: Optional[str] = None
    medicare_eligible: Optional[bool] = None
    medicare_id: Optional[str] = None
    medicaid_eligible: Optional[bool] = None
    medicaid_id: Optional[str] = None


class PrescriberInformation(BaseModel):
    first_name: str
    last_name: str
    degree: Optional[str] = None
    npi_number: str
    facility_name: str
    facility_address: str
    facility_city: str
    facility_state: str
    facility_zip: str
    phone: str
    fax: str
    email: Optional[str] = None
    specialty: Optional[str] = None
    state_license: Optional[str] = None
    dea_number: Optional[str] = None
    upin: Optional[str] = None


class DiagnosisInformation(BaseModel):
    primary_icd_code: str
    primary_diagnosis: str
    secondary_diagnoses: Optional[List[str]] = []
    date_of_diagnosis: Optional[str] = None


class MedicationInformation(BaseModel):
    drug_name: str
    ndc_number: Optional[str] = None
    dosage: str
    frequency: str
    route: str
    quantity: Optional[str] = None
    days_supply: Optional[str] = None
    directions: Optional[str] = None


class TreatmentHistory(BaseModel):
    is_new_therapy: bool
    is_continuation: bool
    date_of_last_treatment: Optional[str] = None
    previous_treatments: Optional[List[str]] = []
    contraindications: Optional[List[str]] = []
    failed_treatments: Optional[List[str]] = []


class LabsImaging(BaseModel):
    hemoglobin: Optional[str] = None
    wbc: Optional[str] = None
    platelets: Optional[str] = None
    creatinine: Optional[str] = None
    liver_function: Optional[str] = None
    recent_imaging: Optional[List[str]] = []


class DispensingInformation(BaseModel):
    place_of_administration: Optional[str] = None
    is_self_administered: Optional[bool] = None
    is_physician_office: Optional[bool] = None
    is_outpatient_infusion: Optional[bool] = None
    is_home_infusion: Optional[bool] = None
    infusion_center_name: Optional[str] = None
    infusion_center_phone: Optional[str] = None
    pharmacy_choice: Optional[str] = None
    specialty_pharmacy: Optional[str] = None


class CanonicalPAData(BaseModel):
    """
    Canonical Prior Authorization Data Schema v1
    All downstream code must use this exact schema
    """
    schema_version: str = Field(default="v1", description="Schema version for compatibility")
    extraction_timestamp: datetime = Field(default_factory=datetime.now)
    
    # Core sections
    patient_demographics: PatientDemographics
    insurance_information: InsuranceInformation
    prescriber_information: PrescriberInformation
    diagnosis_information: DiagnosisInformation
    medication_information: MedicationInformation
    treatment_history: TreatmentHistory
    labs_imaging: Optional[LabsImaging] = None
    dispensing_information: Optional[DispensingInformation] = None
    
    # Metadata
    form_type: Optional[str] = None
    payer_form_id: Optional[str] = None
    extraction_confidence: Optional[float] = None
    validation_notes: Optional[List[str]] = []
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


def convert_extraction_to_canonical(raw_extraction: Dict[str, Any]) -> CanonicalPAData:
    """Convert raw extraction JSON to canonical schema"""
    
    # Extract patient demographics
    demo = raw_extraction['tier_1_mandatory_fields']['patient_demographics']
    full_name = demo['full_name']['value']
    name_parts = full_name.split()
    
    # Parse address
    address = demo['address']['value']
    addr_parts = address.split(',')
    street = addr_parts[0].strip() if addr_parts else ''
    city = addr_parts[1].strip() if len(addr_parts) > 1 else ''
    
    # Parse state/zip from "VA-22407" format
    state_zip = addr_parts[2].strip() if len(addr_parts) > 2 else ''
    state = ''
    zip_code = ''
    if '-' in state_zip:
        state, zip_code = state_zip.split('-', 1)
        state = state.strip()
        zip_code = zip_code.strip()
    
    patient_demo = PatientDemographics(
        first_name=name_parts[0] if name_parts else '',
        last_name=name_parts[-1] if len(name_parts) > 1 else '',
        middle_initial=name_parts[1] if len(name_parts) > 2 else None,
        date_of_birth=demo['date_of_birth']['value'],
        address=street,
        city=city,
        state=state,
        zip_code=zip_code,
        phone_number=demo['phone_numbers']['value'][0] if demo['phone_numbers']['value'] else '',
        weight_lbs=demo['physical_measurements']['weight']['value'].split()[0],
        weight_kg=demo['physical_measurements']['weight']['value'].split(',')[1].strip().split()[0] if ',' in demo['physical_measurements']['weight']['value'] else '',
        height_inches=demo['physical_measurements']['height']['value'].split()[0],
        height_cm=str(int(float(demo['physical_measurements']['height']['value'].split()[0]) * 2.54)) if demo['physical_measurements']['height']['value'] else '',
        allergies=', '.join(raw_extraction['tier_2_clinical_justification']['treatment_history']['contraindications']['value']) if 'tier_2_clinical_justification' in raw_extraction and 'treatment_history' in raw_extraction['tier_2_clinical_justification'] and 'contraindications' in raw_extraction['tier_2_clinical_justification']['treatment_history'] else 'NKDA'
    )
    
    # Extract insurance
    ins = raw_extraction['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
    insurance = InsuranceInformation(
        payer_name=ins['payer_name']['value'],
        member_id=ins['member_id']['value'],
        group_number=ins.get('group_number', {}).get('value'),
        plan_type=ins.get('plan_type', {}).get('value'),
        insured_name=full_name
    )
    
    # Extract prescriber
    pres = raw_extraction['tier_1_mandatory_fields']['prescriber_information']
    prescriber_name = pres['physician_name']['value'].replace(', MD', '')
    pres_parts = prescriber_name.split()
    
    # Parse facility address
    fac_addr = pres['facility_address']['value']
    fac_parts = fac_addr.split(',')
    fac_street = fac_parts[0].strip() if fac_parts else ''
    
    # Parse "Leesburg VA 20176"
    fac_city_state_zip = fac_parts[1].strip() if len(fac_parts) > 1 else ''
    fac_parts_2 = fac_city_state_zip.split()
    fac_city = fac_parts_2[0] if fac_parts_2 else ''
    fac_state = fac_parts_2[1] if len(fac_parts_2) > 1 else ''
    fac_zip = fac_parts_2[2] if len(fac_parts_2) > 2 else ''
    
    prescriber = PrescriberInformation(
        first_name=pres_parts[0] if pres_parts else '',
        last_name=pres_parts[1] if len(pres_parts) > 1 else '',
        degree='MD',
        npi_number=pres['npi_number']['value'],
        facility_name=pres['facility_name']['value'],
        facility_address=fac_street,
        facility_city=fac_city,
        facility_state=fac_state,
        facility_zip=fac_zip,
        phone=pres['phone_fax']['value']['phone'],
        fax=pres['phone_fax']['value']['fax'],
        specialty='Gastroenterologist'
    )
    
    # Extract diagnosis
    diag = raw_extraction['tier_2_clinical_justification']['primary_diagnosis']
    diagnosis = DiagnosisInformation(
        primary_icd_code=diag['icd_code']['value'],
        primary_diagnosis=diag['diagnosis_description']['value']
    )
    
    # Extract medication - check if it exists
    if 'requested_medication' in raw_extraction:
        med = raw_extraction['requested_medication']
    else:
        # Create placeholder
        med = {
            'drug_name': {'value': 'Skyrizi'},
            'dosage': {'value': 'Per protocol'},
            'frequency': {'value': 'Per schedule'},
            'route': {'value': 'IV/SQ'}
        }
    medication = MedicationInformation(
        drug_name=med['drug_name']['value'],
        dosage=med['dosage']['value'],
        frequency=med['frequency']['value'],
        route=med['route']['value']
    )
    
    # Extract treatment history
    treatment = TreatmentHistory(
        is_new_therapy=True,  # Infer from context
        is_continuation=False,
        contraindications=raw_extraction['tier_2_clinical_justification']['treatment_history']['contraindications']['value']
    )
    
    return CanonicalPAData(
        patient_demographics=patient_demo,
        insurance_information=insurance,
        prescriber_information=prescriber,
        diagnosis_information=diagnosis,
        medication_information=medication,
        treatment_history=treatment,
        form_type="Aetna Skyrizi PA",
        extraction_confidence=0.95
    )


if __name__ == "__main__":
    # Test conversion
    import json
    
    with open('akshey_extracted.json', 'r') as f:
        raw_data = json.load(f)
    
    canonical = convert_extraction_to_canonical(raw_data)
    
    # Save canonical format
    with open('canonical_data.json', 'w') as f:
        json.dump(canonical.model_dump(), f, indent=2, default=str)
    
    print("✅ Canonical schema created and test data converted")
    print(f"Schema version: {canonical.schema_version}")
    print(f"Patient: {canonical.patient_demographics.first_name} {canonical.patient_demographics.last_name}")
    print(f"DOB: {canonical.patient_demographics.date_of_birth}")
    print(f"Insurance: {canonical.insurance_information.member_id}")
    print(f"Provider: {canonical.prescriber_information.first_name} {canonical.prescriber_information.last_name}")