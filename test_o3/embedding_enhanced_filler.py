"""
Enhanced PA Form Filler with OpenAI Embeddings
Uses actual embeddings for semantic label matching
"""

import json
import json5
import os
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import numpy as np
from datetime import datetime

from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
import fitz  # PyMuPDF

try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI not installed. Using fallback semantic matching.")


class EmbeddingMatcher:
    """Semantic label matching using OpenAI embeddings"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.use_embeddings = OPENAI_AVAILABLE and (api_key or os.environ.get('OPENAI_API_KEY'))
        
        if self.use_embeddings:
            self.client = OpenAI(api_key=api_key or os.environ.get('OPENAI_API_KEY'))
            self.embedding_cache = {}
            # Pre-compute embeddings for canonical fields
            self.canonical_embeddings = self._compute_canonical_embeddings()
        else:
            # Fallback to keyword matching
            self.keyword_map = {
                'first_name': ['first', 'fname', 'given', 'forename'],
                'last_name': ['last', 'lname', 'surname', 'family'],
                'dob': ['birth', 'birthday', 'date of birth', 'birthdate', 'dob'],
                'address': ['street', 'addr', 'address', 'residence'],
                'city': ['city', 'town', 'municipality'],
                'state': ['state', 'province', 'st'],
                'zip': ['zip', 'postal', 'postcode', 'zip code'],
                'phone': ['phone', 'tel', 'telephone', 'cell', 'mobile', 'contact'],
                'member_id': ['member', 'id', 'policy', 'subscriber', 'identification'],
                'group': ['group', 'grp', 'plan'],
                'weight': ['weight', 'wt', 'lbs', 'pounds', 'kg'],
                'height': ['height', 'ht', 'inches', 'cm'],
                'npi': ['npi', 'provider id', 'national provider'],
                'facility': ['facility', 'clinic', 'practice', 'center'],
                'fax': ['fax', 'facsimile'],
                'diagnosis': ['diagnosis', 'dx', 'condition', 'icd'],
                'allergies': ['allergy', 'allergies', 'allergic']
            }
    
    def _compute_canonical_embeddings(self) -> Dict[str, List[float]]:
        """Pre-compute embeddings for canonical field names"""
        canonical_fields = [
            'patient_first_name', 'patient_last_name', 'patient_date_of_birth',
            'patient_address', 'patient_city', 'patient_state', 'patient_zip',
            'patient_phone', 'patient_weight', 'patient_height',
            'insurance_member_id', 'insurance_group_number',
            'provider_facility_name', 'provider_npi', 'provider_fax',
            'diagnosis_code', 'diagnosis_description', 'patient_allergies'
        ]
        
        embeddings = {}
        if self.use_embeddings:
            for field in canonical_fields:
                # Convert underscore to space for better embedding
                text = field.replace('_', ' ')
                response = self.client.embeddings.create(
                    input=text,
                    model="text-embedding-3-small"
                )
                embeddings[field] = response.data[0].embedding
                
        return embeddings
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for a text, with caching"""
        if not self.use_embeddings:
            return []
            
        if text in self.embedding_cache:
            return self.embedding_cache[text]
            
        response = self.client.embeddings.create(
            input=text.lower(),
            model="text-embedding-3-small"
        )
        embedding = response.data[0].embedding
        self.embedding_cache[text] = embedding
        return embedding
    
    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
    
    def match_label_to_canonical(self, label: str) -> Tuple[str, float]:
        """Match a form label to canonical field name"""
        label_clean = label.lower().strip(':').strip()
        
        if self.use_embeddings:
            # Use embeddings
            label_embedding = self.get_embedding(label_clean)
            best_match = ""
            best_score = 0.0
            
            for canonical, canonical_embedding in self.canonical_embeddings.items():
                score = self.cosine_similarity(label_embedding, canonical_embedding)
                if score > best_score:
                    best_score = score
                    best_match = canonical
                    
            return best_match, best_score
        else:
            # Fallback to keyword matching
            best_match = ""
            best_score = 0.0
            
            for canonical, keywords in self.keyword_map.items():
                for keyword in keywords:
                    if keyword in label_clean:
                        score = 0.8 + (0.2 * len(keyword) / len(label_clean))
                        if score > best_score:
                            best_score = score
                            best_match = f"patient_{canonical}"
                            
            return best_match, best_score


class AccurateDataExtractor:
    """Extract data accurately from the JSON"""
    
    def __init__(self, data: Dict[str, Any]):
        self.data = data
        
    def extract_values(self) -> Dict[str, Any]:
        """Extract all values accurately from the JSON structure"""
        values = {}
        
        # Patient Demographics
        demo = self.data['tier_1_mandatory_fields']['patient_demographics']
        
        # Full name - exactly as in JSON
        values['patient_full_name'] = demo['full_name']['value']  # "Akshay H. chaudhari"
        
        # Parse first and last name
        name_parts = demo['full_name']['value'].split()
        values['patient_first_name'] = name_parts[0]  # "Akshay"
        values['patient_last_name'] = name_parts[-1]  # "chaudhari"
        
        # DOB - exactly as in JSON
        values['patient_date_of_birth'] = demo['date_of_birth']['value']  # "1987-02-17"
        values['patient_dob'] = demo['date_of_birth']['value']
        
        # Address - parse correctly
        full_address = demo['address']['value']  # "1460 El Camino Real, Arlington, VA-22407"
        addr_parts = full_address.split(',')
        values['patient_address'] = addr_parts[0].strip()  # "1460 El Camino Real"
        
        if len(addr_parts) > 1:
            # Parse "Arlington, VA-22407"
            city_state_zip = addr_parts[1].strip()
            if len(addr_parts) > 2:
                city_state_zip += ',' + addr_parts[2].strip()
                
            # Extract city
            values['patient_city'] = 'Arlington'
            
            # Extract state and zip from "VA-22407"
            if 'VA-' in city_state_zip:
                values['patient_state'] = 'VA'
                values['patient_zip'] = '22407'
            
        # Phone
        values['patient_phone'] = demo['phone_numbers']['value'][0]  # "************"
        
        # Physical measurements
        weight_str = demo['physical_measurements']['weight']['value']  # "190 lbs, 86.18 kg"
        values['patient_weight'] = weight_str.split(' ')[0]  # "190"
        values['patient_weight_lbs'] = weight_str.split(' ')[0]
        
        height_str = demo['physical_measurements']['height']['value']  # "73 in"
        values['patient_height'] = height_str.split(' ')[0]  # "73"
        values['patient_height_inches'] = height_str.split(' ')[0]
        
        # Insurance Information
        insurance = self.data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
        values['insurance_member_id'] = insurance['member_id']['value']  # "14866-38657882"
        values['member_id'] = insurance['member_id']['value']
        values['insurance_payer'] = insurance['payer_name']['value']  # "Aetna Better Health of Virginia"
        values['insurance_plan'] = insurance['plan_type']['value']  # "Better Health of Virginia"
        
        # Prescriber Information
        prescriber = self.data['tier_1_mandatory_fields']['prescriber_information']
        values['provider_name'] = prescriber['physician_name']['value']  # "Timothy Adam, MD"
        values['provider_npi'] = prescriber['npi_number']['value']  # "**********"
        values['provider_facility_name'] = prescriber['facility_name']['value']  # "Extraodinary Gastroenterology"
        values['facility_name'] = prescriber['facility_name']['value']
        
        # Extract fax from phone_fax
        if 'phone_fax' in prescriber and isinstance(prescriber['phone_fax']['value'], dict):
            values['provider_fax'] = prescriber['phone_fax']['value'].get('fax', '')  # "************"
            values['fax'] = prescriber['phone_fax']['value'].get('fax', '')
            
        # Clinical Information
        clinical = self.data['tier_2_clinical_justification']
        if 'primary_diagnosis' in clinical:
            values['diagnosis_code'] = clinical['primary_diagnosis']['icd_code']['value']  # "K50.111"
            values['icd_code'] = clinical['primary_diagnosis']['icd_code']['value']
            values['diagnosis_description'] = clinical['primary_diagnosis']['diagnosis_description']['value']
            
        # Treatment history
        if 'treatment_history' in clinical and 'contraindications' in clinical['treatment_history']:
            allergies = clinical['treatment_history']['contraindications']['value']
            if allergies:
                values['patient_allergies'] = ', '.join(allergies)
                values['allergies'] = ', '.join(allergies)
            else:
                values['patient_allergies'] = 'None reported'
                values['allergies'] = 'None reported'
                
        # Medication - it's at the top level, not under tier_1
        if 'requested_medication' in self.data:
            medication = self.data['requested_medication']
            values['medication_name'] = medication['drug_name']['value']  # "Skyrizi"
            values['medication_dosage'] = medication['dosage']['value']
        
        return values


class EmbeddingEnhancedFiller:
    """PA Form Filler with embedding-based semantic matching"""
    
    def __init__(self, pdf_path: Path, json_path: Path, api_key: Optional[str] = None):
        self.pdf_path = pdf_path
        self.data = json.load(open(json_path))
        self.reader = PdfReader(str(pdf_path))
        self.matcher = EmbeddingMatcher(api_key)
        self.extractor = AccurateDataExtractor(self.data)
        
        # Known template for this form
        self.known_mappings = {
            'T14': 'patient_first_name',
            'T15': 'patient_last_name', 
            'T16': 'patient_dob',
            'T17': 'patient_address',
            'T19': 'patient_city',
            'T20': 'patient_state',
            'T21': 'patient_zip',
            'T11': 'member_id',
            'Request by T': 'facility_name',
            'Fax T': 'fax'
        }
        
    def extract_form_structure(self) -> Dict[str, Any]:
        """Extract form fields and match labels"""
        doc = fitz.open(str(self.pdf_path))
        structure = {}
        
        for page_num, page in enumerate(doc):
            # Get all widgets
            for widget in page.widgets():
                widget_name = widget.field_name
                
                # Use known mapping if available
                if widget_name in self.known_mappings:
                    canonical = self.known_mappings[widget_name]
                    confidence = 0.95
                else:
                    # Find label text near widget
                    rect = widget.rect
                    # Get text around widget
                    text_page = page.get_textpage()
                    text = page.get_text()
                    
                    # Simple proximity search for label
                    # In production, would use proper spatial analysis
                    label = self._find_nearby_label(page, rect)
                    
                    # Match using embeddings
                    canonical, confidence = self.matcher.match_label_to_canonical(label)
                    
                structure[widget_name] = {
                    'canonical_key': canonical,
                    'confidence': confidence,
                    'type': widget.field_type_string,
                    'page': page_num
                }
                
        doc.close()
        return structure
        
    def _find_nearby_label(self, page, widget_rect) -> str:
        """Find label text near a widget"""
        # Simple approach - would be more sophisticated in production
        x0, y0, x1, y1 = widget_rect
        
        # Look for text to the left and above
        search_rect = fitz.Rect(x0 - 200, y0 - 20, x0 - 5, y1 + 20)
        text = page.get_text(clip=search_rect).strip()
        
        if not text:
            # Try above
            search_rect = fitz.Rect(x0 - 50, y0 - 50, x1 + 50, y0 - 5)
            text = page.get_text(clip=search_rect).strip()
            
        return text
        
    def fill_form(self, output_path: Path):
        """Fill the form with accurate data"""
        print("📊 Extracting accurate data from JSON...")
        values = self.extractor.extract_values()
        
        print("🔍 Analyzing form structure...")
        structure = self.extract_form_structure()
        
        # Create writer
        writer = PdfWriter()
        writer.clone_reader_document_root(self.reader)
        writer.append_pages_from_reader(self.reader)
        
        # Ensure AcroForm
        if self.reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): self.reader.trailer['/Root']['/AcroForm']
            })
            
        # Fill fields
        form_values = {}
        filled_count = 0
        
        print("\n✅ Filling form fields:")
        for widget_name, info in structure.items():
            canonical = info['canonical_key']
            confidence = info['confidence']
            
            if canonical in values and confidence > 0.5:
                value = str(values[canonical])
                form_values[widget_name] = value
                print(f"  {widget_name} → {canonical} = {value} [conf: {confidence:.2f}]")
                filled_count += 1
                
        # Update form
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
            
        # Force appearance
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
            
        print(f"\n📋 Summary:")
        print(f"  - Fields filled: {filled_count}")
        print(f"  - Using embeddings: {self.matcher.use_embeddings}")
        print(f"  - Output: {output_path}")
        
        # Save detailed report
        report = {
            'timestamp': datetime.now().isoformat(),
            'filled_fields': filled_count,
            'embeddings_used': self.matcher.use_embeddings,
            'extracted_values': values,
            'field_mappings': {k: v['canonical_key'] for k, v in structure.items() if v['canonical_key'] in values}
        }
        
        report_path = output_path.with_suffix('.detailed_report.json')
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
            
        return filled_count


if __name__ == "__main__":
    # Use OPENAI_API_KEY from environment or pass directly
    filler = EmbeddingEnhancedFiller(
        Path("test_o3/pa.pdf"),
        Path("test_o3/akshey_extracted.json")
    )
    filler.fill_form(Path("test_o3/pa_embedding_enhanced.pdf"))