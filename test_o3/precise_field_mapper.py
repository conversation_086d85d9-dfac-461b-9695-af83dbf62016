"""
PRECISE Field Mapper - Use visual inspection to map exact field positions
"""

import json
from pathlib import Path
from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject

from canonical_schema import convert_extraction_to_canonical


class PreciseFieldMapper:
    """Map fields using known visual positions from the screenshot"""
    
    def __init__(self):
        # Based on visual inspection of the PDF form
        # This is the EXACT mapping from screenshot analysis
        self.precise_mapping = {
            # Section A - Patient Information (visual order top to bottom, left to right)
            'T14': 'first_name',           # First Name (row 1, col 1)
            'T15': 'last_name',            # Last Name (row 1, col 2) 
            'T16': 'dob',                  # DOB (row 1, col 3)
            'T17': 'address',              # Address (row 2, col 1)
            'T19': 'city',                 # City (row 2, col 2)
            'T20': 'state',                # State (row 2, col 3)
            'T21': 'zip',                  # ZIP (row 2, col 4)
            'T21B': 'home_phone',          # Home Phone (row 3, col 1)
            'T21C': 'work_phone',          # Work Phone (row 3, col 2)
            'T21D': 'cell_phone',          # Cell Phone (row 3, col 3)
            'T21E': 'email',               # Email (row 3, col 4)
            'T21F': 'weight_lbs',          # Weight lbs (row 4, col 1)
            'Insurance Info T.1': 'weight_kgs',     # Weight kgs (row 4, col 2)
            'Insurance Info T.2': 'height_inches',  # Height inches (row 4, col 3)
            'Insurance Info T.6': 'height_cms',     # Height cms (row 4, col 4)
            'Insurance Info T.7': 'allergies',      # Allergies (row 4, col 5)
            
            # Section B - Insurance Information
            'T11': 'member_id',            # Aetna Member ID
            'T12': 'group_number',         # Group #
            'T18': 'insured_name',         # Insured
            'T13': 'insured_name_alt',     # Insured (alternate)
            
            # Section C - Prescriber Information  
            'Presc Info T.1': 'prescriber_first_name',  # First Name
            'Presc Info T.2': 'prescriber_last_name',   # Last Name
            'Presc Info T.17': 'prescriber_address',    # Address
            'Presc Info T.8': 'prescriber_city',        # City
            'Presc Info T.15': 'prescriber_state',      # State
            'Presc Info T.19': 'prescriber_zip',        # ZIP
            'Presc Info T.11': 'prescriber_npi',        # NPI
            'Presc Info T.15': 'prescriber_dea',        # DEA (if different field)
            
            # Header fields
            'Request by T': 'facility_name',
            'Phone T': 'facility_phone', 
            'Fax T': 'facility_fax',
            
            # Diagnosis
            'Primary ICD Code': 'icd_code',
            'Diagnosis Text': 'diagnosis_description'
        }
        
    def extract_precise_values(self, canonical_data) -> dict:
        """Extract values with precise mapping"""
        
        # Convert to dict for easier access
        data = canonical_data.model_dump()
        
        values = {}
        
        # Patient demographics
        demo = data['patient_demographics']
        values['first_name'] = demo['first_name']
        values['last_name'] = demo['last_name'] 
        values['dob'] = demo['date_of_birth']
        values['address'] = demo['address']
        values['city'] = demo['city']
        values['state'] = demo['state']
        values['zip'] = demo['zip_code']
        values['home_phone'] = demo['phone_number']
        values['work_phone'] = demo['phone_number']
        values['cell_phone'] = demo['phone_number']
        values['email'] = demo.get('email', '')
        values['weight_lbs'] = demo.get('weight_lbs', '')
        values['weight_kgs'] = demo.get('weight_kg', '')
        values['height_inches'] = demo.get('height_inches', '')
        values['height_cms'] = demo.get('height_cm', '')
        values['allergies'] = demo.get('allergies', 'NKDA')
        
        # Calculate height in cm if missing
        if not values['height_cms'] and values['height_inches']:
            height_cm = int(float(values['height_inches']) * 2.54)
            values['height_cms'] = str(height_cm)
        
        # Insurance 
        ins = data['insurance_information']
        values['member_id'] = ins['member_id']
        values['group_number'] = ins.get('group_number', '')
        values['insured_name'] = ins['insured_name']
        values['insured_name_alt'] = ins['insured_name']
        
        # Prescriber
        pres = data['prescriber_information']
        values['prescriber_first_name'] = pres['first_name']
        values['prescriber_last_name'] = pres['last_name']
        values['prescriber_address'] = pres['facility_address']
        values['prescriber_city'] = pres['facility_city']
        values['prescriber_state'] = pres['facility_state'] 
        values['prescriber_zip'] = pres['facility_zip']
        values['prescriber_npi'] = pres['npi_number']
        values['prescriber_dea'] = pres.get('dea_number', '')
        
        # Facility
        values['facility_name'] = pres['facility_name']
        values['facility_phone'] = pres['phone']
        values['facility_fax'] = pres['fax']
        
        # Clinical
        diag = data['diagnosis_information']
        values['icd_code'] = diag['primary_icd_code']
        values['diagnosis_description'] = diag['primary_diagnosis']
        
        return values
        
    def fill_form_precisely(self, pdf_path: Path, canonical_data, output_path: Path):
        """Fill form with precise field mapping"""
        
        print("🎯 PRECISE FORM FILLING")
        print("=" * 60)
        
        # Get precise values
        values = self.extract_precise_values(canonical_data)
        
        # Load PDF
        reader = PdfReader(str(pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
            
        # Fill using precise mapping
        form_values = {}
        filled_count = 0
        
        print("✍️ Filling with PRECISE mapping:")
        print("-" * 60)
        
        for pdf_field, data_key in self.precise_mapping.items():
            if data_key in values and values[data_key]:
                form_values[pdf_field] = str(values[data_key])
                print(f"✅ {pdf_field:<25} = {values[data_key]}")
                filled_count += 1
            else:
                print(f"❌ {pdf_field:<25} = MISSING ({data_key})")
                
        # Update form
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
            
        # Force appearance
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
            
        print(f"\n📊 PRECISE FILL SUMMARY:")
        print(f"  Fields mapped: {len(self.precise_mapping)}")
        print(f"  Fields filled: {filled_count}")
        print(f"  Success rate: {filled_count/len(self.precise_mapping):.1%}")
        print(f"  Output: {output_path}")
        
        # Debug: Show what was actually filled
        debug_info = {
            'patient_name': f"{values.get('first_name', '')} {values.get('last_name', '')}",
            'dob': values.get('dob', ''),
            'address': f"{values.get('address', '')}, {values.get('city', '')}, {values.get('state', '')} {values.get('zip', '')}",
            'member_id': values.get('member_id', ''),
            'prescriber': f"{values.get('prescriber_first_name', '')} {values.get('prescriber_last_name', '')}",
            'npi': values.get('prescriber_npi', ''),
            'facility': values.get('facility_name', '')
        }
        
        print(f"\n🔍 DEBUG - Values extracted:")
        for key, val in debug_info.items():
            print(f"  {key}: {val}")
            
        return filled_count


if __name__ == "__main__":
    # Load canonical data
    with open('akshey_extracted.json', 'r') as f:
        raw_data = json.load(f)
        
    canonical = convert_extraction_to_canonical(raw_data)
    
    # Fill with precise mapping
    mapper = PreciseFieldMapper()
    mapper.fill_form_precisely(
        Path('pa.pdf'),
        canonical,
        Path('pa_precise_filled.pdf')
    )