"""
PHASE 3: Production Pipeline - Fill & Flatten with Audit Trail
Complete implementation following the plan:
Referral JSON → Canonical Schema → Template Mapping → Fill → Flatten → Audit
"""

import json
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime

from PyPDF2 import <PERSON>d<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject

from canonical_schema import CanonicalPAData, convert_extraction_to_canonical


class ProductionPipeline:
    """Complete production pipeline implementing the plan"""
    
    def __init__(self, template_path: Path):
        self.template_path = template_path
        self.load_template()
        
    def load_template(self):
        """Load form template"""
        with open(self.template_path, 'r') as f:
            self.template = json.load(f)
        print(f"✅ Loaded template: {self.template['template_metadata']['form_name']} v{self.template['template_metadata']['version']}")
        
    def extract_values_from_canonical(self, canonical_data: CanonicalPAData) -> Dict[str, str]:
        """Extract values from canonical data based on template mapping"""
        values = {}
        
        # Convert canonical data to dict for easy access
        data_dict = canonical_data.model_dump()
        
        for field_name, mapping in self.template['field_mappings'].items():
            canonical_path = mapping['canonical_field']
            confidence = mapping['confidence']
            
            # Skip low confidence mappings
            if confidence < 0.75:
                continue
                
            # Navigate to the value using dot notation
            parts = canonical_path.split('.')
            current = data_dict
            
            try:
                for part in parts:
                    current = current[part]
                    
                if current is not None:
                    values[field_name] = str(current)
                    
            except (KeyError, TypeError):
                # Field not found in data
                continue
                
        return values
        
    def fill_form(self, pdf_path: Path, canonical_data: CanonicalPAData, output_path: Path) -> Dict[str, Any]:
        """Fill PDF form with canonical data"""
        
        print("🔄 Starting form filling process...")
        
        # Extract values using template
        values = self.extract_values_from_canonical(canonical_data)
        
        print(f"📊 Extracted {len(values)} values from canonical data")
        
        # Load PDF
        reader = PdfReader(str(pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm is present
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
        
        # Fill form fields
        filled_fields = {}
        fill_errors = []
        
        print("\\n✍️ Filling form fields:")
        print("-" * 80)
        
        for field_name, value in values.items():
            try:
                filled_fields[field_name] = value
                
                # Get mapping info for confidence
                mapping = self.template['field_mappings'][field_name]
                canonical_field = mapping['canonical_field']
                confidence = mapping['confidence']
                
                print(f"✅ {field_name:<25} ← {canonical_field:<35} = {value:<20} [conf: {confidence:.2f}]")
                
            except Exception as e:
                fill_errors.append(f"{field_name}: {str(e)}")
                print(f"❌ {field_name:<25} ERROR: {str(e)}")
        
        # Update form fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, filled_fields)
            
        # Force appearance generation
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save filled PDF
        with open(output_path, 'wb') as f:
            writer.write(f)
            
        # Create audit record
        audit = {
            'timestamp': datetime.now().isoformat(),
            'pipeline_version': 'v1.0',
            'template_used': self.template['template_metadata']['form_name'],
            'template_version': self.template['template_metadata']['version'],
            'input_pdf': str(pdf_path),
            'output_pdf': str(output_path),
            'canonical_schema_version': canonical_data.schema_version,
            'fields_filled': len(filled_fields),
            'total_template_fields': len(self.template['field_mappings']),
            'fill_success_rate': len(filled_fields) / len(self.template['field_mappings']),
            'fields_detail': {
                field_name: {
                    'value': value,
                    'canonical_source': self.template['field_mappings'][field_name]['canonical_field'],
                    'confidence': self.template['field_mappings'][field_name]['confidence']
                }
                for field_name, value in filled_fields.items()
            },
            'errors': fill_errors,
            'patient_info': {
                'name': f"{canonical_data.patient_demographics.first_name} {canonical_data.patient_demographics.last_name}",
                'dob': canonical_data.patient_demographics.date_of_birth,
                'member_id': canonical_data.insurance_information.member_id
            }
        }
        
        print(f"\\n📋 Form Fill Summary:")
        print(f"  Fields filled: {len(filled_fields)}/{len(self.template['field_mappings'])}")
        print(f"  Success rate: {audit['fill_success_rate']:.1%}")
        print(f"  Errors: {len(fill_errors)}")
        
        return audit
        
    def flatten_pdf(self, pdf_path: Path, output_path: Path) -> bool:
        """Flatten PDF to make it fax-ready"""
        
        print(f"🔄 Flattening PDF for fax compatibility...")
        
        try:
            # Try pdftk first
            result = subprocess.run([
                'pdftk', str(pdf_path), 'output', str(output_path), 'flatten'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ PDF flattened using pdftk: {output_path}")
                return True
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("⚠️ pdftk not available, trying ghostscript...")
            
        try:
            # Try Ghostscript
            result = subprocess.run([
                'gs', '-dNOPAUSE', '-dBATCH', '-sDEVICE=pdfwrite',
                '-dCompatibilityLevel=1.4', '-dPDFSETTINGS=/printer',
                f'-sOutputFile={output_path}', str(pdf_path)
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ PDF flattened using ghostscript: {output_path}")
                return True
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("⚠️ ghostscript not available")
            
        # Fallback: just copy the file
        import shutil
        shutil.copy2(pdf_path, output_path)
        print(f"⚠️ PDF copied without flattening: {output_path}")
        return False
        
    def run_complete_pipeline(self, 
                            raw_extraction_path: Path,
                            blank_pdf_path: Path, 
                            output_dir: Path) -> Dict[str, Any]:
        """Run the complete pipeline: JSON → Canonical → Fill → Flatten → Audit"""
        
        output_dir.mkdir(exist_ok=True)
        
        print("🚀 Starting Production Pipeline")
        print("=" * 80)
        
        # Step 1: Load raw extraction
        print("1️⃣ Loading raw extraction JSON...")
        with open(raw_extraction_path, 'r') as f:
            raw_extraction = json.load(f)
            
        # Step 2: Convert to canonical schema
        print("2️⃣ Converting to canonical schema...")
        canonical_data = convert_extraction_to_canonical(raw_extraction)
        
        # Save canonical for audit
        canonical_path = output_dir / "canonical_data.json"
        with open(canonical_path, 'w') as f:
            json.dump(canonical_data.model_dump(), f, indent=2, default=str)
        
        # Step 3: Fill form
        print("3️⃣ Filling PDF form...")
        filled_path = output_dir / "filled_form.pdf"
        audit = self.fill_form(blank_pdf_path, canonical_data, filled_path)
        
        # Step 4: Flatten for fax
        print("4️⃣ Flattening PDF...")
        flattened_path = output_dir / "flattened_form.pdf"
        flatten_success = self.flatten_pdf(filled_path, flattened_path)
        audit['flattened'] = flatten_success
        audit['flattened_path'] = str(flattened_path)
        
        # Step 5: Save audit
        print("5️⃣ Saving audit trail...")
        audit_path = output_dir / "audit_trail.json"
        with open(audit_path, 'w') as f:
            json.dump(audit, f, indent=2)
            
        # Step 6: Generate summary report
        print("6️⃣ Generating summary report...")
        report_path = output_dir / "pipeline_report.md"
        self.generate_summary_report(audit, canonical_data, report_path)
        
        print("\\n" + "=" * 80)
        print("🎉 Pipeline Complete!")
        print("=" * 80)
        print(f"📁 Output directory: {output_dir}")
        print(f"📄 Filled form: {filled_path}")
        print(f"📠 Fax-ready form: {flattened_path}")
        print(f"📊 Audit trail: {audit_path}")
        print(f"📋 Summary report: {report_path}")
        
        return audit
        
    def generate_summary_report(self, audit: Dict, canonical_data: CanonicalPAData, output_path: Path):
        """Generate human-readable summary report"""
        
        report = f"""# Prior Authorization Form Fill Report
        
## Pipeline Summary
- **Timestamp**: {audit['timestamp']}
- **Template**: {audit['template_used']} v{audit['template_version']}
- **Success Rate**: {audit['fill_success_rate']:.1%} ({audit['fields_filled']}/{audit['total_template_fields']} fields)
- **Schema Version**: {audit['canonical_schema_version']}

## Patient Information
- **Name**: {canonical_data.patient_demographics.first_name} {canonical_data.patient_demographics.last_name}
- **DOB**: {canonical_data.patient_demographics.date_of_birth}
- **Address**: {canonical_data.patient_demographics.address}, {canonical_data.patient_demographics.city}, {canonical_data.patient_demographics.state} {canonical_data.patient_demographics.zip_code}
- **Phone**: {canonical_data.patient_demographics.phone_number}

## Insurance Information  
- **Payer**: {canonical_data.insurance_information.payer_name}
- **Member ID**: {canonical_data.insurance_information.member_id}
- **Insured**: {canonical_data.insurance_information.insured_name}

## Prescriber Information
- **Provider**: {canonical_data.prescriber_information.first_name} {canonical_data.prescriber_information.last_name}, {canonical_data.prescriber_information.degree}
- **NPI**: {canonical_data.prescriber_information.npi_number}
- **Facility**: {canonical_data.prescriber_information.facility_name}
- **Phone**: {canonical_data.prescriber_information.phone}
- **Fax**: {canonical_data.prescriber_information.fax}

## Clinical Information
- **Diagnosis**: {canonical_data.diagnosis_information.primary_diagnosis} ({canonical_data.diagnosis_information.primary_icd_code})
- **Medication**: {canonical_data.medication_information.drug_name}
- **New Therapy**: {canonical_data.treatment_history.is_new_therapy}

## Fields Filled Successfully
"""
        
        for field_name, details in audit['fields_detail'].items():
            report += f"- **{field_name}**: {details['value']} (conf: {details['confidence']:.2f})\\n"
            
        if audit['errors']:
            report += "\\n## Errors\\n"
            for error in audit['errors']:
                report += f"- {error}\\n"
                
        report += f"""
## Files Generated
- **Filled PDF**: {audit['output_pdf']}
- **Flattened PDF**: {audit.get('flattened_path', 'N/A')}
- **Canonical Data**: canonical_data.json
- **Audit Trail**: audit_trail.json

---
*Generated by Production Pipeline v1.0*
"""
        
        with open(output_path, 'w') as f:
            f.write(report)


if __name__ == "__main__":
    # Run the complete pipeline
    pipeline = ProductionPipeline(Path("templates/aetna_skyrizi_v1.json"))
    
    result = pipeline.run_complete_pipeline(
        raw_extraction_path=Path("akshey_extracted.json"),
        blank_pdf_path=Path("pa.pdf"),
        output_dir=Path("production_output")
    )