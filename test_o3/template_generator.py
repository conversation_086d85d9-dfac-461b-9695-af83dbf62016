"""
PHASE 1B: Template Generation with Embeddings
Create precise form template using known field mappings and embeddings for validation
"""

import json
import yaml
import os
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from canonical_schema import CanonicalPAData


class TemplateGenerator:
    """Generate form templates with embedding-based semantic matching"""
    
    def __init__(self, form_name: str = "aetna_skyrizi"):
        self.form_name = form_name
        self.use_embeddings = OPENAI_AVAILABLE and os.environ.get('OPENAI_API_KEY')
        
        if self.use_embeddings:
            self.client = OpenAI()
        
        # Canonical field mapping based on schema structure
        self.canonical_fields = [
            'patient_demographics.first_name',
            'patient_demographics.last_name', 
            'patient_demographics.date_of_birth',
            'patient_demographics.address',
            'patient_demographics.city',
            'patient_demographics.state',
            'patient_demographics.zip_code',
            'patient_demographics.phone_number',
            'patient_demographics.email',
            'patient_demographics.weight_lbs',
            'patient_demographics.weight_kg',
            'patient_demographics.height_inches',
            'patient_demographics.height_cm',
            'patient_demographics.allergies',
            'insurance_information.member_id',
            'insurance_information.group_number',
            'insurance_information.insured_name',
            'insurance_information.payer_name',
            'prescriber_information.first_name',
            'prescriber_information.last_name',
            'prescriber_information.npi_number',
            'prescriber_information.facility_name',
            'prescriber_information.facility_address',
            'prescriber_information.facility_city',
            'prescriber_information.facility_state',
            'prescriber_information.facility_zip',
            'prescriber_information.phone',
            'prescriber_information.fax',
            'prescriber_information.specialty',
            'diagnosis_information.primary_icd_code',
            'diagnosis_information.primary_diagnosis',
            'medication_information.drug_name',
            'medication_information.dosage',
            'treatment_history.is_new_therapy',
            'treatment_history.is_continuation'
        ]
        
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """Get OpenAI embedding for text"""
        if not self.use_embeddings:
            return None
            
        try:
            response = self.client.embeddings.create(
                input=text.lower(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"Embedding error: {e}")
            return None
            
    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity"""
        import math
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = math.sqrt(sum(a * a for a in vec1))
        magnitude2 = math.sqrt(sum(b * b for b in vec2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0
        
        return dot_product / (magnitude1 * magnitude2)
        
    def match_field_to_canonical(self, field_name: str, field_label: str = "") -> tuple[str, float]:
        """Match form field to canonical schema field"""
        
        # Manual mappings for known fields (high confidence)
        manual_mappings = {
            'T14': ('patient_demographics.first_name', 0.95),
            'T15': ('patient_demographics.last_name', 0.95),
            'T16': ('patient_demographics.date_of_birth', 0.95),
            'T17': ('patient_demographics.address', 0.95),
            'T19': ('patient_demographics.city', 0.95),
            'T20': ('patient_demographics.state', 0.95),
            'T21': ('patient_demographics.zip_code', 0.95),
            'T21B': ('patient_demographics.phone_number', 0.90),
            'T21C': ('patient_demographics.phone_number', 0.85),
            'T21D': ('patient_demographics.phone_number', 0.85),
            'T21E': ('patient_demographics.email', 0.90),
            'T21F': ('patient_demographics.weight_lbs', 0.90),
            'T11': ('insurance_information.member_id', 0.95),
            'T12': ('insurance_information.group_number', 0.90),
            'T18': ('insurance_information.insured_name', 0.90),
            'T13': ('insurance_information.insured_name', 0.85),
            'Request by T': ('prescriber_information.facility_name', 0.95),
            'Phone T': ('prescriber_information.phone', 0.95),
            'Fax T': ('prescriber_information.fax', 0.95),
            'Primary ICD Code': ('diagnosis_information.primary_icd_code', 0.95),
            'Diagnosis Text': ('diagnosis_information.primary_diagnosis', 0.95),
            
            # Additional mappings from inspection
            'Insurance Info T.1': ('patient_demographics.weight_kg', 0.80),
            'Insurance Info T.2': ('patient_demographics.height_inches', 0.80),
            'Insurance Info T.6': ('patient_demographics.height_cm', 0.80),
            'Insurance Info T.7': ('patient_demographics.allergies', 0.85),
            
            # Prescriber fields
            'Presc Info T.11': ('prescriber_information.npi_number', 0.90),
            'Presc Info T.1': ('prescriber_information.first_name', 0.85),
            'Presc Info T.2': ('prescriber_information.last_name', 0.85),
            'Presc Info T.17': ('prescriber_information.facility_address', 0.80),
            'Presc Info T.8': ('prescriber_information.facility_city', 0.80),
            'Presc Info T.15': ('prescriber_information.facility_state', 0.80),
            'Presc Info T.19': ('prescriber_information.facility_zip', 0.80),
            
            # Clinical checkboxes for therapy type
            'Clinical CB.1': ('treatment_history.is_new_therapy', 0.85),
            'Clinical CB.2': ('treatment_history.is_continuation', 0.85)
        }
        
        # Check manual mapping first
        if field_name in manual_mappings:
            return manual_mappings[field_name]
            
        # Use embeddings for unknown fields
        if self.use_embeddings and field_label:
            search_text = f"{field_name} {field_label}"
            field_embedding = self.get_embedding(search_text)
            
            if field_embedding:
                best_match = ""
                best_score = 0.0
                
                for canonical_field in self.canonical_fields:
                    canonical_embedding = self.get_embedding(canonical_field.replace('_', ' ').replace('.', ' '))
                    
                    if canonical_embedding:
                        score = self.cosine_similarity(field_embedding, canonical_embedding)
                        if score > best_score:
                            best_score = score
                            best_match = canonical_field
                            
                return best_match, best_score
                
        return "", 0.0
        
    def generate_template(self, widget_discovery_path: Path) -> Dict:
        """Generate template from widget discovery results"""
        
        # Load widget discovery
        with open(widget_discovery_path, 'r') as f:
            discovery = json.load(f)
            
        template = {
            'template_metadata': {
                'form_name': self.form_name,
                'version': 'v1',
                'created': datetime.now().isoformat(),
                'schema_version': 'v1',
                'total_fields': len(discovery['widget_mappings']),
                'confidence_threshold': 0.55
            },
            'field_mappings': {}
        }
        
        mapped_count = 0
        high_confidence_count = 0
        
        print(f"🗺️ Generating template for {self.form_name}")
        print("-" * 80)
        
        for field_name, widget_info in discovery['widget_mappings'].items():
            label = widget_info['label']
            
            # Map to canonical field
            canonical_field, confidence = self.match_field_to_canonical(field_name, label)
            
            if canonical_field and confidence > 0.5:
                template['field_mappings'][field_name] = {
                    'canonical_field': canonical_field,
                    'confidence': confidence,
                    'needs_review': confidence < 0.8,
                    'field_type': widget_info['field_type'],
                    'page': widget_info['page'],
                    'original_label': label
                }
                
                mapped_count += 1
                if confidence >= 0.8:
                    high_confidence_count += 1
                    
                status = "✅" if confidence >= 0.8 else "⚠️"
                print(f"{status} {field_name:<25} → {canonical_field:<40} [conf: {confidence:.2f}]")
            else:
                print(f"❌ {field_name:<25} → UNMAPPED")
                
        template['template_metadata']['mapped_fields'] = mapped_count
        template['template_metadata']['high_confidence_fields'] = high_confidence_count
        
        print("\n" + "=" * 80)
        print(f"📊 Template Generation Summary:")
        print(f"  Total fields: {len(discovery['widget_mappings'])}")
        print(f"  Mapped fields: {mapped_count}")
        print(f"  High confidence: {high_confidence_count}")
        print(f"  Needs review: {mapped_count - high_confidence_count}")
        
        return template
        
    def save_template(self, template: Dict, output_dir: Path):
        """Save template in both JSON and YAML formats"""
        output_dir.mkdir(exist_ok=True)
        
        # Save as JSON
        json_path = output_dir / f"{self.form_name}_v1.json"
        with open(json_path, 'w') as f:
            json.dump(template, f, indent=2)
            
        # Save as YAML (more readable)
        yaml_path = output_dir / f"{self.form_name}_v1.yaml"
        
        # Create simplified YAML format
        yaml_template = {
            '# Aetna Skyrizi PA Form Template v1': None,
            '# Generated automatically with confidence scores': None,
            '# Format: field_name: canonical_schema_path': None
        }
        
        for field_name, mapping in template['field_mappings'].items():
            if mapping['confidence'] >= 0.8:
                yaml_template[field_name] = mapping['canonical_field']
            else:
                yaml_template[f"{field_name}  # NEEDS_REVIEW"] = mapping['canonical_field']
                
        with open(yaml_path, 'w') as f:
            yaml.dump(yaml_template, f, default_flow_style=False, allow_unicode=True)
            
        print(f"✅ Template saved:")
        print(f"  JSON: {json_path}")
        print(f"  YAML: {yaml_path}")
        
        return json_path, yaml_path


if __name__ == "__main__":
    generator = TemplateGenerator("aetna_skyrizi")
    
    # Generate template from widget discovery
    template = generator.generate_template(Path("widget_discovery.json"))
    
    # Save template
    templates_dir = Path("templates")
    json_path, yaml_path = generator.save_template(template, templates_dir)