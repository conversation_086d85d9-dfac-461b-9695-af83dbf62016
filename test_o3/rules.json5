{
  // PA Form Conditional Logic Rules
  // Format: field -> conditions
  
  "patient_type": {
    "new_patient": {
      "excludes": ["existing_patient", "continuation_of_therapy"],
      "requires": ["initial_diagnosis_date", "no_prior_treatment"]
    },
    "existing_patient": {
      "excludes": ["new_patient"],
      "requires": ["prior_authorization_number", "treatment_start_date"]
    }
  },
  
  "insurance_type": {
    "medicare": {
      "requires": ["medicare_id", "medicare_part_d"],
      "excludes": ["commercial_only_fields"]
    },
    "commercial": {
      "requires": ["member_id", "group_number"],
      "optional": ["employer_name"]
    }
  },
  
  "therapy_status": {
    "therapy_naive": {
      "excludes": ["prior_biologics", "previous_treatments"],
      "checkbox_value": "Yes"
    },
    "therapy_experienced": {
      "requires": ["prior_treatments_list", "failure_reasons"],
      "checkbox_value": "No"
    }
  },
  
  "step_therapy": {
    "completed": {
      "requires": ["tried_medications", "failure_documentation"],
      "implies": ["medical_necessity_override"]
    },
    "not_applicable": {
      "requires": ["contraindication_reason"],
      "excludes": ["tried_medications"]
    }
  },
  
  "diagnosis_specific": {
    "rheumatoid_arthritis": {
      "requires": ["joint_count", "das28_score", "rheumatoid_factor"],
      "valid_icd": ["M05.*", "M06.*"]
    },
    "psoriasis": {
      "requires": ["bsa_percentage", "pasi_score"],
      "valid_icd": ["L40.*"]
    },
    "crohns_disease": {
      "requires": ["cdai_score", "previous_surgeries"],
      "valid_icd": ["K50.*"]
    }
  }
}