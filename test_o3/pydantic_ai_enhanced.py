"""
Enhanced PA Form Filling using PydanticAI Agents
This demonstrates how PydanticAI can intelligently map extracted data to form fields
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import date
from pydantic import BaseModel, Field
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
import pdfplumber


# Pydantic Models for Structured Data
class ExtractedField(BaseModel):
    """Represents a field extracted from the referral package"""
    value: Any
    confidence: float = Field(ge=0, le=1)
    citations: List[str] = []
    reasoning: Optional[str] = None


class FormField(BaseModel):
    """Represents a field in the PA form"""
    widget_id: str
    label: str
    field_type: str  # 'text', 'checkbox', 'radio', etc.
    position: Tuple[float, float, float, float]  # x0, y0, x1, y1
    page: int


class FieldMapping(BaseModel):
    """Maps a form field to extracted data"""
    form_field: Form<PERSON>ield
    extracted_key: str
    confidence: float = Field(ge=0, le=1)
    transformation: Optional[str] = None  # e.g., "split_date", "capitalize"


class PAFormFillerAgent:
    """
    Intelligent agent that learns to map extracted data to form fields
    This is where PydanticAI would enhance the solution
    """
    
    def __init__(self, pdf_path: Path, extracted_json_path: Path):
        self.pdf_path = pdf_path
        self.extracted_data = json.load(open(extracted_json_path))
        self.form_fields = self._analyze_form_structure()
        
    def _analyze_form_structure(self) -> List[FormField]:
        """
        Analyze PDF to extract form field information
        """
        fields = []
        reader = PdfReader(str(self.pdf_path))
        
        with pdfplumber.open(str(self.pdf_path)) as pdf:
            for page_num, page in enumerate(pdf.pages):
                # Get text with positions to find labels
                words = page.extract_words()
                
                # Get form widgets
                if page_num < len(reader.pages):
                    pdf_page = reader.pages[page_num]
                    annots = pdf_page.get("/Annots", [])
                    if hasattr(annots, 'get_object'):
                        annots = annots.get_object()
                    
                    for annot_ref in annots:
                        annot = annot_ref.get_object()
                        if "/T" in annot:  # Has a field name
                            widget_id = annot["/T"]
                            rect = annot.get("/Rect", [0, 0, 0, 0])
                            field_type = annot.get("/FT", "/Tx")
                            
                            # Find label for this field
                            label = self._find_field_label(words, rect)
                            
                            fields.append(FormField(
                                widget_id=widget_id,
                                label=label,
                                field_type=field_type,
                                position=tuple(float(x) for x in rect),
                                page=page_num
                            ))
        
        return fields
    
    def _find_field_label(self, words: List[Dict], rect: List[float]) -> str:
        """
        Find the label text associated with a form field
        """
        if len(rect) < 4:
            return ""
        
        x0, y0, x1, y1 = rect
        best_label = ""
        min_distance = float('inf')
        
        for word in words:
            wx0, wy0, wx1, wy1 = word['x0'], word['bottom'], word['x1'], word['top']
            
            # Check if word is to the left of the field
            if float(wx1) < float(x0) and abs((float(wy0) + float(wy1)) / 2 - (float(y0) + float(y1)) / 2) < 10:
                distance = float(x0) - float(wx1)
                if distance < min_distance:
                    min_distance = distance
                    best_label = word['text']
        
        return best_label
    
    def create_intelligent_mapping(self) -> List[FieldMapping]:
        """
        This is where PydanticAI would shine - intelligently mapping fields
        based on semantic understanding, not just string matching
        """
        mappings = []
        
        # In a real PydanticAI implementation, this would use:
        # 1. Semantic embeddings to match labels to data keys
        # 2. Learning from examples to improve accuracy
        # 3. Confidence scoring based on multiple factors
        
        # For now, we'll use a simplified approach
        label_to_key_map = {
            'first name': 'tier_1_mandatory_fields.patient_demographics.full_name.value',
            'last name': 'tier_1_mandatory_fields.patient_demographics.full_name.value',
            'dob': 'tier_1_mandatory_fields.patient_demographics.date_of_birth.value',
            'address': 'tier_1_mandatory_fields.patient_demographics.address.value',
            'member id': 'tier_1_mandatory_fields.insurance_information.primary_insurance.member_id.value',
            'phone': 'tier_1_mandatory_fields.patient_demographics.phone_numbers.value',
            'fax': 'tier_1_mandatory_fields.prescriber_information.phone_fax.value'
        }
        
        for field in self.form_fields:
            label_lower = field.label.lower()
            
            # Find best match
            best_match = None
            best_confidence = 0
            
            for pattern, key in label_to_key_map.items():
                if pattern in label_lower:
                    confidence = len(pattern) / len(label_lower)
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_match = key
            
            if best_match:
                # Determine transformation needed
                transformation = None
                if 'first name' in label_lower:
                    transformation = 'extract_first_name'
                elif 'last name' in label_lower:
                    transformation = 'extract_last_name'
                
                mappings.append(FieldMapping(
                    form_field=field,
                    extracted_key=best_match,
                    confidence=best_confidence,
                    transformation=transformation
                ))
        
        return mappings
    
    def apply_transformations(self, value: Any, transformation: Optional[str]) -> Any:
        """
        Apply transformations to extracted values
        """
        if not transformation or not value:
            return value
        
        if transformation == 'extract_first_name':
            parts = value.split()
            return parts[0] if parts else ''
        elif transformation == 'extract_last_name':
            parts = value.split()
            return parts[-1].capitalize() if len(parts) > 1 else ''
        elif transformation == 'split_date':
            # Convert YYYY-MM-DD to MM/DD/YYYY
            if '-' in str(value):
                y, m, d = str(value).split('-')
                return f"{m}/{d}/{y}"
        
        return value
    
    def extract_value_from_path(self, path: str) -> Any:
        """
        Extract value from nested JSON using dot notation
        """
        keys = path.split('.')
        value = self.extracted_data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value
    
    def fill_form(self, output_path: Path, mappings: Optional[List[FieldMapping]] = None):
        """
        Fill the form using the intelligent mappings
        """
        if mappings is None:
            mappings = self.create_intelligent_mapping()
        
        # Create form values
        form_values = {}
        
        for mapping in mappings:
            if mapping.confidence > 0.5:  # Only use high-confidence mappings
                value = self.extract_value_from_path(mapping.extracted_key)
                if value:
                    transformed_value = self.apply_transformations(
                        value, mapping.transformation
                    )
                    form_values[mapping.form_field.widget_id] = str(transformed_value)
                    
                    print(f"Mapping {mapping.form_field.label} -> {transformed_value} "
                          f"(confidence: {mapping.confidence:.2f})")
        
        # Fill the PDF
        reader = PdfReader(str(self.pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm is copied
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
        
        # Update fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        print(f"\nFilled {len(form_values)} fields with confidence")
        print(f"Output saved to {output_path}")


# Example of how PydanticAI would enhance this further:
"""
from pydantic_ai import Agent, RunContext

class FormFillingAgent(Agent):
    '''
    An intelligent agent that learns to map extracted data to form fields
    '''
    
    async def map_fields(self, ctx: RunContext[FormField], extracted_data: dict) -> FieldMapping:
        '''
        Use AI to intelligently map form fields to extracted data
        '''
        # The agent would:
        # 1. Understand semantic relationships between labels and data
        # 2. Learn from examples of successful mappings
        # 3. Handle variations in terminology (e.g., "DOB" vs "Date of Birth")
        # 4. Apply appropriate transformations automatically
        
        prompt = f'''
        Form field: {ctx.data.label} (type: {ctx.data.field_type})
        Available data: {list(extracted_data.keys())}
        
        Find the best matching data field and determine any needed transformations.
        '''
        
        # The AI would return a structured FieldMapping
        return await self.run(prompt)
"""


if __name__ == "__main__":
    # Create the intelligent agent
    agent = PAFormFillerAgent(
        Path("test_o3/pa.pdf"),
        Path("test_o3/akshey_extracted.json")
    )
    
    # Analyze form structure
    print("=== FORM STRUCTURE ANALYSIS ===")
    for field in agent.form_fields[:10]:  # Show first 10 fields
        print(f"Widget: {field.widget_id}, Label: '{field.label}', Type: {field.field_type}")
    
    # Create intelligent mappings
    print("\n=== INTELLIGENT FIELD MAPPING ===")
    mappings = agent.create_intelligent_mapping()
    
    # Fill the form
    agent.fill_form(Path("test_o3/pa_pydantic_ai_filled.pdf")) 