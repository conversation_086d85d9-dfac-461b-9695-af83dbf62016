import PyPDF2
import pdfplumber

pdf_path = "pa.pdf"
reader = PyPDF2.PdfReader(pdf_path)
fields = reader.get_fields()

print("=== ALL FORM FIELDS ===")
for field_name, field_obj in fields.items():
    field_type = field_obj.get('/FT', 'Unknown')
    print(f"{field_name}: {field_type}")

print("\n=== ANALYZING FIELD POSITIONS WITH LABELS ===")
with pdfplumber.open(pdf_path) as pdf:
    for page_num, page in enumerate(pdf.pages):
        print(f"\nPage {page_num + 1}:")
        words = page.extract_words()
        
        # Look for key labels
        for word in words:
            text = word['text'].lower()
            if any(key in text for key in ['first', 'last', 'address', 'city', 'member', 'aetna']):
                print(f"  Label '{word['text']}' at x={word['x0']:.1f}, y={word['top']:.1f}") 