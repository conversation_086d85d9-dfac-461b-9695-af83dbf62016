"""
PHASE 1A: Widget Discovery with Spatial Analysis
PyPDF → enumerate widgets → pdfplumber → grab text LEFT/ABOVE each widget
"""

import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import fitz  # PyMuPDF
import pdfplumber
from PyPDF2 import PdfReader


class WidgetDiscovery:
    """Discover form widgets and their spatial relationships to labels"""
    
    def __init__(self, pdf_path: Path):
        self.pdf_path = pdf_path
        self.reader = PdfReader(str(pdf_path))
        self.pymupdf_doc = fitz.open(str(pdf_path))
        
    def get_all_widgets(self) -> List[Dict]:
        """Get all form widgets with their properties"""
        widgets = []
        
        for page_num, page in enumerate(self.reader.pages):
            if '/Annots' in page:
                annotations = page['/Annots']
                
                for annot_ref in annotations:
                    annot = annot_ref.get_object()
                    
                    if annot.get('/FT'):  # Form field
                        field_type = str(annot.get('/FT', ''))
                        field_name = str(annot.get('/T', 'unnamed'))
                        field_value = str(annot.get('/V', ''))
                        
                        # Get rectangle coordinates
                        rect = annot.get('/Rect', [0, 0, 0, 0])
                        x0, y0, x1, y1 = [float(x) for x in rect]
                        
                        widgets.append({
                            'page': page_num,
                            'field_name': field_name,
                            'field_type': field_type,
                            'field_value': field_value,
                            'rect': [x0, y0, x1, y1],
                            'width': x1 - x0,
                            'height': y1 - y0,
                            'center_x': (x0 + x1) / 2,
                            'center_y': (y0 + y1) / 2
                        })
                        
        return widgets
        
    def find_label_for_widget(self, widget: Dict, page_num: int) -> Tuple[str, float]:
        """Find the label text for a widget using spatial analysis"""
        x0, y0, x1, y1 = widget['rect']
        
        # Use pdfplumber for precise text extraction
        with pdfplumber.open(self.pdf_path) as pdf:
            page = pdf.pages[page_num]
            
            # Get all text with bounding boxes
            words = page.extract_words()
            
            label_candidates = []
            
            for word in words:
                wx0, wy0, wx1, wy1 = word['x0'], word['top'], word['x1'], word['bottom']
                word_text = word['text'].strip()
                
                if not word_text or len(word_text) < 2:
                    continue
                    
                # Calculate distances and relationships
                
                # 1. Text to the LEFT of widget (most common)
                if wx1 <= x0:  # Word ends before widget starts
                    # Check if vertically aligned
                    word_center_y = (wy0 + wy1) / 2
                    widget_center_y = (y0 + y1) / 2
                    
                    vertical_distance = abs(word_center_y - widget_center_y)
                    horizontal_distance = x0 - wx1
                    
                    # Close vertically and reasonable horizontal distance
                    if vertical_distance <= 10 and horizontal_distance <= 200:
                        confidence = 1.0 - (vertical_distance / 10) - (horizontal_distance / 200)
                        label_candidates.append({
                            'text': word_text,
                            'confidence': confidence,
                            'distance': horizontal_distance,
                            'type': 'left'
                        })
                
                # 2. Text ABOVE widget
                elif wy1 <= y0:  # Word is above widget
                    word_center_x = (wx0 + wx1) / 2
                    widget_center_x = (x0 + x1) / 2
                    
                    horizontal_distance = abs(word_center_x - widget_center_x)
                    vertical_distance = y0 - wy1
                    
                    # Reasonably aligned horizontally
                    if horizontal_distance <= 50 and vertical_distance <= 50:
                        confidence = 1.0 - (horizontal_distance / 50) - (vertical_distance / 50)
                        label_candidates.append({
                            'text': word_text,
                            'confidence': confidence,
                            'distance': vertical_distance,
                            'type': 'above'
                        })
                        
            # Find best candidate
            if label_candidates:
                # Sort by confidence, then by distance
                label_candidates.sort(key=lambda x: (-x['confidence'], x['distance']))
                best = label_candidates[0]
                
                # Try to build multi-word labels
                full_label = self._build_full_label(best, words, x0, y0, x1, y1)
                
                return full_label, best['confidence']
                
        return "", 0.0
        
    def _build_full_label(self, best_word: Dict, all_words: List[Dict], x0: float, y0: float, x1: float, y1: float) -> str:
        """Build full label by finding adjacent words"""
        label_parts = [best_word['text']]
        
        # Find words near the best word to build complete label
        best_word_info = None
        for word in all_words:
            if word['text'] == best_word['text']:
                best_word_info = word
                break
                
        if not best_word_info:
            return best_word['text']
            
        # Look for words to the left and right of the best word
        for word in all_words:
            if word['text'] == best_word['text']:
                continue
                
            wx0, wy0, wx1, wy1 = word['x0'], word['top'], word['x1'], word['bottom']
            bx0, by0, bx1, by1 = best_word_info['x0'], best_word_info['top'], best_word_info['x1'], best_word_info['bottom']
            
            # Check if on same line
            if abs((wy0 + wy1) / 2 - (by0 + by1) / 2) <= 5:
                # Word to the left
                if wx1 <= bx0 and (bx0 - wx1) <= 20:
                    label_parts.insert(0, word['text'])
                # Word to the right  
                elif wx0 >= bx1 and (wx0 - bx1) <= 20:
                    label_parts.append(word['text'])
                    
        return ' '.join(label_parts)
        
    def discover_all_widgets(self) -> Dict[str, Dict]:
        """Discover all widgets and their labels"""
        widgets = self.get_all_widgets()
        
        print(f"🔍 Discovered {len(widgets)} widgets")
        
        widget_mapping = {}
        
        for widget in widgets:
            label, confidence = self.find_label_for_widget(widget, widget['page'])
            
            widget_mapping[widget['field_name']] = {
                'field_name': widget['field_name'],
                'field_type': widget['field_type'],
                'rect': widget['rect'],
                'page': widget['page'],
                'label': label,
                'label_confidence': confidence,
                'needs_review': confidence < 0.55
            }
            
            status = "❌" if confidence < 0.55 else "✅"
            print(f"{status} {widget['field_name']:<20} | {label:<30} | conf: {confidence:.2f}")
            
        return widget_mapping
        
    def save_discovery(self, output_path: Path):
        """Save widget discovery results"""
        mapping = self.discover_all_widgets()
        
        # Calculate stats
        total_widgets = len(mapping)
        high_confidence = sum(1 for w in mapping.values() if w['label_confidence'] >= 0.55)
        needs_review = total_widgets - high_confidence
        
        discovery_result = {
            'discovery_metadata': {
                'pdf_path': str(self.pdf_path),
                'total_widgets': total_widgets,
                'high_confidence_mappings': high_confidence,
                'needs_review': needs_review,
                'confidence_threshold': 0.55
            },
            'widget_mappings': mapping
        }
        
        with open(output_path, 'w') as f:
            json.dump(discovery_result, f, indent=2)
            
        print(f"\n📊 Discovery Summary:")
        print(f"  Total widgets: {total_widgets}")
        print(f"  High confidence: {high_confidence}")
        print(f"  Needs review: {needs_review}")
        print(f"  Discovery saved: {output_path}")
        
        return discovery_result


if __name__ == "__main__":
    discoverer = WidgetDiscovery(Path("pa.pdf"))
    result = discoverer.save_discovery(Path("widget_discovery.json"))