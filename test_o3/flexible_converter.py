"""
Flexible converter that handles different JSON structures
"""

import json
from pathlib import Path
from canonical_schema import CanonicalPAData, PatientDemographics, InsuranceInformation, PrescriberInformation, DiagnosisInformation, MedicationInformation, TreatmentHistory
from precise_field_mapper import Precise<PERSON>ieldMapper


def flexible_convert_to_canonical(raw_extraction: dict) -> CanonicalPAData:
    """Convert raw extraction to canonical schema with flexible handling"""
    
    # Patient demographics
    demo = raw_extraction['tier_1_mandatory_fields']['patient_demographics']
    full_name = demo['full_name']['value']
    name_parts = full_name.split()
    
    # Parse address
    address = demo['address']['value']
    addr_parts = address.split(',')
    street = addr_parts[0].strip() if addr_parts else ''
    city = addr_parts[1].strip() if len(addr_parts) > 1 else ''
    
    # Parse state/zip
    state_zip = addr_parts[2].strip() if len(addr_parts) > 2 else ''
    state = ''
    zip_code = ''
    if '-' in state_zip:
        state, zip_code = state_zip.split('-', 1)
        state = state.strip()
        zip_code = zip_code.strip()
    elif ' ' in state_zip:
        parts = state_zip.split()
        if len(parts) >= 2:
            state = parts[0]
            zip_code = parts[1]
    
    # Handle physical measurements - check if it's nested under patient_demographics or separate
    phys_measurements = None
    if 'physical_measurements' in demo:
        phys_measurements = demo['physical_measurements']
    elif 'physical_measurements' in raw_extraction['tier_1_mandatory_fields']:
        phys_measurements = raw_extraction['tier_1_mandatory_fields']['physical_measurements']
    
    # Extract weight and height
    weight_lbs = ''
    weight_kg = ''
    height_inches = ''
    height_cm = ''
    
    if phys_measurements:
        if 'weight' in phys_measurements:
            weight_full = phys_measurements['weight']['value']
            if 'lbs' in weight_full:
                weight_lbs = weight_full.split()[0]
            if 'kg' in weight_full:
                weight_kg = weight_full.split(',')[1].strip().split()[0] if ',' in weight_full else ''
                
        if 'height' in phys_measurements:
            height_full = phys_measurements['height']['value']
            if 'inches' in height_full:
                height_inches = height_full.split()[0]
            if 'cm' in height_full:
                height_cm = height_full.split(',')[1].strip().split()[0] if ',' in height_full else ''
    
    # Calculate missing conversions
    if height_inches and not height_cm:
        height_cm = str(int(float(height_inches) * 2.54))
    if weight_lbs and not weight_kg:
        weight_kg = str(round(float(weight_lbs) * 0.453592, 2))
    
    # Extract allergies - look in multiple places
    allergies = 'NKDA'
    if 'tier_2_clinical_justification' in raw_extraction:
        clinical = raw_extraction['tier_2_clinical_justification']
        if 'treatment_history' in clinical and 'contraindications' in clinical['treatment_history']:
            contraind = clinical['treatment_history']['contraindications']['value']
            if contraind:
                allergies = ', '.join(contraind)
    
    patient_demo = PatientDemographics(
        first_name=name_parts[0] if name_parts else '',
        last_name=name_parts[-1] if len(name_parts) > 1 else '',
        middle_initial=name_parts[1] if len(name_parts) > 2 else None,
        date_of_birth=demo['date_of_birth']['value'],
        address=street,
        city=city,
        state=state,
        zip_code=zip_code,
        phone_number=demo['phone_numbers']['value'][0] if demo['phone_numbers']['value'] else '',
        weight_lbs=weight_lbs,
        weight_kg=weight_kg,
        height_inches=height_inches,
        height_cm=height_cm,
        allergies=allergies
    )
    
    # Extract insurance
    ins = raw_extraction['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
    insurance = InsuranceInformation(
        payer_name=ins['payer_name']['value'],
        member_id=ins['member_id']['value'],
        group_number=ins.get('group_number', {}).get('value', ''),
        plan_type=ins.get('plan_type', {}).get('value', ''),
        insured_name=full_name
    )
    
    # Extract prescriber - handle different field names
    pres = raw_extraction['tier_1_mandatory_fields']['prescriber_information']
    
    # Get prescriber name - could be "physician_name" or "ordering_physician"
    prescriber_name = ''
    if 'physician_name' in pres:
        prescriber_name = pres['physician_name']['value']
    elif 'ordering_physician' in pres:
        prescriber_name = pres['ordering_physician']['value']
    
    prescriber_name = prescriber_name.replace(', MD', '').replace(', DO', '')
    pres_parts = prescriber_name.split()
    
    # Get facility info
    facility_name = pres.get('facility_name', {}).get('value', '')
    facility_address = pres.get('facility_address', {}).get('value', '')
    
    # Parse facility address if available
    fac_city, fac_state, fac_zip = '', '', ''
    if facility_address and ',' in facility_address:
        fac_parts = facility_address.split(',')
        if len(fac_parts) > 1:
            city_state_zip = fac_parts[1].strip()
            parts = city_state_zip.split()
            if len(parts) >= 3:
                fac_city = parts[0]
                fac_state = parts[1]
                fac_zip = parts[2]
    
    # Get NPI - could be "npi_number" or "npi"
    npi = ''
    if 'npi_number' in pres:
        npi = pres['npi_number']['value']
    elif 'npi' in pres:
        npi = pres['npi']['value']
    
    # Get phone/fax
    phone = ''
    fax = ''
    if 'phone_fax' in pres:
        phone_fax = pres['phone_fax']['value']
        phone = phone_fax.get('phone', '')
        fax = phone_fax.get('fax', '')
    
    prescriber = PrescriberInformation(
        first_name=pres_parts[0] if pres_parts else '',
        last_name=pres_parts[1] if len(pres_parts) > 1 else '',
        degree='MD',
        npi_number=npi,
        facility_name=facility_name,
        facility_address=facility_address.split(',')[0] if facility_address else '',
        facility_city=fac_city,
        facility_state=fac_state,
        facility_zip=fac_zip,
        phone=phone,
        fax=fax,
        specialty='Gastroenterologist'
    )
    
    # Extract diagnosis
    clinical = raw_extraction['tier_2_clinical_justification']
    diag = clinical['primary_diagnosis']
    
    # Handle different diagnosis field structures
    icd_code = ''
    diagnosis_desc = ''
    
    if 'icd_code' in diag:
        icd_code = diag['icd_code']['value']
    elif 'icd10_codes' in diag:
        icd_code = diag['icd10_codes'][0] if diag['icd10_codes'] else ''
    
    if 'diagnosis_description' in diag:
        diagnosis_desc = diag['diagnosis_description']['value']
    elif 'description' in diag:
        diagnosis_desc = diag['description']
    
    diagnosis = DiagnosisInformation(
        primary_icd_code=icd_code,
        primary_diagnosis=diagnosis_desc
    )
    
    # Extract medication
    med_data = clinical.get('requested_medication', {})
    
    medication = MedicationInformation(
        drug_name=med_data.get('drug_name', ''),
        dosage=med_data.get('dosage', ''),
        frequency=med_data.get('frequency', ''),
        route=med_data.get('route', '')
    )
    
    # Treatment history
    treatment = TreatmentHistory(
        is_new_therapy=True,
        is_continuation=False
    )
    
    return CanonicalPAData(
        patient_demographics=patient_demo,
        insurance_information=insurance,
        prescriber_information=prescriber,
        diagnosis_information=diagnosis,
        medication_information=medication,
        treatment_history=treatment,
        form_type="Amy PA Form",
        extraction_confidence=0.90
    )


def test_amy_system():
    """Test complete system on Amy's data with flexible converter"""
    
    print("🧪 TESTING SYSTEM ON AMY'S PA FORM")
    print("=" * 60)
    
    # Load Amy's data
    amy_json_path = Path("../Input Data/Extracted_ground_truth/amy_structured.json")
    amy_pa_path = Path("../Input Data/Amy/PA.pdf")
    
    print(f"📄 Loading Amy's data from: {amy_json_path}")
    with open(amy_json_path, 'r') as f:
        amy_raw_data = json.load(f)
    
    print(f"📋 Loading Amy's PA form from: {amy_pa_path}")
    
    # Convert to canonical schema using flexible converter
    print("\n🔄 Converting Amy's data to canonical schema...")
    amy_canonical = flexible_convert_to_canonical(amy_raw_data)
    
    print(f"✅ Canonical conversion complete:")
    print(f"  Patient: {amy_canonical.patient_demographics.first_name} {amy_canonical.patient_demographics.last_name}")
    print(f"  DOB: {amy_canonical.patient_demographics.date_of_birth}")
    print(f"  Address: {amy_canonical.patient_demographics.address}, {amy_canonical.patient_demographics.city}, {amy_canonical.patient_demographics.state} {amy_canonical.patient_demographics.zip_code}")
    print(f"  Weight: {amy_canonical.patient_demographics.weight_lbs} lbs / {amy_canonical.patient_demographics.weight_kg} kg")
    print(f"  Height: {amy_canonical.patient_demographics.height_inches} inches / {amy_canonical.patient_demographics.height_cm} cm")
    print(f"  Insurance: {amy_canonical.insurance_information.member_id} ({amy_canonical.insurance_information.payer_name})")
    print(f"  Provider: {amy_canonical.prescriber_information.first_name} {amy_canonical.prescriber_information.last_name}")
    print(f"  NPI: {amy_canonical.prescriber_information.npi_number}")
    print(f"  Facility: {amy_canonical.prescriber_information.facility_name}")
    print(f"  Diagnosis: {amy_canonical.diagnosis_information.primary_icd_code} - {amy_canonical.diagnosis_information.primary_diagnosis}")
    print(f"  Medication: {amy_canonical.medication_information.drug_name}")
    
    # Fill Amy's form using precise mapper
    print("\n🎯 Filling Amy's PA form with precise mapping...")
    mapper = PreciseFieldMapper()
    
    output_path = Path("amy_pa_filled.pdf")
    filled_count = mapper.fill_form_precisely(
        amy_pa_path,
        amy_canonical,
        output_path
    )
    
    print(f"\n🎉 AMY'S FORM PROCESSING COMPLETE!")
    print(f"📊 Fields filled: {filled_count}")
    print(f"📄 Output: {output_path}")
    
    # Save Amy's canonical data for reference
    canonical_path = Path("amy_canonical_data.json")
    with open(canonical_path, 'w') as f:
        json.dump(amy_canonical.model_dump(), f, indent=2, default=str)
    
    print(f"💾 Amy's canonical data saved: {canonical_path}")
    
    return filled_count, amy_canonical


if __name__ == "__main__":
    filled_count, canonical = test_amy_system()