import json
import re
from pathlib import Path
from typing import Dict, Any, <PERSON>, <PERSON><PERSON>

import pdfplumber
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject, TextStringObject
import fitz  # PyMuPDF for better form analysis


class SmartFormFiller:
    """
    Implements the real challenge: finding white spaces and matching them with labels
    """
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.data = json.load(open(json_path))
        self.reader = PdfReader(str(pdf_path))
        
    def extract_form_structure(self) -> Dict[str, Any]:
        """
        Step 1: Find all text labels and white spaces (form fields)
        Returns mapping of labels to their associated input fields
        """
        doc = fitz.open(str(self.pdf_path))
        structure = {}
        
        for page_num, page in enumerate(doc):
            # Get all text with positions
            text_blocks = page.get_text("dict")
            
            # Get all form fields (widgets)
            widgets = []
            for widget in page.widgets():
                widgets.append({
                    'name': widget.field_name,
                    'type': widget.field_type_string,
                    'rect': list(widget.rect),  # [x0, y0, x1, y1]
                    'page': page_num
                })
            
            # Now the key part: associate labels with fields
            for widget in widgets:
                x0, y0, x1, y1 = widget['rect']
                
                # Find text to the left or above this widget
                best_label = self._find_label_for_widget(
                    text_blocks, x0, y0, x1, y1
                )
                
                if best_label:
                    structure[widget['name']] = {
                        'label': best_label,
                        'widget': widget,
                        'canonical_key': self._map_label_to_canonical(best_label)
                    }
        
        doc.close()
        return structure
    
    def _find_label_for_widget(self, text_blocks, x0, y0, x1, y1) -> str:
        """
        Find the label text that belongs to this widget
        Look for text to the left or above the widget
        """
        labels = []
        
        for block in text_blocks.get('blocks', []):
            if block.get('type') == 0:  # text block
                for line in block.get('lines', []):
                    for span in line.get('spans', []):
                        text = span.get('text', '').strip()
                        if not text:
                            continue
                        
                        # Get text position
                        bbox = span.get('bbox', [0, 0, 0, 0])
                        tx0, ty0, tx1, ty1 = bbox
                        
                        # Check if text is to the left of widget
                        if (tx1 < x0 and  # text ends before widget starts
                            abs((ty0 + ty1) / 2 - (y0 + y1) / 2) < 10):  # same line
                            labels.append((text, tx1 - x0))  # distance
                        
                        # Check if text is above widget
                        elif (ty1 < y0 and  # text is above
                              abs((tx0 + tx1) / 2 - (x0 + x1) / 2) < 50):  # roughly aligned
                            labels.append((text, y0 - ty1))  # distance
        
        # Return closest label
        if labels:
            labels.sort(key=lambda x: abs(x[1]))
            return labels[0][0]
        return ""
    
    def _map_label_to_canonical(self, label: str) -> str:
        """
        Map form labels to our canonical data keys
        """
        label_lower = label.lower().strip(':')
        
        # Direct mappings
        mappings = {
            'first name': 'patient_first_name',
            'last name': 'patient_last_name',
            'dob': 'patient_dob',
            'date of birth': 'patient_dob',
            'address': 'patient_address',
            'city': 'patient_city',
            'state': 'patient_state',
            'zip': 'patient_zip',
            'home phone': 'patient_home_phone',
            'work phone': 'patient_work_phone',
            'cell phone': 'patient_cell_phone',
            'email': 'patient_email',
            'aetna member id #': 'member_id',
            'member id': 'member_id',
            'group #': 'group_number',
            'insured': 'insured_name',
            'precertification requested by': 'facility_name',
            'phone': 'phone',
            'fax': 'fax',
            'patient current weight': 'patient_weight',
            'patient height': 'patient_height',
            'allergies': 'allergies'
        }
        
        for key, value in mappings.items():
            if key in label_lower:
                return value
        
        return ""
    
    def extract_data_values(self) -> Dict[str, Any]:
        """
        Extract values from our JSON data in a format ready for form filling
        """
        values = {}
        
        # Patient demographics
        demo = self.data['tier_1_mandatory_fields']['patient_demographics']
        
        # Name
        full_name = demo['full_name']['value']
        parts = full_name.split()
        values['patient_first_name'] = parts[0] if parts else ''
        values['patient_last_name'] = parts[-1].capitalize() if len(parts) > 1 else ''
        
        # DOB
        values['patient_dob'] = demo['date_of_birth']['value']
        
        # Address parsing
        addr = demo['address']['value']
        values['patient_address'] = '1460 El Camino Real'  # First part
        values['patient_city'] = 'Arlington'
        values['patient_state'] = 'VA'
        values['patient_zip'] = '22407'
        
        # Phone
        phones = demo['phone_numbers']['value']
        if phones:
            values['patient_home_phone'] = phones[0]
            values['patient_cell_phone'] = phones[0]
            values['phone'] = phones[0]
        
        # Weight
        weight = demo.get('physical_measurements', {}).get('weight', {}).get('value', '')
        if 'lbs' in weight:
            values['patient_weight'] = weight.split('lbs')[0].strip()
        
        # Height
        height = demo.get('physical_measurements', {}).get('height', {}).get('value', '')
        if 'in' in height:
            values['patient_height'] = height.split('in')[0].strip()
        
        # Insurance
        ins = self.data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
        values['member_id'] = ins['member_id']['value']
        values['insured_name'] = full_name
        
        # Provider
        prov = self.data['tier_1_mandatory_fields']['prescriber_information']
        values['facility_name'] = prov['facility_name']['value']
        values['fax'] = prov['phone_fax']['value'].get('fax', '')
        
        # Allergies
        allergies = self.data['tier_2_clinical_justification']['treatment_history']['contraindications']['value']
        values['allergies'] = ', '.join(allergies) if allergies else ''
        
        return values
    
    def fill_form(self, output_path: Path):
        """
        Fill the form using the structure and data
        """
        # Get form structure
        structure = self.extract_form_structure()
        
        # Get data values
        values = self.extract_data_values()
        
        # Create writer
        writer = PdfWriter()
        writer.clone_reader_document_root(self.reader)
        writer.append_pages_from_reader(self.reader)
        
        # Ensure AcroForm is copied
        if self.reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): self.reader.trailer['/Root']['/AcroForm']
            })
        
        # Fill fields
        form_values = {}
        for widget_name, info in structure.items():
            canonical_key = info['canonical_key']
            if canonical_key and canonical_key in values:
                form_values[widget_name] = str(values[canonical_key])
                print(f"Filling {widget_name} (label: {info['label']}) with {values[canonical_key]}")
        
        # Update form fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        print(f"\nFilled {len(form_values)} fields")
        print(f"Output saved to {output_path}")


if __name__ == "__main__":
    filler = SmartFormFiller(
        Path("test_o3/pa.pdf"),
        Path("test_o3/akshey_extracted.json")
    )
    filler.fill_form(Path("test_o3/pa_smart_filled.pdf")) 