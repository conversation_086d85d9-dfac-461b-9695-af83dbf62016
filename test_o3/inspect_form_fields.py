"""
Inspect PDF form fields to get correct field names and order
"""

from pathlib import Path
from PyPDF2 import PdfReader
import json


def inspect_form_fields(pdf_path: Path):
    """Inspect all form fields in the PDF"""
    reader = PdfReader(str(pdf_path))
    
    print(f"Inspecting: {pdf_path}\n")
    
    all_fields = []
    
    # Get form fields
    if '/AcroForm' in reader.trailer['/Root']:
        fields = reader.get_form_text_fields()
        
        print("Form Text Fields Found:")
        print("-" * 80)
        
        for idx, (field_name, field_value) in enumerate(fields.items()):
            print(f"{idx:3d}. Field Name: '{field_name}'")
            print(f"     Current Value: '{field_value if field_value else '[empty]'}'")
            print()
            
            all_fields.append({
                'index': idx,
                'field_name': field_name,
                'current_value': field_value
            })
            
    # Also check for fields by page
    print("\nFields by Page:")
    print("-" * 80)
    
    for page_num, page in enumerate(reader.pages):
        if '/Annots' in page:
            annotations = page['/Annots']
            print(f"\nPage {page_num}:")
            
            for annot_ref in annotations:
                annot = annot_ref.get_object()
                if annot.get('/FT'):  # Form field
                    field_type = annot.get('/FT')
                    field_name = annot.get('/T', 'unnamed')
                    field_value = annot.get('/V', '')
                    
                    print(f"  - {field_name} (Type: {field_type}, Value: {field_value})")
    
    # Save to JSON for analysis
    output_path = pdf_path.with_suffix('.fields.json')
    with open(output_path, 'w') as f:
        json.dump(all_fields, f, indent=2)
    
    print(f"\nField list saved to: {output_path}")
    
    return all_fields


if __name__ == "__main__":
    fields = inspect_form_fields(Path("test_o3/pa.pdf"))
    print(f"\nTotal fields found: {len(fields)}")