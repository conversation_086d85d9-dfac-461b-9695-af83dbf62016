import argparse, json, re, os, yaml
from pathlib import Path
from typing import Dict, Any

import pdfplumber
from PyPDF2 import PdfReader, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
from rapidfuzz import process, fuzz


# Canonical keys we might encounter with aliases
ALIASES = {
    "patient_first_name": ["first name", "given name", "first"],
    "patient_last_name": ["last name", "surname", "family name"],
    "patient_dob": ["dob", "date of birth", "birth date"],
    "member_id": ["member id", "policy id", "id #", "policy #"],
    "phone": ["phone", "phone #", "telephone"],
    "fax": ["fax"],
    "address": ["address"],
    "city": ["city"],
    "state": ["state"],
    "zip": ["zip"],
    "icd_code": ["icd"],
    "drug_name": ["drug", "medication", "medication name"],
    "dosage": ["dose", "dosage"],
}

# Helper to extract first/last name etc from JSON

def derive(json_data: Dict[str, Any]) -> Dict[str, str]:
    out = {}
    pdemo = json_data["tier_1_mandatory_fields"]["patient_demographics"]
    full = pdemo["full_name"]["value"]
    parts = full.split()
    if parts:
        out["patient_first_name"] = parts[0]
        out["patient_last_name"] = parts[-1].capitalize()
    dob = pdemo["date_of_birth"]["value"]
    out["patient_dob"] = dob
    try:
        y,m,d = dob.split('-')
        out["patient_dob_year"] = y
        out["patient_dob_month"] = m
        out["patient_dob_day"] = d
    except ValueError:
        pass
    out["address"] = pdemo["address"]["value"]
    out["patient_address"] = pdemo["address"]["value"]
    # Parse address components
    addr = pdemo["address"]["value"]
    if ',' in addr:
        parts = addr.split(',')
        out["patient_address"] = parts[0].strip()
        if len(parts) >= 2:
            # Second part is "Arlington, VA-22407"
            city_state_zip = parts[1].strip()
            out["patient_city"] = "Arlington"
            # Extract state and zip from "VA-22407"
            if "VA-" in city_state_zip:
                out["patient_state"] = "VA"
                out["patient_zip"] = "22407"
    phones = pdemo["phone_numbers"]["value"]
    if phones:
        out["phone"] = phones[0]
        out["patient_phone"] = phones[0]
        out["patient_home_phone"] = phones[0]
        out["patient_work_phone"] = ""
        out["patient_cell_phone"] = phones[0]
    out["patient_email"] = ""
    out["group_number"] = ""
    out["insured_name"] = full
    # Extract weight
    weight = pdemo.get("physical_measurements", {}).get("weight", {}).get("value", "")
    if weight:
        # Extract just the lbs part
        if "lbs" in weight:
            lbs_part = weight.split("lbs")[0].strip()
            out["patient_weight_lbs"] = lbs_part
    out["insured_name_alt"] = ""  # Not needed
    ins = json_data["tier_1_mandatory_fields"]["insurance_information"]["primary_insurance"]
    out["member_id"] = ins["member_id"]["value"]
    out["payer_name"] = ins["payer_name"]["value"]
    out["plan_type"] = ins.get("plan_type", {}).get("value", "")
    presc = json_data["tier_1_mandatory_fields"]["prescriber_information"]
    out["fax"] = presc["phone_fax"]["value"].get("fax", "")
    out["facility_name"] = presc.get("facility_name", {}).get("value", "")
    diag = json_data["tier_2_clinical_justification"]["primary_diagnosis"]
    out["icd_code"] = diag["icd_code"]["value"]
    med = json_data["tier_2_clinical_justification"]["requested_medication"]
    out["drug_name"] = med["drug_name"]["value"]
    out["dosage"] = med["dosage"]["value"]
    return out


def find_label_for_field(pdf: PdfReader, page_index: int, rect, words):
    # rect: [x0,y0,x1,y1] in user space. Words list from pdfplumber already page coordinate.
    x0, y0, x1, y1 = rect
    # pdfplumber origin bottom-left; PyPDF rect origin also bottom-left.
    candidates = []
    for w in words:
        wx0, wy0, wx1, wy1 = w["x0"], w["bottom"], w["x1"], w["top"]
        # accept words to the left within same line band or directly above within 40 px
        if (wy0 < y1 + 5 and wy1 > y0 - 5) and wx1 <= x0 - 2:
            candidates.append(w)
        elif (y0 + 5 <= wy1 <= y0 + 40) and (wx0 - 200 <= wx0 <= x1 + 200):
            candidates.append(w)
    # order by x coordinate
    candidates.sort(key=lambda w: w["x0"])
    label = " ".join(w["text"] for w in candidates)
    return label.lower().strip()


def build_mapping(pdf_path: Path) -> Dict[str, str]:
    reader = PdfReader(str(pdf_path))
    mapping = {}

    # open same file with pdfplumber to access words by page coords
    with pdfplumber.open(str(pdf_path)) as plumber_pdf:
        for page_index, page in enumerate(reader.pages):
            annots = page.get("/Annots") or []
            if not annots:
                continue
            annots_list = annots.get_object() if hasattr(annots, 'get_object') else annots
            words = plumber_pdf.pages[page_index].extract_words()
            for annot_ref in annots_list:
                annot = annot_ref.get_object()
                if "/T" not in annot:
                    continue
                field_name = annot["/T"]
                rect = [float(v) for v in annot["/Rect"]]
                field_type = annot.get('/FT')
                label = find_label_for_field(reader, page_index, rect, words)
                best = None
                best_score = 0
                for key, aliases in ALIASES.items():
                    match, score, _ = process.extractOne(label, aliases, scorer=fuzz.partial_ratio)
                    if score > best_score:
                        best_score = score
                        best = key
                if best_score >= 60 and field_type == '/Tx':
                    mapping[field_name] = best
    return mapping


def fill(pdf_path: Path, json_data: Dict[str, Any], output: Path, template_path: Path = None):
    reader = PdfReader(str(pdf_path))
    writer = PdfWriter()
    writer.clone_reader_document_root(reader)
    writer.append_pages_from_reader(reader)
    # ensure AcroForm copied (clone already does but keep safe)
    if reader.trailer['/Root'].get('/AcroForm'):
        writer._root_object.update({NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']})

    data_pool = derive(json_data)
    if template_path and template_path.exists():
        mapping = yaml.safe_load(template_path.read_text())
    else:
        mapping = build_mapping(pdf_path)

    filled = {}
    for pdf_field, canonical in mapping.items():
        value = data_pool.get(canonical, "")
        if value:
            filled[pdf_field] = value
    # update pages
    for page in writer.pages:
        writer.update_page_form_field_values(page, filled)
    writer._root_object.update({NameObject("/NeedAppearances"): BooleanObject(True)})
    with output.open("wb") as f:
        writer.write(f)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--json", required=True)
    parser.add_argument("--pdf", required=True)
    parser.add_argument("--out", required=True)
    parser.add_argument("--template", required=False, help="Optional YAML template mapping pdf_field -> canonical_key")
    args = parser.parse_args()

    pdf_path = Path(args.pdf)
    json_data = json.load(open(args.json))
    fill(pdf_path, json_data, Path(args.out), template_path=Path(args.template) if args.template else None) 