"""
BATCH RUNNER
============

Production batch runner that orchestrates the complete schema-first PA pipeline:
1. Harvest Schema: Generate .yaml schema for any PDF
2. Dynamic Extractor: Extract data using LLM based on schema
3. Dynamic Filler: Fill PDF using schema mappings
4. Batch Processing: Process multiple patient folders

This is the main script that meets the assignment requirements:
"The pipeline should be designed to generalize to any form and any drug, 
even those unseen during development."
"""

import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime

from schema_harvester import SchemaHarvester
from extractor.pa_extractor import PAExtractor
from schema_first_filler import SchemaFirstPAFiller

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProductionBatchRunner:
    """Production batch runner for PA form automation"""
    
    def __init__(self, schemas_dir: str = "schemas"):
        self.schemas_dir = Path(schemas_dir)
        self.schemas_dir.mkdir(exist_ok=True)
        
        # Initialize components
        self.schema_harvester = SchemaHarvester()
        self.extractor = PAExtractor()
        self.filler = SchemaFirstPAFiller(str(self.schemas_dir))
        
        self.batch_results = []
    
    def ensure_schema_exists(self, pdf_path: str) -> str:
        """Ensure schema exists for PDF, create if missing"""
        
        logger.info(f"🔍 ENSURING SCHEMA EXISTS FOR: {pdf_path}")
        
        # Check if schema already exists
        existing_schema = self.filler.find_schema_for_pdf(pdf_path)
        if existing_schema:
            logger.info(f"✅ Schema exists: {existing_schema}")
            return existing_schema
        
        # Generate new schema
        logger.info(f"📋 Generating new schema for: {pdf_path}")
        schema_path = self.schema_harvester.harvest_pdf_schema(
            pdf_path, 
            str(self.schemas_dir / "templates")
        )
        
        logger.info(f"✅ Schema generated: {schema_path}")
        return schema_path
    
    def process_single_patient(self, patient_folder: str) -> Dict[str, Any]:
        """Process a single patient folder with complete pipeline"""
        
        logger.info(f"🚀 PROCESSING PATIENT: {patient_folder}")
        
        folder_path = Path(patient_folder)
        patient_name = folder_path.name
        
        try:
            # Find referral PDF and PA form
            referral_pdfs = list(folder_path.glob("*referral*.pdf"))
            pa_pdfs = list(folder_path.glob("*pa*.pdf")) or list(folder_path.glob("*.pdf"))
            
            if not referral_pdfs:
                raise FileNotFoundError(f"No referral PDF found in {patient_folder}")
            
            if not pa_pdfs:
                raise FileNotFoundError(f"No PA PDF found in {patient_folder}")
            
            referral_pdf = referral_pdfs[0]
            pa_pdf = pa_pdfs[0]
            
            logger.info(f"📄 Referral PDF: {referral_pdf}")
            logger.info(f"📄 PA PDF: {pa_pdf}")
            
            # Step 1: Ensure schema exists for PA form
            schema_path = self.ensure_schema_exists(str(pa_pdf))
            
            # Step 2: Extract data from referral
            extracted_json_path = folder_path / f"{patient_name}_extracted.json"
            
            if not extracted_json_path.exists():
                logger.info(f"📊 Extracting data from referral: {referral_pdf}")
                extracted_data = self.extractor.extract_from_referral_pdf(str(referral_pdf))
                
                # Save extracted data
                with open(extracted_json_path, 'w') as f:
                    json.dump(extracted_data, f, indent=2)
                
                logger.info(f"💾 Extracted data saved: {extracted_json_path}")
            else:
                logger.info(f"📊 Using existing extracted data: {extracted_json_path}")
                with open(extracted_json_path, 'r') as f:
                    extracted_data = json.load(f)
            
            # Step 3: Fill PA form using schema
            logger.info(f"📝 Filling PA form using schema-first approach")
            
            schema = self.filler.load_schema(schema_path)
            filled_pdf = self.filler.fill_pdf_with_schema(str(pa_pdf), extracted_data, schema)
            
            # Step 4: Generate missing fields report
            missing_fields_report = self.generate_missing_fields_report(
                schema, self.filler.get_fill_audit(), patient_name
            )
            
            result = {
                "patient_name": patient_name,
                "patient_folder": str(folder_path),
                "referral_pdf": str(referral_pdf),
                "pa_pdf": str(pa_pdf),
                "schema_used": schema_path,
                "extracted_json": str(extracted_json_path),
                "filled_pdf": filled_pdf,
                "missing_fields_report": missing_fields_report,
                "status": "success",
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"✅ PATIENT PROCESSED SUCCESSFULLY: {patient_name}")
            return result
            
        except Exception as e:
            logger.error(f"❌ PATIENT PROCESSING FAILED: {patient_name} - {e}")
            
            result = {
                "patient_name": patient_name,
                "patient_folder": str(folder_path),
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
            return result
    
    def generate_missing_fields_report(self, schema: Dict[str, Any], fill_audit: List[Dict], patient_name: str) -> str:
        """Generate missing fields report"""
        
        field_mappings = schema.get("field_mappings", {})
        
        # Analyze fill audit
        successful_fills = set()
        failed_fills = set()
        
        for audit_entry in fill_audit:
            if audit_entry.get("type") == "fill":
                if audit_entry.get("status") == "success":
                    # Find semantic name for this acro_id
                    acro_id = audit_entry.get("acro_id", "")
                    for semantic_name, field_meta in field_mappings.items():
                        if field_meta.get("acro_id") == acro_id:
                            successful_fills.add(semantic_name)
                            break
                else:
                    # Find semantic name for failed fill
                    acro_id = audit_entry.get("acro_id", "")
                    for semantic_name, field_meta in field_mappings.items():
                        if field_meta.get("acro_id") == acro_id:
                            failed_fills.add(semantic_name)
                            break
        
        all_fields = set(field_mappings.keys())
        missing_fields = all_fields - successful_fills - failed_fills
        
        report_path = f"missing_fields_{patient_name.lower()}.md"
        
        with open(report_path, 'w') as f:
            f.write(f"# Missing Fields Report - {patient_name}\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"## Summary\n")
            f.write(f"- **Total fields in schema**: {len(all_fields)}\n")
            f.write(f"- **Fields filled successfully**: {len(successful_fills)}\n")
            f.write(f"- **Fields failed to fill**: {len(failed_fills)}\n")
            f.write(f"- **Fields not attempted**: {len(missing_fields)}\n")
            f.write(f"- **Success rate**: {len(successful_fills)/len(all_fields)*100:.1f}%\n\n")
            
            if successful_fills:
                f.write(f"## ✅ Successfully Filled Fields ({len(successful_fills)})\n\n")
                for field_name in sorted(successful_fills):
                    field_meta = field_mappings.get(field_name, {})
                    acro_id = field_meta.get("acro_id", "Unknown")
                    f.write(f"- **{field_name}** (`{acro_id}`)\n")
                f.write(f"\n")
            
            if failed_fills:
                f.write(f"## ❌ Failed to Fill ({len(failed_fills)})\n\n")
                for field_name in sorted(failed_fills):
                    field_meta = field_mappings.get(field_name, {})
                    acro_id = field_meta.get("acro_id", "Unknown")
                    tooltip = field_meta.get("tooltip", "No tooltip")
                    f.write(f"- **{field_name}** (`{acro_id}`): {tooltip}\n")
                f.write(f"\n")
            
            if missing_fields:
                f.write(f"## ⚠️ Not Attempted ({len(missing_fields)})\n\n")
                for field_name in sorted(missing_fields):
                    field_meta = field_mappings.get(field_name, {})
                    acro_id = field_meta.get("acro_id", "Unknown")
                    tooltip = field_meta.get("tooltip", "No tooltip")
                    f.write(f"- **{field_name}** (`{acro_id}`): {tooltip}\n")
        
        logger.info(f"📄 Missing fields report: {report_path}")
        return report_path
    
    def process_batch(self, input_dir: str) -> Dict[str, Any]:
        """Process multiple patient folders"""
        
        logger.info(f"🏭 BATCH PROCESSING: {input_dir}")
        
        input_path = Path(input_dir)
        if not input_path.exists():
            raise FileNotFoundError(f"Input directory not found: {input_dir}")
        
        # Find all patient folders
        patient_folders = [p for p in input_path.iterdir() if p.is_dir()]
        
        if not patient_folders:
            raise ValueError(f"No patient folders found in: {input_dir}")
        
        logger.info(f"📁 Found {len(patient_folders)} patient folders")
        
        batch_results = []
        successful = 0
        failed = 0
        
        for patient_folder in patient_folders:
            result = self.process_single_patient(str(patient_folder))
            batch_results.append(result)
            
            if result["status"] == "success":
                successful += 1
            else:
                failed += 1
        
        # Generate batch summary
        batch_summary = {
            "batch_timestamp": datetime.now().isoformat(),
            "input_directory": str(input_path),
            "total_patients": len(patient_folders),
            "successful": successful,
            "failed": failed,
            "success_rate": successful / len(patient_folders) * 100,
            "results": batch_results
        }
        
        # Save batch results
        batch_results_path = f"batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(batch_results_path, 'w') as f:
            json.dump(batch_summary, f, indent=2)
        
        logger.info(f"🎯 BATCH COMPLETE:")
        logger.info(f"📊 Success rate: {batch_summary['success_rate']:.1f}% ({successful}/{len(patient_folders)})")
        logger.info(f"📄 Batch results: {batch_results_path}")
        
        return batch_summary


def main():
    """Main CLI interface"""
    
    parser = argparse.ArgumentParser(description="Production PA Form Batch Runner")
    parser.add_argument("input_dir", help="Directory containing patient folders")
    parser.add_argument("--schemas-dir", default="schemas", help="Directory for schema files")
    
    args = parser.parse_args()
    
    print("🏭 PRODUCTION PA FORM BATCH RUNNER")
    print("=" * 60)
    print(f"Input directory: {args.input_dir}")
    print(f"Schemas directory: {args.schemas_dir}")
    print("=" * 60)
    
    try:
        # Initialize batch runner
        runner = ProductionBatchRunner(args.schemas_dir)
        
        # Process batch
        results = runner.process_batch(args.input_dir)
        
        print(f"\n🎯 BATCH PROCESSING COMPLETE:")
        print(f"📊 Success rate: {results['success_rate']:.1f}%")
        print(f"✅ Successful: {results['successful']}")
        print(f"❌ Failed: {results['failed']}")
        
        if results['success_rate'] >= 90:
            print(f"\n🎉 EXCELLENT RESULTS! System is production-ready.")
        elif results['success_rate'] >= 70:
            print(f"\n✅ GOOD RESULTS! Minor improvements possible.")
        else:
            print(f"\n🔧 NEEDS IMPROVEMENT: Review failed cases.")
        
    except Exception as e:
        print(f"\n❌ BATCH PROCESSING FAILED: {e}")
        logger.error(f"Batch processing failed: {e}")


if __name__ == "__main__":
    main()
