"""
SCHEMA-FIRST PA FILLER
======================

Production-ready, schema-first PA form filler that:
1. Uses schema files to map any PDF form dynamically
2. Includes proper security (no eval())
3. Includes set_need_appearances_writer() for visibility
4. Generalizes to any unseen form
5. Provides complete audit trails
"""

import yaml
import json
from PyPDF2 import PdfReader, PdfWriter
from pathlib import Path
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SchemaFirstPAFiller:
    """Schema-first PA form filler - generalizes to any form"""
    
    def __init__(self, schemas_dir: str):
        self.schemas_dir = Path(schemas_dir)
        self.fill_audit = []
    
    def find_schema_for_pdf(self, pdf_path: str) -> Optional[str]:
        """Find matching schema file for a PDF (schema-first approach)"""
        
        pdf_name = Path(pdf_path).stem
        
        # Look for exact match first
        exact_schema = self.schemas_dir / "templates" / f"{pdf_name}.yaml"
        if exact_schema.exists():
            logger.info(f"📋 Found exact schema: {exact_schema}")
            return str(exact_schema)
        
        # Look for hash-based schemas
        for schema_file in (self.schemas_dir / "templates").glob("*.yaml"):
            if pdf_name in schema_file.stem:
                logger.info(f"📋 Found matching schema: {schema_file}")
                return str(schema_file)
        
        logger.error(f"❌ No schema found for PDF: {pdf_path}")
        logger.info(f"Available schemas: {list((self.schemas_dir / 'templates').glob('*.yaml'))}")
        return None
    
    def load_schema(self, schema_path: str) -> Dict[str, Any]:
        """Load and validate schema file"""
        
        with open(schema_path, 'r') as f:
            schema = yaml.safe_load(f)
        
        if "field_mappings" not in schema:
            raise ValueError(f"Invalid schema - missing field_mappings: {schema_path}")
        
        field_mappings = schema["field_mappings"]
        logger.info(f"📋 Schema loaded: {len(field_mappings)} field mappings")
        
        return schema
    
    def extract_flat_data(self, nested_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract flat data from nested JSON structure"""

        logger.info(f"📊 EXTRACTING FLAT DATA FROM NESTED STRUCTURE")

        flat_data = {}

        # Patient Demographics
        patient_demo = nested_data.get("tier_1_mandatory_fields", {}).get("patient_demographics", {})

        if "full_name" in patient_demo:
            full_name = patient_demo["full_name"].get("value", "")
            flat_data["patient_full_name"] = full_name

            # Split name
            name_parts = full_name.split()
            if len(name_parts) >= 2:
                flat_data["patient_first_name"] = name_parts[0]
                flat_data["patient_last_name"] = " ".join(name_parts[1:])

        if "date_of_birth" in patient_demo:
            flat_data["patient_dob"] = patient_demo["date_of_birth"].get("value", "")

        if "address" in patient_demo:
            flat_data["patient_address"] = patient_demo["address"].get("value", "")

        if "phone_numbers" in patient_demo:
            phone_list = patient_demo["phone_numbers"].get("value", [])
            if phone_list and isinstance(phone_list, list):
                flat_data["patient_phone"] = phone_list[0]

        # Insurance Information
        insurance_info = nested_data.get("tier_1_mandatory_fields", {}).get("insurance_information", {})
        primary_insurance = insurance_info.get("primary_insurance", {})

        if "member_id" in primary_insurance:
            flat_data["member_id"] = primary_insurance["member_id"].get("value", "")

        # Prescriber Information
        prescriber_info = nested_data.get("tier_1_mandatory_fields", {}).get("prescriber_information", {})

        if "ordering_physician" in prescriber_info:
            flat_data["prescriber_name"] = prescriber_info["ordering_physician"].get("value", "")

        if "npi" in prescriber_info:
            flat_data["prescriber_npi"] = prescriber_info["npi"].get("value", "")

        # Clinical Information
        clinical_info = nested_data.get("tier_2_clinical_justification", {})

        if "requested_medication" in clinical_info:
            med_info = clinical_info["requested_medication"]
            flat_data["drug_name"] = med_info.get("drug_name", "")
            flat_data["dosage"] = med_info.get("dosage", "")
            flat_data["frequency"] = med_info.get("frequency", "")

        if "primary_diagnosis" in clinical_info:
            diag_info = clinical_info["primary_diagnosis"]
            icd_codes = diag_info.get("icd10_codes", [])
            if icd_codes:
                flat_data["icd_code"] = icd_codes[0] if isinstance(icd_codes, list) else str(icd_codes)

        logger.info(f"📊 EXTRACTED {len(flat_data)} flat fields:")
        for key, value in flat_data.items():
            logger.info(f"  {key}: {value}")

        return flat_data

    def map_to_acroform_fields(self, extracted_data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, str]:
        """Map extracted data to PDF AcroForm field IDs using schema"""

        logger.info(f"🔗 MAPPING TO ACROFORM FIELDS")

        # First flatten the nested data
        flat_data = self.extract_flat_data(extracted_data)

        field_mappings = schema["field_mappings"]
        form_values = {}

        for field_name, field_info in field_mappings.items():
            if field_name in flat_data:
                acro_id = field_info.get("acro_id", "")
                value = flat_data[field_name]

                if acro_id and value:
                    # SECURITY FIX: No eval() - safe string conversion only
                    form_values[acro_id] = str(value)
                    logger.info(f"  ✅ {field_name} → {acro_id} = '{value}'")

                    # Audit trail
                    self.fill_audit.append({
                        "type": "mapping",
                        "source_field": field_name,
                        "acro_id": acro_id,
                        "value": str(value),
                        "timestamp": datetime.now().isoformat()
                    })

        logger.info(f"✅ Mapped {len(form_values)} fields to AcroForm IDs")
        return form_values
    
    def fill_pdf_with_schema(self, pdf_path: str, extracted_data: Dict[str, Any], schema: Dict[str, Any]) -> str:
        """Fill PDF using schema mappings with proper visibility"""
        
        logger.info(f"📝 FILLING PDF WITH SCHEMA: {pdf_path}")
        
        # Map data using schema
        form_values = self.map_to_acroform_fields(extracted_data, schema)
        
        if not form_values:
            raise ValueError("No form values mapped - check schema and extracted data")
        
        # Load PDF
        reader = PdfReader(pdf_path)
        writer = PdfWriter()
        writer.clone_document_from_reader(reader)
        
        # Validate PDF has form fields
        pdf_fields = reader.get_fields()
        if not pdf_fields:
            raise ValueError(f"PDF has no form fields: {pdf_path}")
        
        # Fill form fields
        successful_fills = 0
        failed_fills = []
        
        for acro_id, value in form_values.items():
            try:
                if acro_id in pdf_fields:
                    # Check field type for proper handling
                    field_obj = pdf_fields[acro_id].get_object()
                    field_type = field_obj.get('/FT', '')
                    
                    if field_type == '/Tx':  # Text field
                        writer.update_page_form_field_values(
                            writer.pages[0], {acro_id: value}
                        )
                        successful_fills += 1
                        logger.info(f"  ✅ Text field {acro_id}: '{value}'")
                        
                    elif field_type == '/Btn':  # Button field
                        # Handle button fields properly
                        button_values = ['/Yes', '/On', '1', 'Yes', 'On']
                        success = False
                        for btn_val in button_values:
                            try:
                                writer.update_page_form_field_values(
                                    writer.pages[0], {acro_id: btn_val}
                                )
                                successful_fills += 1
                                logger.info(f"  ✅ Button field {acro_id}: '{btn_val}' (for '{value}')")
                                success = True
                                break
                            except:
                                continue
                        
                        if not success:
                            failed_fills.append(f"{acro_id}: Button field - no values worked")
                    
                    else:
                        # Fallback for unknown types
                        writer.update_page_form_field_values(
                            writer.pages[0], {acro_id: value}
                        )
                        successful_fills += 1
                        logger.info(f"  ✅ Unknown field {acro_id}: '{value}' (type: {field_type})")
                    
                    # Audit successful fill
                    self.fill_audit.append({
                        "type": "fill",
                        "acro_id": acro_id,
                        "value": value,
                        "field_type": field_type,
                        "status": "success",
                        "timestamp": datetime.now().isoformat()
                    })
                    
                else:
                    failed_fills.append(f"{acro_id}: Field not found in PDF")
                    logger.warning(f"  ❌ {acro_id}: Field not found in PDF")
                    
                    # Audit failed fill
                    self.fill_audit.append({
                        "type": "fill",
                        "acro_id": acro_id,
                        "value": value,
                        "status": "failed",
                        "error": "Field not found in PDF",
                        "timestamp": datetime.now().isoformat()
                    })
                    
            except Exception as e:
                failed_fills.append(f"{acro_id}: {str(e)}")
                logger.error(f"  ❌ {acro_id}: {e}")
                
                # Audit failed fill
                self.fill_audit.append({
                    "type": "fill",
                    "acro_id": acro_id,
                    "value": value,
                    "status": "failed",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        # CRITICAL FIX: Set need appearances for visibility
        writer.set_need_appearances_writer()
        
        # Save filled PDF
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = Path(pdf_path).stem
        output_filename = f"SCHEMA_FILLED_{pdf_name}_{timestamp}.pdf"
        
        with open(output_filename, "wb") as f:
            writer.write(f)
        
        logger.info(f"✅ SCHEMA-FILLED PDF SAVED: {output_filename}")
        logger.info(f"📊 Fill results: {successful_fills}/{len(form_values)} successful")
        
        if failed_fills:
            logger.warning(f"❌ Failed fills:")
            for failure in failed_fills:
                logger.warning(f"  {failure}")
        
        return output_filename
    
    def process_patient_folder(self, patient_folder: str) -> Dict[str, Any]:
        """Process a patient folder using schema-first approach"""
        
        logger.info(f"🚀 PROCESSING PATIENT FOLDER: {patient_folder}")
        
        folder_path = Path(patient_folder)
        
        # Find referral JSON
        json_files = list(folder_path.glob("*extracted.json"))
        if not json_files:
            raise FileNotFoundError(f"No extracted JSON found in {patient_folder}")
        
        referral_json = json_files[0]
        logger.info(f"📄 Referral JSON: {referral_json}")
        
        # Find PA PDF
        pdf_files = list(folder_path.glob("*.pdf"))
        if not pdf_files:
            raise FileNotFoundError(f"No PDF found in {patient_folder}")
        
        pa_pdf = pdf_files[0]
        logger.info(f"📄 PA PDF: {pa_pdf}")
        
        # Find schema for this PDF
        schema_path = self.find_schema_for_pdf(str(pa_pdf))
        if not schema_path:
            raise FileNotFoundError(f"No schema found for PDF: {pa_pdf}")
        
        # Load data and schema
        with open(referral_json, 'r') as f:
            extracted_data = json.load(f)
        
        schema = self.load_schema(schema_path)
        
        # Fill PDF using schema
        filled_pdf = self.fill_pdf_with_schema(str(pa_pdf), extracted_data, schema)
        
        return {
            "patient_folder": patient_folder,
            "referral_json": str(referral_json),
            "pa_pdf": str(pa_pdf),
            "schema_used": schema_path,
            "filled_pdf": filled_pdf,
            "fill_audit": self.fill_audit.copy()
        }
    
    def get_fill_audit(self) -> List[Dict]:
        """Get complete audit trail"""
        return self.fill_audit


def main():
    """Test schema-first filler"""
    
    print("🏭 SCHEMA-FIRST PA FILLER TEST")
    print("=" * 60)
    
    # Test with Abdullah's data
    filler = SchemaFirstPAFiller("schemas")
    
    try:
        # Process using schema-first approach
        result = filler.process_patient_folder("Input Data/Abdullah")
        
        print(f"\n🎯 SCHEMA-FIRST RESULTS:")
        print(f"📁 Filled PDF: {result['filled_pdf']}")
        print(f"📋 Schema used: {result['schema_used']}")
        print(f"📊 Audit entries: {len(result['fill_audit'])}")
        
        print(f"\n✅ SUCCESS: Schema-first approach working!")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        logger.error(f"Schema-first processing failed: {e}")


if __name__ == "__main__":
    main()
