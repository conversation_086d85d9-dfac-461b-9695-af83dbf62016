form_metadata:
  slug: abdullah_pa_6767c8d3
  source_pdf: abdullah_pa.pdf
  harvested_at: '2025-06-13T21:36:40.297580'
  total_fields: 288
  harvester_version: '1.0'
extraction_stub:
  start_treatment: null
  t2: null
  t3: null
  t4: null
  cb5: null
  t6: null
  t7: null
  t8: null
  prescriber_name: null
  patient_phone: null
  prescriber_fax: null
  patient_address: null
  patient_city: null
  patient_state: null
  patient_zip: null
  t22: null
  t24: null
  t25: null
  t26: null
  t27: null
  t23: null
  member_id: null
  t29: null
  t30: null
  cb31: null
  cb32: null
  t33: null
  t34: null
  t35: null
  patient_first_name: null
  patient_last_name: null
  cb44: null
  cb45: null
  cb46: null
  cb47: null
  t54: null
  prescriber_npi: null
  t56: null
  t57: null
  '0': null
  '1': null
  t58: null
  '3': null
  '4': null
  '7': null
  '10': null
  '21': null
  '23': null
  '24': null
  '25': null
  '26': null
  provider_admin_cb: null
  '5': null
  '8': null
  '9': null
  '18': null
  '19': null
  '27': null
  '28': null
  '35': null
  '36': null
  provider_admin_t: null
  dosage: null
  t65: null
  t66: null
  icd_code: null
  cb96: null
  patient_dob: null
  cb99: null
  cb100: null
  cb101: null
  cb102: null
  cb103: null
  cb104: null
  cb104a: null
  cb104b: null
  cb104c: null
  cb104d: null
  cb105: null
  cb113: null
  cb114: null
  cb118: null
  cb125: null
  t126: null
  cb164: null
  cb165: null
  cb166: null
  cb167: null
  cb168: null
  cb169: null
  cb170: null
  cb171: null
  cb172: null
  cb173: null
  cb174: null
  cb175: null
  cb176: null
  cb177: null
  cb178: null
  cb179: null
  cb180: null
  cb181: null
  drug_name: null
  t190: null
  t191: null
  t192: null
  t193: null
  t194: null
  t195: null
  t197: null
  t198: null
  t199: null
  t200: null
  t201: null
  t202: null
  cb203: null
  cb204: null
  cb205: null
  cb206: null
  cb207: null
  cb208: null
  cb209: null
  cb210: null
  cb211: null
  cb212: null
  cb213: null
  cb214: null
  cb215: null
  cb216: null
  cb217: null
  cb218: null
  cb223: null
  cb224: null
  cb225: null
  cb226: null
  cb227: null
  cb228: null
  cb229: null
  cb281: null
  cb282: null
  cb283: null
  cb284: null
  t286: null
  t287: null
  t288: null
  t289: null
  t290: null
  t291: null
  t293: null
  t294: null
  t295: null
  t296: null
  t297: null
  t298: null
  cb299: null
  cb300: null
  cb301: null
  cb302: null
  cb303: null
  cb304: null
  t305: null
  cb310: null
  cb311: null
  cb312: null
  cb306: null
  cb307: null
  cb308: null
  cb309: null
  cb313: null
  cb314: null
  cb315: null
  cb316: null
  t317: null
  t318: null
  t319: null
  cb127: null
  cb128: null
  cb129: null
  cb130: null
  cb131: null
  cb132: null
  cb133: null
  cb134: null
  cb135: null
  cb136: null
  cb137: null
  cb138: null
  cb139: null
  cb140: null
  cb141: null
  cb142: null
  cb143: null
  cb144: null
  cb145: null
  cb146: null
  cb147: null
  cb148: null
  cb149: null
  cb150: null
  cb151: null
  cb152: null
  cb153: null
  cb154: null
  t155: null
  cb156: null
  cb157: null
  cb158: null
  cb159: null
  cb160: null
  cb161: null
  cb162: null
  cb163: null
  cb70: null
  cb71: null
  cb60: null
  cb61: null
  cb62: null
  cb63: null
  t80: null
  cb72: null
  cb73: null
  cb74: null
  t75: null
  t76: null
  cb77: null
  cb78: null
  cb79: null
  t81: null
  cb82: null
  cb83: null
  cb85: null
  cb86: null
  cb87: null
  cb88: null
  t89: null
  t90: null
  cb91: null
  cb92: null
  cb93: null
  cb94: null
  t95: null
  t96a: null
  cb97: null
  cb98: null
  cb99a: null
  cb100a: null
  cb101a: null
  cb102a: null
  cb103a: null
  t104: null
  t105: null
  cb106: null
  cb107: null
  cb108: null
  cb109: null
  cb110: null
  cb111: null
  cb112: null
  t113: null
  t114: null
  cb115: null
  cb116: null
  cb117: null
  cb119: null
  cb120: null
  cb121: null
  cb122: null
  cb123: null
  cb124: null
  cb98a: null
  cb106a: null
  cb106b: null
  cb106z: null
  cb107z: null
  cb108z: null
  cb109z: null
  cb110z: null
  cb111z: null
  cb112z: null
  cb115z: null
  cb116z: null
  cb117z: null
  cb119z: null
  cb120z: null
  cb121z: null
  cb122z: null
  cb123z: null
  cb124z: null
field_mappings:
  start_treatment:
    acro_id: CB1
    type: button
    tooltip: Start of treatment
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t2:
    acro_id: T2
    type: text
    tooltip: 'Start date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t3:
    acro_id: T3
    type: text
    tooltip: 'Start date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t4:
    acro_id: T4
    type: text
    tooltip: 'Start date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb5:
    acro_id: CB5
    type: button
    tooltip: Continuation of therapy
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t6:
    acro_id: T6
    type: text
    tooltip: 'Date of last treatment: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t7:
    acro_id: T7
    type: text
    tooltip: 'Date of last treatment: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t8:
    acro_id: T8
    type: text
    tooltip: 'Date of last treatment: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  prescriber_name:
    acro_id: Provider Admin CB.22
    type: button
    tooltip: "Physician\u2019s Office"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_name
  patient_phone:
    acro_id: T19
    type: text
    tooltip: Patient Phone
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_phone
  prescriber_fax:
    acro_id: '34'
    type: text
    tooltip: 'Fax:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_phone
  patient_address:
    acro_id: Provider Admin T.29a
    type: text
    tooltip: 'Address:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_address
  patient_city:
    acro_id: 30a
    type: text
    tooltip: 'City:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  patient_state:
    acro_id: '31'
    type: text
    tooltip: 'State:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  patient_zip:
    acro_id: '32'
    type: text
    tooltip: 'ZIP:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t22:
    acro_id: T22
    type: text
    tooltip: 'E-mail:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t24:
    acro_id: T24
    type: text
    tooltip: lbs
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t25:
    acro_id: T25
    type: text
    tooltip: kgs
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t26:
    acro_id: T26
    type: text
    tooltip: inches
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t27:
    acro_id: T27
    type: text
    tooltip: cms
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t23:
    acro_id: T23
    type: text
    tooltip: 'Allergies:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  member_id:
    acro_id: T28
    type: text
    tooltip: 'Member ID #:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t29:
    acro_id: T29
    type: text
    tooltip: 'Group #:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t30:
    acro_id: T30
    type: text
    tooltip: 'Insured:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb31:
    acro_id: CB31
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb32:
    acro_id: CB32
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t33:
    acro_id: T33
    type: text
    tooltip: 'If yes, provide ID#:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t34:
    acro_id: T34
    type: text
    tooltip: 'Carrier Name:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t35:
    acro_id: T35
    type: text
    tooltip: 'Insured:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  patient_first_name:
    acro_id: T12
    type: text
    tooltip: Patient First Name
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_name
  patient_last_name:
    acro_id: T13
    type: text
    tooltip: Patient Last Name
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_name
  cb44:
    acro_id: CB44
    type: button
    tooltip: M.D.
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb45:
    acro_id: CB45
    type: button
    tooltip: D.O.
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb46:
    acro_id: CB46
    type: button
    tooltip: N.P.
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb47:
    acro_id: CB47
    type: button
    tooltip: P.A.
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t54:
    acro_id: T54
    type: text
    tooltip: 'St Lic #:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  prescriber_npi:
    acro_id: Provider Admin T.37
    type: text
    tooltip: 'NPI:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t56:
    acro_id: T56
    type: text
    tooltip: 'DEA #:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t57:
    acro_id: T57
    type: text
    tooltip: 'UPIN:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '0':
    acro_id: '0'
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '1':
    acro_id: '1'
    type: button
    tooltip: Self-administered
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t58:
    acro_id: T58
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '3':
    acro_id: '3'
    type: button
    tooltip: Home
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '4':
    acro_id: '4'
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '7':
    acro_id: '7'
    type: button
    tooltip: Home Infusion Center
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '10':
    acro_id: '10'
    type: button
    tooltip: Administration code(s)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '21':
    acro_id: '21'
    type: button
    tooltip: Outpatient Dialysis Center
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '23':
    acro_id: '23'
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '24':
    acro_id: '24'
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '25':
    acro_id: '25'
    type: button
    tooltip: Mail Order
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '26':
    acro_id: '26'
    type: button
    tooltip: 'Other:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  provider_admin_cb:
    acro_id: Provider Admin CB
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '5':
    acro_id: '5'
    type: text
    tooltip: 'Name:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '8':
    acro_id: '8'
    type: text
    tooltip: 'Agency Name:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '9':
    acro_id: '9'
    type: text
    tooltip: 'CPT:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '18':
    acro_id: '18'
    type: text
    tooltip: 'TIN:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '19':
    acro_id: '19'
    type: text
    tooltip: 'PIN:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '27':
    acro_id: '27'
    type: text
    tooltip: 'Other:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '28':
    acro_id: '28'
    type: text
    tooltip: 'Name:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '35':
    acro_id: '35'
    type: text
    tooltip: 'TIN:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  '36':
    acro_id: '36'
    type: text
    tooltip: 'PIN:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  provider_admin_t:
    acro_id: Provider Admin T
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  dosage:
    acro_id: T64
    type: text
    tooltip: 'Dose:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t65:
    acro_id: T65
    type: text
    tooltip: 'Directions for Use:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t66:
    acro_id: T66
    type: text
    tooltip: 'HCPCS Code:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  icd_code:
    acro_id: T69
    type: text
    tooltip: 'Other ICD Code:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb96:
    acro_id: CB96
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  patient_dob:
    acro_id: T14
    type: text
    tooltip: Patient DOB (MM/DD/YYYY)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_date
  cb99:
    acro_id: CB99
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb100:
    acro_id: CB100
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb101:
    acro_id: CB101
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb102:
    acro_id: CB102
    type: text
    tooltip: ''
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb103:
    acro_id: CB103
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb104:
    acro_id: CB104
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb104a:
    acro_id: CB104a
    type: button
    tooltip: Wegener granulomatosis
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb104b:
    acro_id: CB104b
    type: button
    tooltip: Churg-Strauss syndrome
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb104c:
    acro_id: CB104c
    type: button
    tooltip: microscopic polyangiitis
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb104d:
    acro_id: CB104d
    type: button
    tooltip: pauci-immune glomerulonephritis
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb105:
    acro_id: CB105
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb113:
    acro_id: CB113
    type: button
    tooltip: None of the above
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb114:
    acro_id: CB114
    type: button
    tooltip: AIDS-related B-cell lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb118:
    acro_id: CB118
    type: button
    tooltip: Gastric MALT lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb125:
    acro_id: CB125
    type: button
    tooltip: 'Other:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t126:
    acro_id: T126
    type: text
    tooltip: 'Other:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb164:
    acro_id: CB164
    type: button
    tooltip: Relapsing-remitting MS (RRMS)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb165:
    acro_id: CB165
    type: button
    tooltip: Secondary-progressive MS (SPMS)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb166:
    acro_id: CB166
    type: button
    tooltip: Primary-progressive MS (PPMS)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb167:
    acro_id: CB167
    type: button
    tooltip: Progressive-relapsing MS (PRMS)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb168:
    acro_id: CB168
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb169:
    acro_id: CB169
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb170:
    acro_id: CB170
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb171:
    acro_id: CB171
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb172:
    acro_id: CB172
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb173:
    acro_id: CB173
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb174:
    acro_id: CB174
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb175:
    acro_id: CB175
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb176:
    acro_id: CB176
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb177:
    acro_id: CB177
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb178:
    acro_id: CB178
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb179:
    acro_id: CB179
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb180:
    acro_id: CB180
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb181:
    acro_id: CB181
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  drug_name:
    acro_id: T125
    type: text
    tooltip: "Please explain if there are contraindications or any other medical reason(s)\
      \ that the patient cannot use any of the following preferred products when indicated\
      \ for the patient\u2019s diagnosis?"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess:
    - format_name
  t190:
    acro_id: T190
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t191:
    acro_id: T191
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t192:
    acro_id: T192
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t193:
    acro_id: T193
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t194:
    acro_id: T194
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t195:
    acro_id: T195
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t197:
    acro_id: T197
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t198:
    acro_id: T198
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t199:
    acro_id: T199
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t200:
    acro_id: T200
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t201:
    acro_id: T201
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t202:
    acro_id: T202
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb203:
    acro_id: CB203
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb204:
    acro_id: CB204
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb205:
    acro_id: CB205
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb206:
    acro_id: CB206
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb207:
    acro_id: CB207
    type: button
    tooltip: Mild
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb208:
    acro_id: CB208
    type: button
    tooltip: Moderate
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb209:
    acro_id: CB209
    type: button
    tooltip: Severe
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb210:
    acro_id: CB210
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb211:
    acro_id: CB211
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb212:
    acro_id: CB212
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb213:
    acro_id: CB213
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb214:
    acro_id: CB214
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb215:
    acro_id: CB215
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb216:
    acro_id: CB216
    type: button
    tooltip: ineffective
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb217:
    acro_id: CB217
    type: button
    tooltip: not tolerated
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb218:
    acro_id: CB218
    type: button
    tooltip: contraindicated
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb223:
    acro_id: CB223
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb224:
    acro_id: CB224
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb225:
    acro_id: CB225
    type: button
    tooltip: azathioprine
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb226:
    acro_id: CB226
    type: button
    tooltip: cyclosporine
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb227:
    acro_id: CB227
    type: button
    tooltip: hydroxychloroquine
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb228:
    acro_id: CB228
    type: button
    tooltip: leflunomide
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb229:
    acro_id: CB229
    type: button
    tooltip: sulfasalazine
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb281:
    acro_id: CB281
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb282:
    acro_id: CB282
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb283:
    acro_id: CB283
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb284:
    acro_id: CB284
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t286:
    acro_id: T286
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t287:
    acro_id: T287
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t288:
    acro_id: T288
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t289:
    acro_id: T289
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t290:
    acro_id: T290
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t291:
    acro_id: T291
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t293:
    acro_id: T293
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t294:
    acro_id: T294
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t295:
    acro_id: T295
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t296:
    acro_id: T296
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t297:
    acro_id: T297
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t298:
    acro_id: T298
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb299:
    acro_id: CB299
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb300:
    acro_id: CB300
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb301:
    acro_id: CB301
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb302:
    acro_id: CB302
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb303:
    acro_id: CB303
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb304:
    acro_id: CB304
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t305:
    acro_id: T305
    type: text
    tooltip: 'Please indicate the length of time on Rituxan (rituximab):'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb310:
    acro_id: CB310
    type: button
    tooltip: Mild
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb311:
    acro_id: CB311
    type: button
    tooltip: Moderate
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb312:
    acro_id: CB312
    type: button
    tooltip: Severe
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb306:
    acro_id: CB306
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb307:
    acro_id: CB307
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb308:
    acro_id: CB308
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb309:
    acro_id: CB309
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb313:
    acro_id: CB313
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb314:
    acro_id: CB314
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb315:
    acro_id: CB315
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb316:
    acro_id: CB316
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t317:
    acro_id: T317
    type: text
    tooltip: 'Date: (MM)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t318:
    acro_id: T318
    type: text
    tooltip: 'Date: (DD)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t319:
    acro_id: T319
    type: text
    tooltip: 'Date: (YYYY)'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb127:
    acro_id: CB127
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb128:
    acro_id: CB128
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb129:
    acro_id: CB129
    type: button
    tooltip: leptomeningeal metastases from lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb130:
    acro_id: CB130
    type: button
    tooltip: primary CNS lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb131:
    acro_id: CB131
    type: button
    tooltip: none of the above
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb132:
    acro_id: CB132
    type: button
    tooltip: chronic lymphocytic leukemia (CLL)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb133:
    acro_id: CB133
    type: button
    tooltip: small lymphocytic leukemia
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb134:
    acro_id: CB134
    type: button
    tooltip: none of the above
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb135:
    acro_id: CB135
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb136:
    acro_id: CB136
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb137:
    acro_id: CB137
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb138:
    acro_id: CB138
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb139:
    acro_id: CB139
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb140:
    acro_id: CB140
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb141:
    acro_id: CB141
    type: button
    tooltip: relapsed hairy cell leukemia
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb142:
    acro_id: CB142
    type: button
    tooltip: refractory hairy cell leukemia
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb143:
    acro_id: CB143
    type: button
    tooltip: none of the above
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb144:
    acro_id: CB144
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb145:
    acro_id: CB145
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb146:
    acro_id: CB146
    type: button
    tooltip: heart transplant recipient
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb147:
    acro_id: CB147
    type: button
    tooltip: other solid organ transplant recipient
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb148:
    acro_id: CB148
    type: button
    tooltip: Bavencio (avelumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb149:
    acro_id: CB149
    type: button
    tooltip: Imfinzi (durvalumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb150:
    acro_id: CB150
    type: button
    tooltip: Keytruda (pembrolizumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb151:
    acro_id: CB151
    type: button
    tooltip: Opdivo (nivolumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb152:
    acro_id: CB152
    type: button
    tooltip: Tecentriq (atezolizumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb153:
    acro_id: CB153
    type: button
    tooltip: Yervoy (ipilimumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb154:
    acro_id: CB154
    type: button
    tooltip: 'Other:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t155:
    acro_id: T155
    type: text
    tooltip: 'Other:'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb156:
    acro_id: CB156
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb157:
    acro_id: CB157
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb158:
    acro_id: CB158
    type: button
    tooltip: refractory immune thrombocytopenic purpura
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb159:
    acro_id: CB159
    type: button
    tooltip: idiopathic thrombocytopenic purpura (ITP)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb160:
    acro_id: CB160
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb161:
    acro_id: CB161
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb162:
    acro_id: CB162
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb163:
    acro_id: CB163
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb70:
    acro_id: CB70
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb71:
    acro_id: CB71
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb60:
    acro_id: CB60
    type: button
    tooltip: Riabni (rituximab-arrx)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb61:
    acro_id: CB61
    type: button
    tooltip: Rituxan (rituximab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb62:
    acro_id: CB62
    type: button
    tooltip: Ruxience (rituximab-pvvr)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb63:
    acro_id: CB63
    type: button
    tooltip: Truxima (rituximab-abbs)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t80:
    acro_id: T80
    type: text
    tooltip: "When was the member\u2019s adverse reaction to the preferred drug?"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb72:
    acro_id: CB72
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb73:
    acro_id: CB73
    type: button
    tooltip: Ruxience (rituximab-pvvr)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb74:
    acro_id: CB74
    type: button
    tooltip: Truxima (rituximab-abbs)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t75:
    acro_id: T75
    type: text
    tooltip: "When was the member\u2019s trial and failure of the preferred drug?"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t76:
    acro_id: T76
    type: text
    tooltip: Please describe the nature of the failure of the preferred drug
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb77:
    acro_id: CB77
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb78:
    acro_id: CB78
    type: button
    tooltip: Ruxience (rituximab-pvvr)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb79:
    acro_id: CB79
    type: button
    tooltip: Truxima (rituximab-abbs)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t81:
    acro_id: T81
    type: text
    tooltip: Please describe the nature of the adverse reaction to the preferred drug
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb82:
    acro_id: CB82
    type: button
    tooltip: Ruxience (rituximab-pvvr)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb83:
    acro_id: CB83
    type: button
    tooltip: Truxima (rituximab-abbs)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb85:
    acro_id: CB85
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb86:
    acro_id: CB86
    type: button
    tooltip: Inflectra (infliximab-dyyb)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb87:
    acro_id: CB87
    type: button
    tooltip: Renflexis (infliximab-abda)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb88:
    acro_id: CB88
    type: button
    tooltip: Simponi Aria (golimumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t89:
    acro_id: T89
    type: text
    tooltip: "When was the member\u2019s trial and failure of the preferred drug?"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t90:
    acro_id: T90
    type: text
    tooltip: Please describe the nature of the failure of the preferred drug
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb91:
    acro_id: CB91
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb92:
    acro_id: CB92
    type: button
    tooltip: Inflectra (infliximab-dyyb)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb93:
    acro_id: CB93
    type: button
    tooltip: Renflexis (infliximab-abda)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb94:
    acro_id: CB94
    type: button
    tooltip: Simponi Aria (golimumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t95:
    acro_id: T95
    type: text
    tooltip: "When was the member\u2019s adverse reaction to the preferred drug?"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t96a:
    acro_id: T96a
    type: text
    tooltip: Please describe the nature of the adverse reaction to the preferred drug
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb97:
    acro_id: CB97
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb98:
    acro_id: CB98
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb99a:
    acro_id: CB99a
    type: button
    tooltip: Humira (adalimumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb100a:
    acro_id: CB100a
    type: button
    tooltip: Idacio (adalimumab-aacf)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb101a:
    acro_id: CB101a
    type: button
    tooltip: Rinvoq (upadacitinib)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb102a:
    acro_id: CB102a
    type: button
    tooltip: Tyenne SC (tocilizumab-aazg)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb103a:
    acro_id: CB103a
    type: button
    tooltip: Xeljanz/Xeljanz XR (tofacitinib)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t104:
    acro_id: T104
    type: text
    tooltip: "When was the member\u2019s trial and failure of the preferred drug?"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t105:
    acro_id: T105
    type: text
    tooltip: Please describe the nature of the failure of the preferred drug
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb106:
    acro_id: CB106
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb107:
    acro_id: CB107
    type: button
    tooltip: pemphigus vulgaris
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb108:
    acro_id: CB108
    type: button
    tooltip: pemphigus folliaceus
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb109:
    acro_id: CB109
    type: button
    tooltip: bullous pemphigoid
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb110:
    acro_id: CB110
    type: button
    tooltip: cicatricial pemphigoid
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb111:
    acro_id: CB111
    type: button
    tooltip: epidermolysis bullosa acquisita
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb112:
    acro_id: CB112
    type: button
    tooltip: paraneoplastic pemphigus
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t113:
    acro_id: T113
    type: text
    tooltip: "When was the member\u2019s adverse reaction to the preferred drug?"
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  t114:
    acro_id: T114
    type: text
    tooltip: Please describe the nature of the adverse reaction to the preferred drug
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb115:
    acro_id: CB115
    type: button
    tooltip: Burkitt lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb116:
    acro_id: CB116
    type: button
    tooltip: Diffuse large B-cell lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb117:
    acro_id: CB117
    type: button
    tooltip: Follicular lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb119:
    acro_id: CB119
    type: button
    tooltip: High-grade B-Cell lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb120:
    acro_id: CB120
    type: button
    tooltip: Mantle cell lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb121:
    acro_id: CB121
    type: button
    tooltip: Nodal marginal zone lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb122:
    acro_id: CB122
    type: button
    tooltip: Nongastric MALT lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb123:
    acro_id: CB123
    type: button
    tooltip: Primary cutaneous B-cell lymphomas
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb124:
    acro_id: CB124
    type: button
    tooltip: Splenic marginal zone lymphoma
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb98a:
    acro_id: CB98a
    type: button
    tooltip: Enbrel (etanercept)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb106a:
    acro_id: CB106a
    type: button
    tooltip: 'Yes'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb106b:
    acro_id: CB106b
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb106z:
    acro_id: CB106z
    type: button
    tooltip: 'No'
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb107z:
    acro_id: CB107z
    type: button
    tooltip: Enbrel (etanercept)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb108z:
    acro_id: CB108z
    type: button
    tooltip: Humira (adalimumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb109z:
    acro_id: CB109z
    type: button
    tooltip: Idacio (adalimumab-aacf)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb110z:
    acro_id: CB110z
    type: button
    tooltip: Rinvoq (upadacitinib)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb111z:
    acro_id: CB111z
    type: button
    tooltip: Tyenne SC (tocilizumab-aazg)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb112z:
    acro_id: CB112z
    type: button
    tooltip: Xeljanz/Xeljanz XR (tofacitinib)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb115z:
    acro_id: CB115z
    type: button
    tooltip: Inflectra (infliximab-dyyb)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb116z:
    acro_id: CB116z
    type: button
    tooltip: Renflexis (infliximab-abda)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb117z:
    acro_id: CB117z
    type: button
    tooltip: Simponi Aria (golimumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb119z:
    acro_id: CB119z
    type: button
    tooltip: Enbrel (etanercept)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb120z:
    acro_id: CB120z
    type: button
    tooltip: Humira (adalimumab)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb121z:
    acro_id: CB121z
    type: button
    tooltip: Idacio (adalimumab-aacf)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb122z:
    acro_id: CB122z
    type: button
    tooltip: Rinvoq (upadacitinib)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb123z:
    acro_id: CB123z
    type: button
    tooltip: Tyenne SC (tocilizumab-aazg)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
  cb124z:
    acro_id: CB124z
    type: button
    tooltip: Xeljanz/Xeljanz XR (tofacitinib)
    required: false
    conditional_on: null
    options: []
    validation: {}
    postprocess: []
