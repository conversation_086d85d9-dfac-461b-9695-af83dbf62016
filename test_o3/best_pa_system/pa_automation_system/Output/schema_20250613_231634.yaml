# PA Form Schema
# Generated from: schema_20250613_231634.yaml
# Total fields: 325

23:
  acro_id: "23"
  human_name: "23"
  type: text

24:
  acro_id: "24"
  human_name: "24"
  type: text

4:
  acro_id: "4"
  human_name: "4"
  type: text

address:
  acro_id: "T15"
  human_name: "Address:"
  type: text

address_1:
  acro_id: "T48"
  human_name: "Address:"
  type: text

address_2:
  acro_id: "11A"
  human_name: "Address:"
  type: text

address_3:
  acro_id: "29a"
  human_name: "Address:"
  type: text

administration_codes:
  acro_id: "10"
  human_name: "Administration code(s)"
  type: checkbox

agency_name:
  acro_id: "8"
  human_name: "Agency Name:"
  type: text

aids_related_b_cell_lymphoma:
  acro_id: "CB114"
  human_name: "AIDS-related B-cell lymphoma"
  type: checkbox

allergies:
  acro_id: "T23"
  human_name: "Allergies:"
  type: text

azathioprine:
  acro_id: "CB225"
  human_name: "azathioprine"
  type: checkbox

bavencio_avelumab:
  acro_id: "CB148"
  human_name: "<PERSON><PERSON><PERSON> (avelumab)"
  type: checkbox

bullous_pemphigoid:
  acro_id: "CB109"
  human_name: "bullous pemphigoid"
  type: checkbox

burkitt_lymphoma:
  acro_id: "CB115"
  human_name: "Burkitt lymphoma"
  type: checkbox

carrier_name:
  acro_id: "T34"
  human_name: "Carrier Name:"
  type: text

cb100:
  acro_id: "CB100"
  human_name: "CB100"
  type: text

cb101:
  acro_id: "CB101"
  human_name: "CB101"
  type: text

cb102:
  acro_id: "CB102"
  human_name: "CB102"
  type: text

cb96:
  acro_id: "CB96"
  human_name: "CB96"
  type: text

cb99:
  acro_id: "CB99"
  human_name: "CB99"
  type: text

cell_phone:
  acro_id: "T21"
  human_name: "Cell Phone:"
  type: text

chronic_lymphocytic_leukemia_cll:
  acro_id: "CB132"
  human_name: "chronic lymphocytic leukemia (CLL)"
  type: checkbox

churg_strauss_syndrome:
  acro_id: "CB104b"
  human_name: "Churg-Strauss syndrome"
  type: checkbox

cicatricial_pemphigoid:
  acro_id: "CB110"
  human_name: "cicatricial pemphigoid"
  type: checkbox

city:
  acro_id: "T16"
  human_name: "City:"
  type: text

city_1:
  acro_id: "T49"
  human_name: "City:"
  type: text

city_2:
  acro_id: "13"
  human_name: "City:"
  type: text

city_3:
  acro_id: "30a"
  human_name: "City:"
  type: text

cms:
  acro_id: "T27"
  human_name: "cms"
  type: text

continuation_of_therapy:
  acro_id: "CB5"
  human_name: "Continuation of therapy"
  type: checkbox

contraindicated:
  acro_id: "CB218"
  human_name: "contraindicated"
  type: checkbox

cpt:
  acro_id: "9"
  human_name: "CPT:"
  type: text

cyclosporine:
  acro_id: "CB226"
  human_name: "cyclosporine"
  type: checkbox

date_dd:
  acro_id: "T191"
  human_name: "Date: (DD)"
  type: text

date_dd_1:
  acro_id: "T194"
  human_name: "Date: (DD)"
  type: text

date_dd_2:
  acro_id: "T198"
  human_name: "Date: (DD)"
  type: text

date_dd_3:
  acro_id: "T201"
  human_name: "Date: (DD)"
  type: text

date_dd_4:
  acro_id: "T287"
  human_name: "Date: (DD)"
  type: text

date_dd_5:
  acro_id: "T290"
  human_name: "Date: (DD)"
  type: text

date_dd_6:
  acro_id: "T294"
  human_name: "Date: (DD)"
  type: text

date_dd_7:
  acro_id: "T297"
  human_name: "Date: (DD)"
  type: text

date_dd_8:
  acro_id: "T318"
  human_name: "Date: (DD)"
  type: text

date_mm:
  acro_id: "T190"
  human_name: "Date: (MM)"
  type: text

date_mm_1:
  acro_id: "T193"
  human_name: "Date: (MM)"
  type: text

date_mm_2:
  acro_id: "T197"
  human_name: "Date: (MM)"
  type: text

date_mm_3:
  acro_id: "T200"
  human_name: "Date: (MM)"
  type: text

date_mm_4:
  acro_id: "T286"
  human_name: "Date: (MM)"
  type: text

date_mm_5:
  acro_id: "T289"
  human_name: "Date: (MM)"
  type: text

date_mm_6:
  acro_id: "T293"
  human_name: "Date: (MM)"
  type: text

date_mm_7:
  acro_id: "T296"
  human_name: "Date: (MM)"
  type: text

date_mm_8:
  acro_id: "T317"
  human_name: "Date: (MM)"
  type: text

date_of_last_treatment_dd:
  acro_id: "T7"
  human_name: "Date of last treatment: (DD)"
  type: text

date_of_last_treatment_mm:
  acro_id: "T6"
  human_name: "Date of last treatment: (MM)"
  type: text

date_of_last_treatment_yyyy:
  acro_id: "T8"
  human_name: "Date of last treatment: (YYYY)"
  type: text

date_yyyy:
  acro_id: "T192"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_1:
  acro_id: "T195"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_2:
  acro_id: "T199"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_3:
  acro_id: "T202"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_4:
  acro_id: "T288"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_5:
  acro_id: "T291"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_6:
  acro_id: "T295"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_7:
  acro_id: "T298"
  human_name: "Date: (YYYY)"
  type: text

date_yyyy_8:
  acro_id: "T319"
  human_name: "Date: (YYYY)"
  type: text

dea:
  acro_id: "T56"
  human_name: "DEA #:"
  type: text

diffuse_large_b_cell_lymphoma:
  acro_id: "CB116"
  human_name: "Diffuse large B-cell lymphoma"
  type: checkbox

directions_for_use:
  acro_id: "T65"
  human_name: "Directions for Use:"
  type: text

do:
  acro_id: "CB45"
  human_name: "D.O."
  type: checkbox

dose:
  acro_id: "T64"
  human_name: "Dose:"
  type: text

e_mail:
  acro_id: "T22"
  human_name: "E-mail:"
  type: text

enbrel_etanercept:
  acro_id: "CB98a"
  human_name: "Enbrel (etanercept)"
  type: checkbox

enbrel_etanercept_1:
  acro_id: "CB107z"
  human_name: "Enbrel (etanercept)"
  type: checkbox

enbrel_etanercept_2:
  acro_id: "CB119z"
  human_name: "Enbrel (etanercept)"
  type: checkbox

epidermolysis_bullosa_acquisita:
  acro_id: "CB111"
  human_name: "epidermolysis bullosa acquisita"
  type: checkbox

fax:
  acro_id: "T11"
  human_name: "Fax:"
  type: text

fax_1:
  acro_id: "T53"
  human_name: "Fax:"
  type: text

fax_2:
  acro_id: "17A"
  human_name: "Fax:"
  type: text

fax_3:
  acro_id: "34"
  human_name: "Fax:"
  type: text

first_name:
  acro_id: "T42"
  human_name: "First Name:"
  type: text

follicular_lymphoma:
  acro_id: "CB117"
  human_name: "Follicular lymphoma"
  type: checkbox

gastric_malt_lymphoma:
  acro_id: "CB118"
  human_name: "Gastric MALT lymphoma"
  type: checkbox

group:
  acro_id: "T29"
  human_name: "Group #:"
  type: text

hcpcs_code:
  acro_id: "T66"
  human_name: "HCPCS Code:"
  type: text

heart_transplant_recipient:
  acro_id: "CB146"
  human_name: "heart transplant recipient"
  type: checkbox

high_grade_b_cell_lymphoma:
  acro_id: "CB119"
  human_name: "High-grade B-Cell lymphoma"
  type: checkbox

home:
  acro_id: "3"
  human_name: "Home"
  type: checkbox

home_infusion_center:
  acro_id: "7"
  human_name: "Home Infusion Center"
  type: checkbox

humira_adalimumab:
  acro_id: "CB99a"
  human_name: "Humira (adalimumab)"
  type: checkbox

humira_adalimumab_1:
  acro_id: "CB108z"
  human_name: "Humira (adalimumab)"
  type: checkbox

humira_adalimumab_2:
  acro_id: "CB120z"
  human_name: "Humira (adalimumab)"
  type: checkbox

hydroxychloroquine:
  acro_id: "CB227"
  human_name: "hydroxychloroquine"
  type: checkbox

idacio_adalimumab_aacf:
  acro_id: "CB100a"
  human_name: "Idacio (adalimumab-aacf)"
  type: checkbox

idacio_adalimumab_aacf_1:
  acro_id: "CB109z"
  human_name: "Idacio (adalimumab-aacf)"
  type: checkbox

idacio_adalimumab_aacf_2:
  acro_id: "CB121z"
  human_name: "Idacio (adalimumab-aacf)"
  type: checkbox

idiopathic_thrombocytopenic_purpura_itp:
  acro_id: "CB159"
  human_name: "idiopathic thrombocytopenic purpura (ITP)"
  type: checkbox

if_yes_provide_id:
  acro_id: "T33"
  human_name: "If yes, provide ID#:"
  type: text

imfinzi_durvalumab:
  acro_id: "CB149"
  human_name: "Imfinzi (durvalumab)"
  type: checkbox

inches:
  acro_id: "T26"
  human_name: "inches"
  type: text

ineffective:
  acro_id: "CB216"
  human_name: "ineffective"
  type: checkbox

inflectra_infliximab_dyyb:
  acro_id: "CB86"
  human_name: "Inflectra (infliximab-dyyb)"
  type: checkbox

inflectra_infliximab_dyyb_1:
  acro_id: "CB92"
  human_name: "Inflectra (infliximab-dyyb)"
  type: checkbox

inflectra_infliximab_dyyb_2:
  acro_id: "CB115z"
  human_name: "Inflectra (infliximab-dyyb)"
  type: checkbox

insured:
  acro_id: "T30"
  human_name: "Insured:"
  type: text

insured_1:
  acro_id: "T35"
  human_name: "Insured:"
  type: text

keytruda_pembrolizumab:
  acro_id: "CB150"
  human_name: "Keytruda (pembrolizumab)"
  type: checkbox

kgs:
  acro_id: "T25"
  human_name: "kgs"
  type: text

last_name:
  acro_id: "T43"
  human_name: "Last Name:"
  type: text

lbs:
  acro_id: "T24"
  human_name: "lbs"
  type: text

leflunomide:
  acro_id: "CB228"
  human_name: "leflunomide"
  type: checkbox

leptomeningeal_metastases_from_lymphoma:
  acro_id: "CB129"
  human_name: "leptomeningeal metastases from lymphoma"
  type: checkbox

mail_order:
  acro_id: "25"
  human_name: "Mail Order"
  type: checkbox

mantle_cell_lymphoma:
  acro_id: "CB120"
  human_name: "Mantle cell lymphoma"
  type: checkbox

md:
  acro_id: "CB44"
  human_name: "M.D."
  type: checkbox

medication:
  acro_id: "T189"
  human_name: "Medication:"
  type: text

medication_1:
  acro_id: "T196"
  human_name: "Medication:"
  type: text

medication_2:
  acro_id: "T285"
  human_name: "Medication:"
  type: text

medication_3:
  acro_id: "T292"
  human_name: "Medication:"
  type: text

member_id:
  acro_id: "T28"
  human_name: "Member ID #:"
  type: text

microscopic_polyangiitis:
  acro_id: "CB104c"
  human_name: "microscopic polyangiitis"
  type: checkbox

mild:
  acro_id: "CB207"
  human_name: "Mild"
  type: checkbox

mild_1:
  acro_id: "CB310"
  human_name: "Mild"
  type: checkbox

moderate:
  acro_id: "CB208"
  human_name: "Moderate"
  type: checkbox

moderate_1:
  acro_id: "CB311"
  human_name: "Moderate"
  type: checkbox

name:
  acro_id: "5"
  human_name: "Name:"
  type: text

name_1:
  acro_id: "28"
  human_name: "Name:"
  type: text

no:
  acro_id: "CB32"
  human_name: "No"
  type: checkbox

no_1:
  acro_id: "0"
  human_name: "No"
  type: checkbox

no_10:
  acro_id: "CB204"
  human_name: "No"
  type: checkbox

no_11:
  acro_id: "CB206"
  human_name: "No"
  type: checkbox

no_12:
  acro_id: "CB211"
  human_name: "No"
  type: checkbox

no_13:
  acro_id: "CB213"
  human_name: "No"
  type: checkbox

no_14:
  acro_id: "CB215"
  human_name: "No"
  type: checkbox

no_15:
  acro_id: "CB224"
  human_name: "No"
  type: checkbox

no_16:
  acro_id: "CB282"
  human_name: "No"
  type: checkbox

no_17:
  acro_id: "CB284"
  human_name: "No"
  type: checkbox

no_18:
  acro_id: "CB300"
  human_name: "No"
  type: checkbox

no_19:
  acro_id: "CB302"
  human_name: "No"
  type: checkbox

no_2:
  acro_id: "CB104"
  human_name: "No"
  type: checkbox

no_20:
  acro_id: "CB304"
  human_name: "No"
  type: checkbox

no_21:
  acro_id: "CB307"
  human_name: "No"
  type: checkbox

no_22:
  acro_id: "CB309"
  human_name: "No"
  type: checkbox

no_23:
  acro_id: "CB314"
  human_name: "No"
  type: checkbox

no_24:
  acro_id: "CB316"
  human_name: "No"
  type: checkbox

no_25:
  acro_id: "CB128"
  human_name: "No"
  type: checkbox

no_26:
  acro_id: "CB136"
  human_name: "No"
  type: checkbox

no_27:
  acro_id: "CB138"
  human_name: "No"
  type: checkbox

no_28:
  acro_id: "CB140"
  human_name: "No"
  type: checkbox

no_29:
  acro_id: "CB145"
  human_name: "No"
  type: checkbox

no_3:
  acro_id: "CB169"
  human_name: "No"
  type: checkbox

no_30:
  acro_id: "CB157"
  human_name: "No"
  type: checkbox

no_31:
  acro_id: "CB161"
  human_name: "No"
  type: checkbox

no_32:
  acro_id: "CB163"
  human_name: "No"
  type: checkbox

no_33:
  acro_id: "CB71"
  human_name: "No"
  type: checkbox

no_34:
  acro_id: "CB72"
  human_name: "No"
  type: checkbox

no_35:
  acro_id: "CB77"
  human_name: "No"
  type: checkbox

no_36:
  acro_id: "CB85"
  human_name: "No"
  type: checkbox

no_37:
  acro_id: "CB91"
  human_name: "No"
  type: checkbox

no_38:
  acro_id: "CB97"
  human_name: "No"
  type: checkbox

no_39:
  acro_id: "CB98"
  human_name: "No"
  type: checkbox

no_4:
  acro_id: "CB171"
  human_name: "No"
  type: checkbox

no_40:
  acro_id: "CB106"
  human_name: "No"
  type: checkbox

no_41:
  acro_id: "CB106b"
  human_name: "No"
  type: checkbox

no_42:
  acro_id: "CB106z"
  human_name: "No"
  type: checkbox

no_5:
  acro_id: "CB173"
  human_name: "No"
  type: checkbox

no_6:
  acro_id: "CB175"
  human_name: "No"
  type: checkbox

no_7:
  acro_id: "CB177"
  human_name: "No"
  type: checkbox

no_8:
  acro_id: "CB179"
  human_name: "No"
  type: checkbox

no_9:
  acro_id: "CB181"
  human_name: "No"
  type: checkbox

nodal_marginal_zone_lymphoma:
  acro_id: "CB121"
  human_name: "Nodal marginal zone lymphoma"
  type: checkbox

none_of_the_above:
  acro_id: "CB113"
  human_name: "None of the above"
  type: checkbox

none_of_the_above_1:
  acro_id: "CB131"
  human_name: "none of the above"
  type: checkbox

none_of_the_above_2:
  acro_id: "CB134"
  human_name: "none of the above"
  type: checkbox

none_of_the_above_3:
  acro_id: "CB143"
  human_name: "none of the above"
  type: checkbox

nongastric_malt_lymphoma:
  acro_id: "CB122"
  human_name: "Nongastric MALT lymphoma"
  type: checkbox

not_tolerated:
  acro_id: "CB217"
  human_name: "not tolerated"
  type: checkbox

np:
  acro_id: "CB46"
  human_name: "N.P."
  type: checkbox

npi:
  acro_id: "T55"
  human_name: "NPI #:"
  type: text

npi_1:
  acro_id: "20"
  human_name: "NPI:"
  type: text

npi_2:
  acro_id: "37"
  human_name: "NPI:"
  type: text

opdivo_nivolumab:
  acro_id: "CB151"
  human_name: "Opdivo (nivolumab)"
  type: checkbox

other:
  acro_id: "26"
  human_name: "Other:"
  type: checkbox

other_1:
  acro_id: "27"
  human_name: "Other:"
  type: text

other_2:
  acro_id: "CB125"
  human_name: "Other:"
  type: checkbox

other_3:
  acro_id: "T126"
  human_name: "Other:"
  type: text

other_4:
  acro_id: "CB154"
  human_name: "Other:"
  type: checkbox

other_5:
  acro_id: "T155"
  human_name: "Other:"
  type: text

other_icd_code:
  acro_id: "CB68"
  human_name: "Other ICD Code:"
  type: checkbox

other_icd_code_1:
  acro_id: "T69"
  human_name: "Other ICD Code:"
  type: text

other_solid_organ_transplant_recipient:
  acro_id: "CB147"
  human_name: "other solid organ transplant recipient"
  type: checkbox

outpatient_dialysis_center:
  acro_id: "21"
  human_name: "Outpatient Dialysis Center"
  type: checkbox

pa:
  acro_id: "CB47"
  human_name: "P.A."
  type: checkbox

paraneoplastic_pemphigus:
  acro_id: "CB112"
  human_name: "paraneoplastic pemphigus"
  type: checkbox

patient_dob_mmddyyyy:
  acro_id: "T14"
  human_name: "Patient DOB (MM/DD/YYYY)"
  type: text

patient_first_name:
  acro_id: "T12"
  human_name: "Patient First Name"
  type: text

patient_last_name:
  acro_id: "T13"
  human_name: "Patient Last Name"
  type: text

patient_phone:
  acro_id: "T19"
  human_name: "Patient Phone"
  type: text

pauci_immune_glomerulonephritis:
  acro_id: "CB104d"
  human_name: "pauci-immune glomerulonephritis"
  type: checkbox

pemphigus_folliaceus:
  acro_id: "CB108"
  human_name: "pemphigus folliaceus"
  type: checkbox

pemphigus_vulgaris:
  acro_id: "CB107"
  human_name: "pemphigus vulgaris"
  type: checkbox

phone:
  acro_id: "T10"
  human_name: "Phone:"
  type: text

phone_1:
  acro_id: "T52"
  human_name: "Phone:"
  type: text

phone_2:
  acro_id: "T59"
  human_name: "Phone:"
  type: text

phone_3:
  acro_id: "6"
  human_name: "Phone:"
  type: text

phone_4:
  acro_id: "16"
  human_name: "Phone:"
  type: text

phone_5:
  acro_id: "33"
  human_name: "Phone:"
  type: text

physicians_office:
  acro_id: "2"
  human_name: "Physician’s Office"
  type: checkbox

physicians_office_1:
  acro_id: "22"
  human_name: "Physician’s Office"
  type: checkbox

pin:
  acro_id: "19"
  human_name: "PIN:"
  type: text

pin_1:
  acro_id: "36"
  human_name: "PIN:"
  type: text

please_describe_the_nature_of_the_adverse_reaction_to_the_preferred_drug:
  acro_id: "T81"
  human_name: "Please describe the nature of the adverse reaction to the preferred drug"
  type: text

please_describe_the_nature_of_the_adverse_reaction_to_the_preferred_drug_1:
  acro_id: "T96a"
  human_name: "Please describe the nature of the adverse reaction to the preferred drug"
  type: text

please_describe_the_nature_of_the_adverse_reaction_to_the_preferred_drug_2:
  acro_id: "T114"
  human_name: "Please describe the nature of the adverse reaction to the preferred drug"
  type: text

please_describe_the_nature_of_the_failure_of_the_preferred_drug:
  acro_id: "T76"
  human_name: "Please describe the nature of the failure of the preferred drug"
  type: text

please_describe_the_nature_of_the_failure_of_the_preferred_drug_1:
  acro_id: "T90"
  human_name: "Please describe the nature of the failure of the preferred drug"
  type: text

please_describe_the_nature_of_the_failure_of_the_preferred_drug_2:
  acro_id: "T105"
  human_name: "Please describe the nature of the failure of the preferred drug"
  type: text

please_explain_if_there_are_any_contraindications_or_other_medical_reasons_that_the_patient_cannot_use_any_of_the_following_preferred_biosimilar_products_when_indicated_for_the_patients_diagnosis:
  acro_id: "T84"
  human_name: "Please explain if there are any contraindications or other medical reason(s) that the patient cannot use any of the following preferred biosimilar products when indicated for the patient’s diagnosis?"
  type: text

please_explain_if_there_are_any_contraindications_or_other_medical_reasons_that_the_patient_cannot_use_any_of_the_following_preferred_products_when_indicated_for_the_patients_diagnosis:
  acro_id: "T118"
  human_name: "Please explain if there are any contraindications or other medical reason(s) that the patient cannot use any of the following preferred products when indicated for the patient’s diagnosis?"
  type: text

please_explain_if_there_are_contraindications_or_any_other_medical_reasons_that_the_patient_cannot_use_any_of_the_following_preferred_products_when_indicated_for_the_patients_diagnosis:
  acro_id: "T125"
  human_name: "Please explain if there are contraindications or any other medical reason(s) that the patient cannot use any of the following preferred products when indicated for the patient’s diagnosis?"
  type: text

please_indicate_the_length_of_time_on_rituxan_rituximab:
  acro_id: "T305"
  human_name: "Please indicate the length of time on Rituxan (rituximab):"
  type: text

precertification_requested_by:
  acro_id: "T9"
  human_name: "Precertification Requested By:"
  type: text

primary_cns_lymphoma:
  acro_id: "CB130"
  human_name: "primary CNS lymphoma"
  type: checkbox

primary_cutaneous_b_cell_lymphomas:
  acro_id: "CB123"
  human_name: "Primary cutaneous B-cell lymphomas"
  type: checkbox

primary_icd_code:
  acro_id: "T67"
  human_name: "Primary ICD Code:"
  type: text

primary_progressive_ms_ppms:
  acro_id: "CB166"
  human_name: "Primary-progressive MS (PPMS)"
  type: checkbox

progressive_relapsing_ms_prms:
  acro_id: "CB167"
  human_name: "Progressive-relapsing MS (PRMS)"
  type: checkbox

provider_admin_cb:
  acro_id: "Provider Admin CB"
  human_name: "Provider Admin CB"
  type: text

provider_admin_t:
  acro_id: "Provider Admin T"
  human_name: "Provider Admin T"
  type: text

refractory_hairy_cell_leukemia:
  acro_id: "CB142"
  human_name: "refractory hairy cell leukemia"
  type: checkbox

refractory_immune_thrombocytopenic_purpura:
  acro_id: "CB158"
  human_name: "refractory immune thrombocytopenic purpura"
  type: checkbox

relapsed_hairy_cell_leukemia:
  acro_id: "CB141"
  human_name: "relapsed hairy cell leukemia"
  type: checkbox

relapsing_remitting_ms_rrms:
  acro_id: "CB164"
  human_name: "Relapsing-remitting MS (RRMS)"
  type: checkbox

renflexis_infliximab_abda:
  acro_id: "CB87"
  human_name: "Renflexis (infliximab-abda)"
  type: checkbox

renflexis_infliximab_abda_1:
  acro_id: "CB93"
  human_name: "Renflexis (infliximab-abda)"
  type: checkbox

renflexis_infliximab_abda_2:
  acro_id: "CB116z"
  human_name: "Renflexis (infliximab-abda)"
  type: checkbox

riabni_rituximab_arrx:
  acro_id: "CB60"
  human_name: "Riabni (rituximab-arrx)"
  type: checkbox

rinvoq_upadacitinib:
  acro_id: "CB101a"
  human_name: "Rinvoq (upadacitinib)"
  type: checkbox

rinvoq_upadacitinib_1:
  acro_id: "CB110z"
  human_name: "Rinvoq (upadacitinib)"
  type: checkbox

rinvoq_upadacitinib_2:
  acro_id: "CB122z"
  human_name: "Rinvoq (upadacitinib)"
  type: checkbox

rituxan_rituximab:
  acro_id: "CB61"
  human_name: "Rituxan (rituximab)"
  type: checkbox

ruxience_rituximab_pvvr:
  acro_id: "CB62"
  human_name: "Ruxience (rituximab-pvvr)"
  type: checkbox

ruxience_rituximab_pvvr_1:
  acro_id: "CB73"
  human_name: "Ruxience (rituximab-pvvr)"
  type: checkbox

ruxience_rituximab_pvvr_2:
  acro_id: "CB78"
  human_name: "Ruxience (rituximab-pvvr)"
  type: checkbox

ruxience_rituximab_pvvr_3:
  acro_id: "CB82"
  human_name: "Ruxience (rituximab-pvvr)"
  type: checkbox

secondary_progressive_ms_spms:
  acro_id: "CB165"
  human_name: "Secondary-progressive MS (SPMS)"
  type: checkbox

self_administered:
  acro_id: "1"
  human_name: "Self-administered"
  type: checkbox

severe:
  acro_id: "CB209"
  human_name: "Severe"
  type: checkbox

severe_1:
  acro_id: "CB312"
  human_name: "Severe"
  type: checkbox

simponi_aria_golimumab:
  acro_id: "CB88"
  human_name: "Simponi Aria (golimumab)"
  type: checkbox

simponi_aria_golimumab_1:
  acro_id: "CB94"
  human_name: "Simponi Aria (golimumab)"
  type: checkbox

simponi_aria_golimumab_2:
  acro_id: "CB117z"
  human_name: "Simponi Aria (golimumab)"
  type: checkbox

small_lymphocytic_leukemia:
  acro_id: "CB133"
  human_name: "small lymphocytic leukemia"
  type: checkbox

splenic_marginal_zone_lymphoma:
  acro_id: "CB124"
  human_name: "Splenic marginal zone lymphoma"
  type: checkbox

st_lic:
  acro_id: "T54"
  human_name: "St Lic #:"
  type: text

start_date_dd:
  acro_id: "T3"
  human_name: "Start date: (DD)"
  type: text

start_date_mm:
  acro_id: "T2"
  human_name: "Start date: (MM)"
  type: text

start_date_yyyy:
  acro_id: "T4"
  human_name: "Start date: (YYYY)"
  type: text

start_of_treatment:
  acro_id: "CB1"
  human_name: "Start of treatment"
  type: checkbox

state:
  acro_id: "T17"
  human_name: "State:"
  type: text

state_1:
  acro_id: "T50"
  human_name: "State:"
  type: text

state_2:
  acro_id: "14"
  human_name: "State:"
  type: text

state_3:
  acro_id: "31"
  human_name: "State:"
  type: text

sulfasalazine:
  acro_id: "CB229"
  human_name: "sulfasalazine"
  type: checkbox

t58:
  acro_id: "T58"
  human_name: "T58"
  type: text

tecentriq_atezolizumab:
  acro_id: "CB152"
  human_name: "Tecentriq (atezolizumab)"
  type: checkbox

tin:
  acro_id: "18"
  human_name: "TIN:"
  type: text

tin_1:
  acro_id: "35"
  human_name: "TIN:"
  type: text

truxima_rituximab_abbs:
  acro_id: "CB63"
  human_name: "Truxima (rituximab-abbs)"
  type: checkbox

truxima_rituximab_abbs_1:
  acro_id: "CB74"
  human_name: "Truxima (rituximab-abbs)"
  type: checkbox

truxima_rituximab_abbs_2:
  acro_id: "CB79"
  human_name: "Truxima (rituximab-abbs)"
  type: checkbox

truxima_rituximab_abbs_3:
  acro_id: "CB83"
  human_name: "Truxima (rituximab-abbs)"
  type: checkbox

tyenne_sc_tocilizumab_aazg:
  acro_id: "CB102a"
  human_name: "Tyenne SC (tocilizumab-aazg)"
  type: checkbox

tyenne_sc_tocilizumab_aazg_1:
  acro_id: "CB111z"
  human_name: "Tyenne SC (tocilizumab-aazg)"
  type: checkbox

tyenne_sc_tocilizumab_aazg_2:
  acro_id: "CB123z"
  human_name: "Tyenne SC (tocilizumab-aazg)"
  type: checkbox

upin:
  acro_id: "T57"
  human_name: "UPIN:"
  type: text

wegener_granulomatosis:
  acro_id: "CB104a"
  human_name: "Wegener granulomatosis"
  type: checkbox

when_was_the_members_adverse_reaction_to_the_preferred_drug:
  acro_id: "T80"
  human_name: "When was the member’s adverse reaction to the preferred drug?"
  type: text

when_was_the_members_adverse_reaction_to_the_preferred_drug_1:
  acro_id: "T95"
  human_name: "When was the member’s adverse reaction to the preferred drug?"
  type: text

when_was_the_members_adverse_reaction_to_the_preferred_drug_2:
  acro_id: "T113"
  human_name: "When was the member’s adverse reaction to the preferred drug?"
  type: text

when_was_the_members_trial_and_failure_of_the_preferred_drug:
  acro_id: "T75"
  human_name: "When was the member’s trial and failure of the preferred drug?"
  type: text

when_was_the_members_trial_and_failure_of_the_preferred_drug_1:
  acro_id: "T89"
  human_name: "When was the member’s trial and failure of the preferred drug?"
  type: text

when_was_the_members_trial_and_failure_of_the_preferred_drug_2:
  acro_id: "T104"
  human_name: "When was the member’s trial and failure of the preferred drug?"
  type: text

work_phone:
  acro_id: "T20"
  human_name: "Work Phone:"
  type: text

xeljanzxeljanz_xr_tofacitinib:
  acro_id: "CB103a"
  human_name: "Xeljanz/Xeljanz XR (tofacitinib)"
  type: checkbox

xeljanzxeljanz_xr_tofacitinib_1:
  acro_id: "CB112z"
  human_name: "Xeljanz/Xeljanz XR (tofacitinib)"
  type: checkbox

xeljanzxeljanz_xr_tofacitinib_2:
  acro_id: "CB124z"
  human_name: "Xeljanz/Xeljanz XR (tofacitinib)"
  type: checkbox

yervoy_ipilimumab:
  acro_id: "CB153"
  human_name: "Yervoy (ipilimumab)"
  type: checkbox

yes:
  acro_id: "CB31"
  human_name: "Yes"
  type: checkbox

yes_1:
  acro_id: "CB103"
  human_name: "Yes"
  type: checkbox

yes_10:
  acro_id: "CB203"
  human_name: "Yes"
  type: checkbox

yes_11:
  acro_id: "CB205"
  human_name: "Yes"
  type: checkbox

yes_12:
  acro_id: "CB210"
  human_name: "Yes"
  type: checkbox

yes_13:
  acro_id: "CB212"
  human_name: "Yes"
  type: checkbox

yes_14:
  acro_id: "CB214"
  human_name: "Yes"
  type: checkbox

yes_15:
  acro_id: "CB223"
  human_name: "Yes"
  type: checkbox

yes_16:
  acro_id: "CB281"
  human_name: "Yes"
  type: checkbox

yes_17:
  acro_id: "CB283"
  human_name: "Yes"
  type: checkbox

yes_18:
  acro_id: "CB299"
  human_name: "Yes"
  type: checkbox

yes_19:
  acro_id: "CB301"
  human_name: "Yes"
  type: checkbox

yes_2:
  acro_id: "CB105"
  human_name: "Yes"
  type: checkbox

yes_20:
  acro_id: "CB303"
  human_name: "Yes"
  type: checkbox

yes_21:
  acro_id: "CB306"
  human_name: "Yes"
  type: checkbox

yes_22:
  acro_id: "CB308"
  human_name: "Yes"
  type: checkbox

yes_23:
  acro_id: "CB313"
  human_name: "Yes"
  type: checkbox

yes_24:
  acro_id: "CB315"
  human_name: "Yes"
  type: checkbox

yes_25:
  acro_id: "CB127"
  human_name: "Yes"
  type: checkbox

yes_26:
  acro_id: "CB135"
  human_name: "Yes"
  type: checkbox

yes_27:
  acro_id: "CB137"
  human_name: "Yes"
  type: checkbox

yes_28:
  acro_id: "CB139"
  human_name: "Yes"
  type: checkbox

yes_29:
  acro_id: "CB144"
  human_name: "Yes"
  type: checkbox

yes_3:
  acro_id: "CB168"
  human_name: "Yes"
  type: checkbox

yes_30:
  acro_id: "CB156"
  human_name: "Yes"
  type: checkbox

yes_31:
  acro_id: "CB160"
  human_name: "Yes"
  type: checkbox

yes_32:
  acro_id: "CB162"
  human_name: "Yes"
  type: checkbox

yes_33:
  acro_id: "CB70"
  human_name: "Yes"
  type: checkbox

yes_34:
  acro_id: "CB106a"
  human_name: "Yes"
  type: checkbox

yes_4:
  acro_id: "CB170"
  human_name: "Yes"
  type: checkbox

yes_5:
  acro_id: "CB172"
  human_name: "Yes"
  type: checkbox

yes_6:
  acro_id: "CB174"
  human_name: "Yes"
  type: checkbox

yes_7:
  acro_id: "CB176"
  human_name: "Yes"
  type: checkbox

yes_8:
  acro_id: "CB178"
  human_name: "Yes"
  type: checkbox

yes_9:
  acro_id: "CB180"
  human_name: "Yes"
  type: checkbox

zip:
  acro_id: "T18"
  human_name: "ZIP:"
  type: text

zip_1:
  acro_id: "T51"
  human_name: "ZIP:"
  type: text

zip_2:
  acro_id: "15"
  human_name: "ZIP:"
  type: text

zip_3:
  acro_id: "32"
  human_name: "ZIP:"
  type: text
