{"extraction_metadata": {"total_pages_processed": "10", "document_quality_assessment": "fair", "primary_document_types_identified": ["skyrizi_order_form", "clinical_notes", "lab_results", "insurance_card"], "form_types_detected": ["custom"], "spatial_processing_applied": "true", "extraction_confidence_overall": "0.85"}, "tier_1_mandatory_fields": {"patient_demographics": {"full_name": {"value": "<PERSON><PERSON><PERSON>", "reasoning": "Patient name is listed on the Skyrizi order form and confirmed in the progress notes.", "citations": ["page 1, Skyrizi Order Form", "page 2, Progress Notes"], "confidence": "1.0", "validation_notes": "Name is consistent across multiple documents."}, "date_of_birth": {"value": "1987-02-17", "reasoning": "Date of birth is listed on the Skyrizi order form and confirmed in the progress notes.", "citations": ["page 1, Skyrizi Order Form", "page 2, Progress Notes"], "confidence": "1.0", "validation_notes": "Date format is consistent."}, "address": {"value": "1460 El Camino Real, Arlington, VA-22407", "reasoning": "Patient address is found in the progress notes.", "citations": ["page 2, Progress Notes"], "confidence": "1.0", "validation_notes": "Complete address is available."}, "phone_numbers": {"value": ["************"], "reasoning": "Patient phone number is found in the progress notes.", "citations": ["page 2, Progress Notes"], "confidence": "1.0", "validation_notes": "Only one phone number is listed."}, "medical_record_number": {"value": "D775152", "reasoning": "Account number is listed in the progress notes and can be used as a medical record number.", "citations": ["page 2, Progress Notes"], "confidence": "1.0", "validation_notes": "Account number is consistent across multiple pages."}, "physical_measurements": {"weight": {"value": "190 lbs, 86.18 kg", "required_for": "dosing calculations", "reasoning": "Weight is listed in both pounds and kilograms on page 1 and page 3.", "citations": ["page 1, Skyrizi Order Form", "page 3, Vital Signs"], "confidence": "1.0", "validation_notes": "Weight is consistent across multiple documents."}, "height": {"value": "73 in", "required_for": "BMI/BSA calculations", "reasoning": "Height is listed in inches on page 3.", "citations": ["page 3, Vital Signs"], "confidence": "1.0", "validation_notes": "Height is available."}, "bmi": {"value": "25.06", "reasoning": "BMI is calculated and listed on page 3.", "citations": ["page 3, Vital Signs"], "confidence": "1.0", "validation_notes": "BMI is available."}, "bsa": {"value": "2.1", "reasoning": "BSA is calculated and listed on page 3.", "citations": ["page 3, Vital Signs"], "confidence": "1.0", "validation_notes": "BSA is available."}}}, "insurance_information": {"primary_insurance": {"member_id": {"value": "14866-38657882", "reasoning": "Member ID is found on the insurance card image.", "citations": ["page 9, Insurance Card"], "confidence": "1.0", "validation_notes": "Member ID is available."}, "payer_name": {"value": "Aetna Better Health of Virginia", "reasoning": "Payer name is listed on the insurance card and confirmed on page 10.", "citations": ["page 9, Insurance Card", "page 10, Patient Insurance"], "confidence": "1.0", "validation_notes": "Payer name is consistent across multiple documents."}, "group_number": {"value": null, "reasoning": "Group number is not explicitly listed on the insurance card or other documents.", "citations": ["page 9, Insurance Card", "page 10, Patient Insurance"], "confidence": "0.7", "validation_notes": "Group number may be implicitly included in the policy number, but it is not explicitly stated."}, "plan_type": {"value": "Better Health of Virginia", "reasoning": "Plan type is listed on the insurance card.", "citations": ["page 9, Insurance Card"], "confidence": "1.0", "validation_notes": "Plan type is available."}}}, "prescriber_information": {"physician_name": {"value": "<PERSON>, MD", "reasoning": "Prescribing physician name is listed on the Skyrizi order form and confirmed in the progress notes.", "citations": ["page 1, Skyrizi Order Form", "page 2, Progress Notes"], "confidence": "1.0", "validation_notes": "Physician name is consistent across multiple documents."}, "npi_number": {"value": "**********", "reasoning": "NPI number is listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "NPI number is available."}, "facility_name": {"value": "Extraodinary Gastroenterology", "reasoning": "Facility name is listed in the progress notes.", "citations": ["page 2, Progress Notes"], "confidence": "1.0", "validation_notes": "Facility name is available."}, "facility_address": {"value": "2755 College Ave Ste. 100, Leesburg VA 20176", "reasoning": "Facility address is listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "Complete facility address is available."}, "phone_fax": {"value": {"phone": "************", "fax": "************"}, "reasoning": "Phone and fax numbers are listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "Phone and fax numbers are available."}, "coordinated_care": {"value": null, "reasoning": "No specific details about coordinated care are explicitly mentioned in the documents.", "citations": [], "confidence": "0.6", "validation_notes": "Care coordination may be implicit, but no explicit details are provided."}, "infusion_facility_details": {"value": null, "reasoning": "No specific infusion facility details are mentioned in the documents.", "citations": [], "confidence": "0.6", "validation_notes": "Infusion facility details may be implicit, but no explicit details are provided."}}}, "tier_2_clinical_justification": {"primary_diagnosis": {"icd_code": {"value": "K50.111", "reasoning": "ICD code for <PERSON><PERSON><PERSON>'s disease of colon with rectal bleeding is listed in the assessments section.", "citations": ["page 3, Assessments"], "confidence": "1.0", "validation_notes": "ICD code is available."}, "diagnosis_description": {"value": "<PERSON><PERSON><PERSON>'s disease of colon with rectal bleeding", "reasoning": "Diagnosis description is listed in the assessments section.", "citations": ["page 3, Assessments"], "confidence": "1.0", "validation_notes": "Diagnosis description is available."}, "severity_indicators": {"value": ["problematic", "poor response to Rinvoq"], "reasoning": "Severity indicators are assessed from the clinical notes.", "citations": ["page 3, Assessments"], "confidence": "0.9", "validation_notes": "Severity assessment is based on clinical interpretation."}}, "requested_medication": {"drug_name": {"value": "Skyrizi", "reasoning": "Requested medication is listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "Medication name is available."}, "ndc_number": {"value": null, "reasoning": "NDC number is not provided in the documents.", "citations": [], "confidence": "0.5", "validation_notes": "NDC number is not available."}, "complex_dosing": {"value": null, "reasoning": "No complex dosing calculations are explicitly mentioned in the documents.", "citations": [], "confidence": "0.6", "validation_notes": "Complex dosing calculations may be implicit, but no explicit details are provided."}, "dosage": {"value": "600mg IV at week 0, week 4 and week 8 per protocol; 360mg SQ at week 12 and every 8 weeks thereafter.", "reasoning": "Dosage information is listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "Dosage information is available."}, "frequency": {"value": "Induction: week 0, week 4 and week 8; Maintenance: week 12 and every 8 weeks thereafter", "reasoning": "Dosing frequency is listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "Dosing frequency is available."}, "route": {"value": "IV and SQ", "reasoning": "Route of administration is listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "Route of administration is available."}}, "treatment_history": {"previous_medications": {"value": [{"drug": "Stelara", "dates": "Since 3/8/2023", "response": "significant improvement in abdominal pain and reduced stool frequency"}, {"drug": "budesonide", "dates": "Prior to 4/12/2023", "response": "Unknown"}, {"drug": "mesalamine", "dates": "Prior to 12/4/2022", "response": "minimal benefit"}, {"drug": "oral corticosteroids", "dates": "Prior to 12/4/2022", "response": "minimal benefit"}, {"drug": "predniSONE", "dates": "5/29/2023", "response": "Reasonable to continue steroid for now"}], "reasoning": "Treatment history is extracted from the progress notes.", "citations": ["page 2, Progress Notes", "page 3, Treatment"], "confidence": "0.9", "validation_notes": "Treatment timeline is based on clinical documentation."}, "treatment_failures": {"value": [{"treatment": "Rinvoq", "reason": "poor response", "date": "5/29/2023"}, {"treatment": "mesalamine", "reason": "minimal benefit", "date": "Prior to 12/4/2022"}, {"treatment": "oral corticosteroids", "reason": "minimal benefit", "date": "Prior to 12/4/2022"}], "reasoning": "Treatment failures are identified from the progress notes.", "citations": ["page 2, Progress Notes", "page 3, Assessments"], "confidence": "0.9", "validation_notes": "Treatment failure reasoning is based on clinical assessment."}, "contraindications": {"value": ["ibuprofen", "acetaminophen"], "reasoning": "Allergies are listed on the Skyrizi order form.", "citations": ["page 1, Skyrizi Order Form"], "confidence": "1.0", "validation_notes": "Contraindications are available."}}}, "tier_3_supporting_data": {"laboratory_results": {"recent_labs": {"value": [{"test": "Triglycerides", "result": "112", "date": "2024-04-03", "reference_range": "0-149 mg/dL"}, {"test": "HDL Cholesterol", "result": "37L", "date": "2024-04-03", "reference_range": ">39 mg/dL"}, {"test": "<PERSON><PERSON><PERSON><PERSON>, Total", "result": "176", "date": "2024-04-03", "reference_range": "100-199 mg/dL"}, {"test": "VLDL Cholesterol Cal", "result": "20", "date": "2024-04-03", "reference_range": "5-40 mg/dl"}, {"test": "LDL Chol Calc (NIH)", "result": "119H", "date": "2024-04-03", "reference_range": "0-99 mg/dl"}, {"test": "Sedimentation Rate-<PERSON>ergren", "result": "19H", "date": "2024-04-03", "reference_range": "0-15 mm/hr"}, {"test": "Hemoglobin", "result": "13.7", "date": "2024-04-03", "reference_range": "13.0-17.7 g/dL"}, {"test": "Hematocrit", "result": "43.5", "date": "2024-04-03", "reference_range": "37.5-51.0%"}, {"test": "Platelets", "result": "349", "date": "2024-04-03", "reference_range": "150-450 x1053/UL"}, {"test": "WBC", "result": "6.1", "date": "2024-04-03", "reference_range": "3.4-10.8 x10E3/UL"}, {"test": "RBC", "result": "5.54", "date": "2024-04-03", "reference_range": "4.14-5.80 x10E6/OL"}, {"test": "C-Re<PERSON>, Quant", "result": "23H", "date": "2024-04-03", "reference_range": "0-10 mg/L"}, {"test": "Calcium, Serum", "result": "9.5", "date": "2024-04-03", "reference_range": "8.7-10.2 mg/dL"}, {"test": "<PERSON><PERSON>, <PERSON>, Ser<PERSON>", "result": "8.2", "date": "2024-04-03", "reference_range": "6.0-8.5 g/dl"}, {"test": "ALT (SGPT)", "result": "21", "date": "2024-04-03", "reference_range": "0-44 IU/L"}, {"test": "AST (SGOT)", "result": "23", "date": "2024-04-03", "reference_range": "0-40 IU/L"}, {"test": "Alkaline Phosphatase, S", "result": "120", "date": "2024-04-03", "reference_range": "44-121 IU/L"}, {"test": "<PERSON><PERSON><PERSON><PERSON>, Total", "result": "0.4", "date": "2024-04-03", "reference_range": "0.0-1.2 mg/dL"}, {"test": "Glucose, Serum", "result": "73", "date": "2024-04-03", "reference_range": "70-99 mg/dl"}, {"test": "<PERSON><PERSON><PERSON><PERSON>, Serum", "result": "0.78", "date": "2024-04-03", "reference_range": "0.76-1.27 mg/dL"}, {"test": "BUN", "result": "15", "date": "2024-04-03", "reference_range": "6-24 mg/dL"}], "reasoning": "Relevant lab values and dates are extracted from the lab reports.", "citations": ["page 5, Lipid Panel", "page 5, Sedimentation Rate", "page 6, CBC", "page 7, C-<PERSON><PERSON>", "page 8, Metabolic Panel"], "confidence": "0.9", "validation_notes": "Result accuracy and date correlation are verified."}, "biomarkers": {"value": [], "reasoning": "No disease-specific biomarkers are explicitly mentioned in the documents.", "citations": [], "confidence": "0.6", "validation_notes": "Biomarkers may be implicit, but no explicit details are provided."}}, "imaging_studies": {"studies_performed": {"value": [{"type": "CT scan", "date": "4/12/2023", "findings": "right-sided colitis"}], "reasoning": "Imaging information and key findings are extracted from the progress notes.", "citations": ["page 2, Progress Notes"], "confidence": "0.9", "validation_notes": "Finding accuracy and clinical relevance are verified."}}, "clinical_assessment": {"symptom_severity": {"value": "increasing abdominal pain, multiple mouth sores, anal skin tags, recurrent outbreaks of genital herpes, rectal bleeding", "reasoning": "Symptom severity is assessed from the clinical documentation.", "citations": ["page 2, Progress Notes"], "confidence": "0.9", "validation_notes": "Subjective vs objective measures are considered."}, "functional_status": {"value": null, "reasoning": "No specific functional assessment is mentioned in the documents.", "citations": [], "confidence": "0.6", "validation_notes": "Functional assessment may be implicit, but no explicit details are provided."}}}, "clinical_narrative": {"medical_necessity_story": {"value": "Patient with <PERSON><PERSON><PERSON>'s disease of colon with rectal bleeding, poor response to Rinvoq, reasonable to try <PERSON><PERSON><PERSON>.", "reasoning": "Medical necessity argument is constructed from available data.", "citations": ["multiple sources combined"], "confidence": "0.9", "validation_notes": "Narrative coherence and medical logic are verified."}, "treatment_timeline": {"value": "10/15/2022: Initial onset of worsening abdominal pain and rectal bleeding. 12/4/2022: Presented seeking a second opinion. 3/8/2023: Started on Stelara. 4/12/2023: Symptoms worsened.", "reasoning": "Treatment timeline is constructed from multiple progress notes.", "citations": ["multiple progress notes"], "confidence": "0.9", "validation_notes": "Chronological accuracy is verified."}}, "missing_critical_information": {"fields_not_found": ["NDC number", "Group Number", "Infusion Facility Details", "Functional Status"], "potential_sources": ["Prescription details", "Insurance card", "Scheduling notes", "Physical therapy notes"], "impact_assessment": "Missing information may affect PA likelihood, especially NDC number and infusion facility details.", "required_vs_optional": {"missing_required_fields": ["NDC number"], "missing_optional_fields": ["Group Number", "Infusion Facility Details", "Functional Status"], "critical_gaps": ["NDC number", "Infusion Facility Details"], "recommended_actions": ["Obtain NDC number from prescription details", "Obtain infusion facility details from scheduling notes"]}}, "data_quality_issues": {"illegible_sections": [], "ambiguous_data": [], "conflicting_information": [], "format_validation_failures": []}, "processing_metadata": {"patient_id": "PATIENT001", "file_uri": "https://generativelanguage.googleapis.com/v1beta/files/y5yq4ryrr9qc", "model_used": "gemini-2.0-flash", "processing_time": **********.0563185, "file_size_mb": 7.517548561096191, "extraction_method": "standard"}}