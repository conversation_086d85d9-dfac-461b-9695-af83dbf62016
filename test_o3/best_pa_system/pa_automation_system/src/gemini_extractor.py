"""
GEMINI FLASH DATA EXTRACTOR
============================

Uses Google Gemini Flash to extract structured data from messy, unstructured referral PDFs.
This is the ONE task that requires AI - everything else is pure Python.

Component Separation:
1. <PERSON><PERSON><PERSON> (pypdf) → Finds the questions (field names)
2. Data Extractor (Gemini Flash) → Finds the answers (patient data) ← THIS FILE
3. Form Filler (pypdf) → Writes answers into questions
"""

import argparse
import json
import logging
import base64
from pathlib import Path
from typing import Dict, Any, List
import yaml
import os

# Google AI SDK
import google.generativeai as genai

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GeminiFlashExtractor:
    """Gemini Flash extractor for referral PDFs."""
    
    def __init__(self, api_key: str = None):
        """Initialize Gemini Flash with API key."""
        
        # Get API key from environment or parameter
        self.api_key = api_key or os.getenv('GOOGLE_API_KEY')
        
        if not self.api_key:
            raise ValueError("Google API key required. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Use Gemini Flash model (fastest, cheapest)
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        
        logger.info(f"✅ Gemini Flash initialized")
    
    def load_schema(self, schema_path: Path) -> Dict[str, Any]:
        """Load schema to understand what fields to extract."""
        
        logger.info(f"📋 Loading schema from: {schema_path}")
        
        with open(schema_path, 'r') as f:
            schema = yaml.safe_load(f)
        
        if not schema:
            raise ValueError(f"Empty schema file: {schema_path}")
        
        logger.info(f"✅ Schema loaded: {len(schema)} fields")
        return schema
    
    def identify_extraction_fields(self, schema: Dict[str, Any]) -> List[Dict[str, str]]:
        """Identify key fields that need to be extracted from referral."""
        
        logger.info(f"🔍 Identifying extraction fields from schema...")
        
        # Define patterns for important PA form fields
        key_patterns = {
            "patient_first_name": ["first", "name", "patient"],
            "patient_last_name": ["last", "name", "patient"],
            "patient_dob": ["birth", "dob", "date"],
            "patient_address": ["address", "street"],
            "patient_phone": ["phone", "cell", "telephone"],
            "member_id": ["member", "id", "insurance"],
            "group_number": ["group", "number"],
            "prescriber_name": ["prescriber", "physician", "doctor"],
            "prescriber_npi": ["npi", "provider"],
            "drug_name": ["drug", "medication", "humira"],
            "dosage": ["dose", "dosage", "strength"],
            "diagnosis": ["diagnosis", "condition", "icd"],
            "date_of_service": ["service", "date", "treatment"]
        }
        
        extraction_fields = []
        
        for semantic_name, patterns in key_patterns.items():
            # Find matching fields in schema
            matches = []
            
            for slug, field_info in schema.items():
                if not isinstance(field_info, dict):
                    continue
                    
                human_name = str(field_info.get('human_name', '')).lower()
                acro_id = str(field_info.get('acro_id', '')).lower()
                slug_str = str(slug).lower()
                
                # Check if any pattern matches
                for pattern in patterns:
                    if pattern in human_name or pattern in acro_id or pattern in slug_str:
                        matches.append({
                            "semantic_name": semantic_name,
                            "slug": str(slug),
                            "acro_id": str(field_info.get('acro_id', '')),
                            "human_name": str(field_info.get('human_name', '')),
                            "type": str(field_info.get('type', 'text'))
                        })
                        break
            
            # Take the best match for each semantic field
            if matches:
                best_match = min(matches, key=lambda x: (
                    0 if semantic_name.replace('_', '') in x['slug'].lower() else 1,
                    len(x['human_name'])
                ))
                extraction_fields.append(best_match)
        
        logger.info(f"✅ Identified {len(extraction_fields)} extraction fields:")
        for field in extraction_fields:
            logger.info(f"   {field['semantic_name']} → {field['acro_id']} ({field['human_name']})")
        
        return extraction_fields
    
    def build_gemini_prompt(self, schema: Dict[str, Any]) -> str:
        """Build dynamic prompt using proper Prompt Engineering."""

        logger.info(f"📝 Building dynamic Gemini prompt using Prompt Engineering...")

        # Import prompt builder
        from prompt_builder import create_dynamic_extraction_prompt

        # Create schema-driven prompt
        prompt = create_dynamic_extraction_prompt(schema)

        logger.info(f"✅ Dynamic prompt created: {len(prompt)} characters")
        return prompt
    
    def extract_from_pdf(self, pdf_path: Path, extraction_prompt: str) -> Dict[str, Any]:
        """Extract data from PDF using Gemini Flash."""
        
        logger.info(f"🤖 Extracting data from PDF with Gemini Flash: {pdf_path}")
        
        try:
            # Read PDF file as bytes
            with open(pdf_path, 'rb') as f:
                pdf_bytes = f.read()
            
            # Prepare the content for Gemini
            pdf_part = {
                "mime_type": "application/pdf",
                "data": pdf_bytes
            }
            
            # Generate content with Gemini Flash
            logger.info(f"📤 Sending PDF to Gemini Flash...")
            
            response = self.model.generate_content([
                extraction_prompt,
                pdf_part
            ])
            
            # Parse the response
            response_text = response.text.strip()
            logger.info(f"📥 Received response from Gemini Flash")
            logger.debug(f"Raw response: {response_text}")
            
            # Clean up response (remove any markdown formatting)
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            # Parse JSON
            try:
                extracted_data = json.loads(response_text)
                logger.info(f"✅ Successfully extracted {len(extracted_data)} fields")
                
                # Log extracted fields
                for key, value in extracted_data.items():
                    logger.info(f"   {key}: {value}")
                
                return extracted_data
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ Failed to parse JSON response: {e}")
                logger.error(f"Raw response: {response_text}")
                return {}
        
        except Exception as e:
            logger.error(f"❌ Gemini extraction failed: {e}")
            return {}
    
    def map_to_schema_format(self, extracted_data: Dict[str, Any], extraction_fields: List[Dict[str, str]]) -> Dict[str, Any]:
        """Map extracted data to schema format using slugs."""
        
        logger.info(f"🔗 Mapping extracted data to schema format...")
        
        schema_mapped = {}
        
        for field in extraction_fields:
            semantic_name = field['semantic_name']
            slug = field['slug']
            
            if semantic_name in extracted_data:
                value = extracted_data[semantic_name]
                if value and str(value).strip():  # Only include non-empty values
                    schema_mapped[slug] = str(value).strip()
                    logger.info(f"   {semantic_name} → {slug}: {value}")
        
        logger.info(f"✅ Mapped {len(schema_mapped)} fields to schema format")
        return schema_mapped


def main():
    """Main CLI interface for Gemini Flash extraction."""
    
    parser = argparse.ArgumentParser(
        description="Extract data from referral PDF using Gemini Flash.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    export GOOGLE_API_KEY="your-api-key-here"
    python src/gemini_extractor.py --schema "schemas/abdullah_clean.yaml" --referral "Input Data/Abdullah/referral.pdf" --output "Output/gemini_extracted.json"
        """
    )
    
    parser.add_argument(
        "--schema", 
        type=Path, 
        required=True, 
        help="Path to the PA form schema YAML file."
    )
    
    parser.add_argument(
        "--referral", 
        type=Path, 
        required=True, 
        help="Path to the referral PDF document."
    )
    
    parser.add_argument(
        "--output", 
        type=Path, 
        required=True, 
        help="Path to save the extracted JSON data."
    )
    
    parser.add_argument(
        "--api-key",
        type=str,
        help="Google API key (or set GOOGLE_API_KEY environment variable)"
    )
    
    args = parser.parse_args()
    
    print(f"🤖 GEMINI FLASH DATA EXTRACTOR")
    print(f"=" * 50)
    print(f"Schema: {args.schema}")
    print(f"Referral: {args.referral}")
    print(f"Output: {args.output}")
    print(f"=" * 50)
    
    try:
        # Validate input files
        for file_path in [args.schema, args.referral]:
            if not file_path.exists():
                print(f"❌ Error: File not found: {file_path}")
                return 1
        
        # Initialize Gemini extractor
        extractor = GeminiFlashExtractor(args.api_key)
        
        # Load schema
        schema = extractor.load_schema(args.schema)
        
        # Build dynamic Gemini prompt using Prompt Engineering
        extraction_prompt = extractor.build_gemini_prompt(schema)

        # Extract data with Gemini Flash
        extracted_data = extractor.extract_from_pdf(args.referral, extraction_prompt)

        if not extracted_data:
            print(f"❌ No data extracted from referral PDF")
            return 1

        # The extracted data is already in the correct format from prompt engineering
        schema_mapped = extracted_data
        
        # Save results
        args.output.parent.mkdir(parents=True, exist_ok=True)
        
        with open(args.output, 'w') as f:
            json.dump(schema_mapped, f, indent=2)
        
        print(f"✅ Gemini Flash extraction complete!")
        print(f"📊 Extracted {len(schema_mapped)} fields")
        print(f"💾 Data saved to: {args.output}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Extraction failed: {e}")
        logger.error(f"Gemini extraction failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
