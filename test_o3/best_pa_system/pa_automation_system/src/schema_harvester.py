"""
SCHEMA HARVESTER
================

Analyzes any blank PA form PDF and creates a schema (map) of all fillable fields.
This is the foundation for handling "unseen forms" - the core requirement.

Usage:
    python src/schema_harvester.py --pdf "Input Data/Patient A/PA.pdf" --output "schemas/patient_a_schema.yaml"
"""

import argparse
import re
import unicodedata
import logging
from pathlib import Path
from typing import Dict, Any

import yaml
from PyPDF2 import PdfReader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_slug(text: str) -> str:
    """Creates a clean, URL-friendly slug from a string."""
    if not text:
        return "unknown_field"
    
    # Normalize unicode and convert to ASCII
    text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('ascii')
    # Remove non-alphanumeric characters except spaces and hyphens
    text = re.sub(r'[^\w\s-]', '', text).strip().lower()
    # Replace spaces and hyphens with underscores
    text = re.sub(r'[-\s]+', '_', text)
    # Remove leading/trailing underscores
    text = text.strip('_')
    
    return text if text else "unknown_field"


def harvest_schema(pdf_path: Path) -> dict:
    """
    Analyzes a PDF form and extracts a schema of its fillable fields.

    Args:
        pdf_path: The path to the blank PA form PDF.

    Returns:
        A dictionary representing the form's schema.
    """
    logger.info(f"Harvesting schema from '{pdf_path}'...")
    schema = {}
    try:
        reader = PdfReader(pdf_path)
        fields = reader.get_fields()

        if not fields:
            logger.warning("No AcroForm widgets found in this PDF. It may be a flat, non-interactive form.")
            return {}

        for field_name, properties in fields.items():
            # The real, ugly widget name - convert to string
            acro_id = str(field_name)

            # Get field object for detailed analysis
            obj = properties.get_object()

            # Extract human-readable name from various sources
            human_readable_name = ""
            if obj:
                # Try tooltip first
                tooltip = obj.get('/TU', '')
                if tooltip:
                    human_readable_name = str(tooltip)
                else:
                    # Fall back to field name
                    human_readable_name = acro_id
            else:
                human_readable_name = acro_id

            if not human_readable_name:
                logger.warning(f"Skipping field with no name: {acro_id}")
                continue

            slug = generate_slug(human_readable_name)

            # Handle duplicate slugs
            original_slug = slug
            counter = 1
            while slug in schema:
                slug = f"{original_slug}_{counter}"
                counter += 1

            # Determine field type
            field_type = "text"
            if obj and obj.get('/FT') == '/Btn':
                field_type = "checkbox"

            schema[slug] = {
                "acro_id": acro_id,
                "human_name": human_readable_name,
                "type": field_type,
            }
        
        logger.info(f"Successfully discovered {len(schema)} fields.")
        return schema

    except Exception as e:
        logger.error(f"Failed to harvest schema from '{pdf_path}': {e}")
        return {}


def main():
    parser = argparse.ArgumentParser(description="Harvest a schema from a fillable PDF form.")
    parser.add_argument("--pdf", type=Path, required=True, help="Path to the blank PA form PDF.")
    parser.add_argument("--output", type=Path, required=True, help="Path to save the generated YAML schema file.")
    args = parser.parse_args()

    args.output.parent.mkdir(parents=True, exist_ok=True)

    schema_data = harvest_schema(args.pdf)

    if schema_data:
        with open(args.output, 'w') as f:
            yaml.dump(schema_data, f, indent=2, sort_keys=True)
        print(f"✅ Schema successfully saved to: {args.output}")
    else:
        print(f"❌ No fields found in PDF. Cannot create schema.")


if __name__ == "__main__":
    main()
