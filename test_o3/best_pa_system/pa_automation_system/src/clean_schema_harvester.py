"""
CLEAN SCHEMA HARVESTER
======================

Simple, clean schema harvester that creates human-readable YAML files.
Avoids PyPDF2 object serialization issues.
"""

import argparse
import re
import unicodedata
import logging
from pathlib import Path
from typing import Dict, Any
import json

from PyPDF2 import PdfReader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_slug(text: str) -> str:
    """Creates a clean, URL-friendly slug from a string."""
    if not text:
        return "unknown_field"
    
    # Normalize unicode and convert to ASCII
    text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('ascii')
    # Remove non-alphanumeric characters except spaces and hyphens
    text = re.sub(r'[^\w\s-]', '', text).strip().lower()
    # Replace spaces and hyphens with underscores
    text = re.sub(r'[-\s]+', '_', text)
    # Remove leading/trailing underscores
    text = text.strip('_')
    
    return text if text else "unknown_field"


def harvest_clean_schema(pdf_path: Path) -> Dict[str, Any]:
    """
    Analyzes a PDF form and extracts a clean schema of its fillable fields.
    Returns only basic Python types (no PyPDF2 objects).
    """
    logger.info(f"🔍 Harvesting clean schema from '{pdf_path}'...")
    
    try:
        reader = PdfReader(pdf_path)
        fields = reader.get_fields()

        if not fields:
            logger.warning("❌ No AcroForm widgets found in this PDF.")
            return {"error": "No fillable fields found"}

        schema = {}
        field_count = 0

        for field_name, properties in fields.items():
            try:
                # Convert field name to clean string
                acro_id = str(field_name)
                
                # Get field object for analysis
                obj = properties.get_object() if hasattr(properties, 'get_object') else None
                
                # Extract human-readable name
                human_name = acro_id  # Default to field name
                if obj:
                    tooltip = obj.get('/TU', '')
                    if tooltip:
                        human_name = str(tooltip)
                
                # Generate clean slug
                slug = generate_slug(human_name)
                
                # Handle duplicate slugs
                original_slug = slug
                counter = 1
                while slug in schema:
                    slug = f"{original_slug}_{counter}"
                    counter += 1
                
                # Determine field type
                field_type = "text"
                if obj and obj.get('/FT') == '/Btn':
                    field_type = "checkbox"
                
                # Store clean field information
                schema[slug] = {
                    "acro_id": acro_id,
                    "human_name": human_name,
                    "type": field_type
                }
                
                field_count += 1
                
            except Exception as e:
                logger.warning(f"Error processing field {field_name}: {e}")
                continue
        
        logger.info(f"✅ Successfully discovered {field_count} fields.")
        return schema

    except Exception as e:
        logger.error(f"❌ Failed to harvest schema from '{pdf_path}': {e}")
        return {"error": str(e)}


def save_clean_schema(schema: Dict[str, Any], output_path: Path) -> bool:
    """Save schema to clean YAML file."""
    
    try:
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create clean YAML content manually to avoid PyPDF2 object serialization
        yaml_content = []
        
        # Add header
        yaml_content.append("# PA Form Schema")
        yaml_content.append(f"# Generated from: {output_path.name}")
        yaml_content.append(f"# Total fields: {len(schema)}")
        yaml_content.append("")
        
        # Add each field
        for slug, field_info in sorted(schema.items()):
            yaml_content.append(f"{slug}:")
            yaml_content.append(f"  acro_id: \"{field_info['acro_id']}\"")
            yaml_content.append(f"  human_name: \"{field_info['human_name']}\"")
            yaml_content.append(f"  type: {field_info['type']}")
            yaml_content.append("")
        
        # Write to file
        with open(output_path, 'w') as f:
            f.write('\n'.join(yaml_content))
        
        logger.info(f"💾 Clean schema saved to: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to save schema to '{output_path}': {e}")
        return False


def main():
    """Main CLI interface for clean schema harvesting."""
    
    parser = argparse.ArgumentParser(
        description="Harvest a clean schema from a fillable PDF form.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python src/clean_schema_harvester.py --pdf "Input Data/Abdullah/abdullah_pa.pdf" --output "schemas/abdullah_clean.yaml"
        """
    )
    
    parser.add_argument(
        "--pdf", 
        type=Path, 
        required=True, 
        help="Path to the blank PA form PDF."
    )
    
    parser.add_argument(
        "--output", 
        type=Path, 
        required=True, 
        help="Path to save the generated YAML schema file."
    )
    
    args = parser.parse_args()
    
    # Validate input file
    if not args.pdf.exists():
        print(f"❌ Error: PDF file not found: {args.pdf}")
        return 1
    
    print(f"🔍 CLEAN SCHEMA HARVESTER")
    print(f"=" * 50)
    print(f"Input PDF: {args.pdf}")
    print(f"Output Schema: {args.output}")
    print(f"=" * 50)
    
    # Harvest schema
    schema_data = harvest_clean_schema(args.pdf)
    
    if "error" in schema_data:
        print(f"❌ Error: {schema_data['error']}")
        return 1
    
    if not schema_data:
        print(f"❌ No fields found in PDF. Cannot create schema.")
        return 1
    
    # Save schema
    if save_clean_schema(schema_data, args.output):
        print(f"✅ Schema harvesting complete!")
        print(f"📊 Discovered {len(schema_data)} fillable fields")
        print(f"💾 Clean schema saved to: {args.output}")
        return 0
    else:
        print(f"❌ Failed to save schema.")
        return 1


if __name__ == "__main__":
    exit(main())
