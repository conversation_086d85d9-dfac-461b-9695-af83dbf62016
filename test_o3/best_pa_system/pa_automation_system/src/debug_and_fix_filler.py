"""
DEBUG AND FIX FILLER
====================

Diagnoses and fixes the field mapping issues causing 0% fill rate.
Implements smart field matching between extracted data and schema fields.
"""

import argparse
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple
import yaml
from datetime import datetime

import fitz  # PyMuPDF

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_schema(schema_path: Path) -> Dict[str, Any]:
    """Load schema file."""
    
    logger.info(f"📋 Loading schema from: {schema_path}")
    
    with open(schema_path, 'r') as f:
        schema = yaml.safe_load(f)
    
    if not schema:
        raise ValueError(f"Empty schema file: {schema_path}")
    
    logger.info(f"✅ Schema loaded: {len(schema)} fields")
    return schema


def load_extracted_data(data_path: Path) -> Dict[str, Any]:
    """Load extracted data JSON."""
    
    logger.info(f"📊 Loading extracted data from: {data_path}")
    
    with open(data_path, 'r') as f:
        data = json.load(f)
    
    if not data:
        raise ValueError(f"Empty data file: {data_path}")
    
    logger.info(f"✅ Extracted data loaded: {len(data)} fields")
    for key, value in data.items():
        logger.info(f"   {key}: {value}")
    
    return data


def smart_field_matching(extracted_data: Dict[str, Any], schema: Dict[str, Any]) -> List[Tuple[str, str, str, str]]:
    """Smart matching between extracted data and schema fields."""
    
    logger.info(f"🧠 SMART FIELD MATCHING")
    logger.info(f"Extracted fields: {list(extracted_data.keys())}")
    
    matches = []
    
    # Define smart mapping rules
    mapping_rules = {
        "address": ["address", "address_1", "address_2", "address_3"],
        "phone": ["cell_phone", "phone", "home_phone"],
        "date_dd": ["date_dd", "date_dd_1", "date_dd_2"],
        "name": ["first_name", "last_name", "name"],
        "leflunomide": ["group", "member_id", "leflunomide"],  # Member ID
        "group": ["group", "group_number"],
        "physicians_office": ["physicians_office", "prescriber_name"],
        "npi_1": ["npi", "npi_1", "dea"],
        "medication": ["humira_adalimumab", "medication", "drug_name"],
        "dose": ["dose", "dosage"],
        "please_explain_if_there_are_any_contraindications_or_other_medical_reasons_that_the_patient_cannot_use_any_of_the_following_preferred_products_when_indicated_for_the_patients_diagnosis": ["diagnosis", "condition"]
    }
    
    for extracted_key, extracted_value in extracted_data.items():
        logger.info(f"\n🔍 Matching extracted field: {extracted_key} = '{extracted_value}'")
        
        # Get possible schema field names for this extracted field
        possible_schema_fields = mapping_rules.get(extracted_key, [extracted_key])
        
        # Find the best match in schema
        best_match = None
        for possible_field in possible_schema_fields:
            if possible_field in schema:
                field_info = schema[possible_field]
                if isinstance(field_info, dict):
                    acro_id = str(field_info.get('acro_id', ''))
                    field_type = str(field_info.get('type', 'text'))
                    human_name = str(field_info.get('human_name', ''))
                    
                    if acro_id:
                        best_match = (extracted_key, possible_field, acro_id, field_type)
                        logger.info(f"  ✅ MATCH: {extracted_key} → {possible_field} → {acro_id} ({field_type})")
                        break
        
        if best_match:
            matches.append(best_match)
        else:
            logger.warning(f"  ❌ NO MATCH: {extracted_key}")
    
    logger.info(f"\n✅ Smart matching complete: {len(matches)} matches found")
    return matches


def fill_pdf_with_smart_matching(pdf_path: Path, extracted_data: Dict[str, Any], matches: List[Tuple], output_path: Path) -> Dict[str, Any]:
    """Fill PDF using smart field matching."""
    
    logger.info(f"📝 FILLING PDF WITH SMART MATCHING: {pdf_path}")
    
    try:
        # Open PDF with PyMuPDF
        doc = fitz.open(str(pdf_path))
        
        # Get all widgets
        all_widgets = {}
        for page_num in range(len(doc)):
            page = doc[page_num]
            widgets = list(page.widgets())
            
            for widget in widgets:
                field_name = widget.field_name
                if field_name:
                    if field_name not in all_widgets:
                        all_widgets[field_name] = []
                    all_widgets[field_name].append({
                        "page": page_num,
                        "widget": widget
                    })
        
        logger.info(f"📋 Found {len(all_widgets)} unique field names in PDF")
        
        # Fill fields using smart matches
        successful_fills = 0
        failed_fills = []
        fill_details = []
        
        for extracted_key, schema_field, acro_id, field_type in matches:
            value = extracted_data[extracted_key]
            
            logger.info(f"\n📝 Filling: {extracted_key} → {acro_id} = '{value}'")
            
            try:
                if acro_id in all_widgets:
                    # Fill all instances of this field
                    for widget_info in all_widgets[acro_id]:
                        page_num = widget_info["page"]
                        
                        # Get fresh widget reference from page
                        page = doc[page_num]
                        page_widgets = list(page.widgets())
                        
                        target_widget = None
                        for pw in page_widgets:
                            if pw.field_name == acro_id:
                                target_widget = pw
                                break
                        
                        if target_widget:
                            try:
                                if target_widget.field_type == fitz.PDF_WIDGET_TYPE_TEXT:
                                    target_widget.field_value = str(value)
                                    target_widget.update()
                                    logger.info(f"    ✅ Text field {acro_id} (page {page_num + 1}): '{value}'")
                                    
                                elif target_widget.field_type == fitz.PDF_WIDGET_TYPE_BUTTON:
                                    if field_type == "checkbox":
                                        target_widget.field_value = True
                                        target_widget.update()
                                        logger.info(f"    ✅ Checkbox {acro_id} (page {page_num + 1}): checked")
                                    else:
                                        # Try setting as text for button fields
                                        target_widget.field_value = str(value)
                                        target_widget.update()
                                        logger.info(f"    ✅ Button {acro_id} (page {page_num + 1}): '{value}'")
                                
                                else:
                                    # Try as text for unknown types
                                    target_widget.field_value = str(value)
                                    target_widget.update()
                                    logger.info(f"    ✅ Unknown field {acro_id} (page {page_num + 1}): '{value}'")
                                
                            except Exception as e:
                                logger.error(f"    ❌ Widget update failed: {e}")
                                continue
                    
                    successful_fills += 1
                    fill_details.append({
                        "extracted_key": extracted_key,
                        "acro_id": acro_id,
                        "value": str(value),
                        "status": "success"
                    })
                    
                else:
                    failed_fills.append(f"{acro_id} ({extracted_key}): Field not found in PDF")
                    logger.warning(f"    ❌ {acro_id}: Field not found in PDF")
                    fill_details.append({
                        "extracted_key": extracted_key,
                        "acro_id": acro_id,
                        "value": str(value),
                        "status": "field_not_found"
                    })
                    
            except Exception as e:
                failed_fills.append(f"{acro_id} ({extracted_key}): {str(e)}")
                logger.error(f"    ❌ {acro_id}: {e}")
                fill_details.append({
                    "extracted_key": extracted_key,
                    "acro_id": acro_id,
                    "value": str(value),
                    "status": "error",
                    "error": str(e)
                })
        
        # Save filled PDF
        output_path.parent.mkdir(parents=True, exist_ok=True)
        doc.save(str(output_path))
        doc.close()
        
        # Verify filled fields
        verification = verify_filled_pdf(output_path)
        
        results = {
            "total_matches": len(matches),
            "successful_fills": successful_fills,
            "failed_fills": len(failed_fills),
            "fill_rate": (successful_fills / len(matches)) * 100 if matches else 0,
            "verification": verification,
            "fill_details": fill_details,
            "failed_fills_list": failed_fills
        }
        
        logger.info(f"\n📊 FILL RESULTS:")
        logger.info(f"   Total matches: {len(matches)}")
        logger.info(f"   Successful fills: {successful_fills}")
        logger.info(f"   Failed fills: {len(failed_fills)}")
        logger.info(f"   Fill rate: {results['fill_rate']:.1f}%")
        logger.info(f"   Verification: {verification['filled_fields']}/{verification['total_widgets']} fields have values")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Failed to fill PDF: {e}")
        return {"error": str(e)}


def verify_filled_pdf(pdf_path: Path) -> Dict[str, Any]:
    """Verify that fields were actually filled and are visible."""
    
    logger.info(f"🔍 Verifying filled PDF: {pdf_path}")
    
    try:
        doc = fitz.open(str(pdf_path))
        
        filled_fields = {}
        total_widgets = 0
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            widgets = list(page.widgets())
            
            for widget in widgets:
                field_name = widget.field_name
                if field_name:
                    total_widgets += 1
                    try:
                        field_value = widget.field_value
                        if field_value and str(field_value).strip():
                            if field_name not in filled_fields:
                                filled_fields[field_name] = []
                            filled_fields[field_name].append({
                                "page": page_num + 1,
                                "value": str(field_value)
                            })
                    except:
                        pass
        
        doc.close()
        
        verification_result = {
            "total_widgets": total_widgets,
            "filled_fields": len(filled_fields),
            "fill_rate": len(filled_fields) / total_widgets * 100 if total_widgets > 0 else 0,
            "filled_values": filled_fields
        }
        
        return verification_result
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return {"error": str(e)}


def main():
    """Main CLI interface for debug and fix filler."""
    
    parser = argparse.ArgumentParser(
        description="Debug and fix PDF form filling with smart field matching.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python src/debug_and_fix_filler.py --pdf "Input Data/Abdullah/abdullah_pa.pdf" --schema "Output/schema_20250613_231054.yaml" --data "Output/extracted_20250613_231054.json" --output "Output/FIXED_filled_form.pdf"
        """
    )
    
    parser.add_argument(
        "--pdf", 
        type=Path, 
        required=True, 
        help="Path to the blank PA form PDF."
    )
    
    parser.add_argument(
        "--schema", 
        type=Path, 
        required=True, 
        help="Path to the schema YAML file."
    )
    
    parser.add_argument(
        "--data", 
        type=Path, 
        required=True, 
        help="Path to the extracted data JSON file."
    )
    
    parser.add_argument(
        "--output", 
        type=Path, 
        required=True, 
        help="Path to save the filled PDF."
    )
    
    args = parser.parse_args()
    
    print(f"🔧 DEBUG AND FIX FILLER")
    print(f"=" * 50)
    print(f"PDF: {args.pdf}")
    print(f"Schema: {args.schema}")
    print(f"Data: {args.data}")
    print(f"Output: {args.output}")
    print(f"=" * 50)
    
    try:
        # Validate input files
        for file_path in [args.pdf, args.schema, args.data]:
            if not file_path.exists():
                print(f"❌ Error: File not found: {file_path}")
                return 1
        
        # Load schema and data
        schema = load_schema(args.schema)
        extracted_data = load_extracted_data(args.data)
        
        # Smart field matching
        matches = smart_field_matching(extracted_data, schema)
        
        if not matches:
            print(f"❌ No field matches found. Check data and schema compatibility.")
            return 1
        
        # Fill PDF with smart matching
        results = fill_pdf_with_smart_matching(args.pdf, extracted_data, matches, args.output)
        
        if "error" in results:
            print(f"❌ Form filling failed: {results['error']}")
            return 1
        
        print(f"\n🎯 DEBUG AND FIX RESULTS:")
        print(f"📊 Fill rate: {results['fill_rate']:.1f}% ({results['successful_fills']}/{results['total_matches']})")
        
        verification = results['verification']
        print(f"🔍 Verification: {verification['fill_rate']:.1f}% ({verification['filled_fields']}/{verification['total_widgets']})")
        print(f"💾 Fixed PDF saved to: {args.output}")
        
        if results['fill_rate'] >= 50:
            print(f"🎉 SUCCESS: Significant improvement in fill rate!")
        elif results['fill_rate'] > 0:
            print(f"✅ PROGRESS: Some fields now filling correctly")
        else:
            print(f"❌ STILL BROKEN: No fields filled")
        
        # Show successful fills
        if results['successful_fills'] > 0:
            print(f"\n✅ Successfully filled fields:")
            for detail in results['fill_details']:
                if detail['status'] == 'success':
                    print(f"   {detail['extracted_key']} → {detail['acro_id']}: '{detail['value']}'")
        
        # Show failures
        if results['failed_fills'] > 0:
            print(f"\n❌ Failed fills:")
            for failure in results['failed_fills_list'][:5]:  # Show first 5
                print(f"   {failure}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Debug and fix failed: {e}")
        logger.error(f"Debug and fix failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
