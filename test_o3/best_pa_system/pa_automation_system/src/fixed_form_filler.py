"""
FIXED FORM FILLER
=================

Form filler that actually makes fields visible using PyMuPDF instead of PyPDF2.
PyPDF2 has visibility issues with certain PDFs - PyMuPDF preserves form fields better.
"""

import argparse
import json
import logging
from pathlib import Path
from typing import Dict, Any
import yaml
from datetime import datetime

import fitz  # PyMuPDF

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_schema(schema_path: Path) -> Dict[str, Any]:
    """Load schema file."""
    
    logger.info(f"📋 Loading schema from: {schema_path}")
    
    with open(schema_path, 'r') as f:
        schema = yaml.safe_load(f)
    
    if not schema:
        raise ValueError(f"Empty schema file: {schema_path}")
    
    logger.info(f"✅ Schema loaded: {len(schema)} fields")
    return schema


def load_extracted_data(data_path: Path) -> Dict[str, Any]:
    """Load extracted data JSON."""
    
    logger.info(f"📊 Loading extracted data from: {data_path}")
    
    with open(data_path, 'r') as f:
        data = json.load(f)
    
    if not data:
        raise ValueError(f"Empty data file: {data_path}")
    
    logger.info(f"✅ Extracted data loaded: {len(data)} fields")
    for key, value in data.items():
        logger.info(f"   {key}: {value}")
    
    return data


def map_data_to_acroform(schema: Dict[str, Any], extracted_data: Dict[str, Any]) -> Dict[str, str]:
    """Map extracted data to PDF AcroForm field IDs using schema."""
    
    logger.info(f"🔗 Mapping data to AcroForm fields...")
    
    form_values = {}
    mapped_count = 0
    
    for slug, field_info in schema.items():
        if not isinstance(field_info, dict):
            continue
            
        # Get the PDF field ID from schema
        acro_id = str(field_info.get('acro_id', ''))
        field_type = str(field_info.get('type', 'text'))
        
        # Check if we have data for this field
        if str(slug) in extracted_data:
            value = extracted_data[str(slug)]
            
            if acro_id and value:
                form_values[acro_id] = {
                    "value": str(value),
                    "type": field_type,
                    "slug": str(slug)
                }
                mapped_count += 1
                logger.info(f"   ✅ {slug} → {acro_id}: '{value}' ({field_type})")
    
    logger.info(f"✅ Mapped {mapped_count} fields to AcroForm IDs")
    return form_values


def fill_pdf_with_pymupdf(pdf_path: Path, form_values: Dict[str, Dict], output_path: Path) -> bool:
    """Fill PDF form using PyMuPDF for better visibility."""
    
    logger.info(f"📝 Filling PDF with PyMuPDF: {pdf_path}")
    
    try:
        # Open PDF with PyMuPDF
        doc = fitz.open(str(pdf_path))
        
        successful_fills = 0
        failed_fills = []
        
        # Get all widgets across all pages
        all_widgets = {}
        for page_num in range(len(doc)):
            page = doc[page_num]
            widgets = list(page.widgets())
            
            for widget in widgets:
                field_name = widget.field_name
                if field_name:
                    if field_name not in all_widgets:
                        all_widgets[field_name] = []
                    all_widgets[field_name].append({
                        "page": page_num,
                        "widget": widget
                    })
        
        logger.info(f"📋 Found {len(all_widgets)} unique field names in PDF")
        
        # Fill fields
        for acro_id, field_data in form_values.items():
            value = field_data["value"]
            field_type = field_data["type"]
            slug = field_data["slug"]
            
            try:
                if acro_id in all_widgets:
                    # Fill all instances of this field
                    for widget_info in all_widgets[acro_id]:
                        page_num = widget_info["page"]
                        widget = widget_info["widget"]
                        
                        # Get fresh widget reference from page
                        page = doc[page_num]
                        page_widgets = list(page.widgets())
                        
                        target_widget = None
                        for pw in page_widgets:
                            if pw.field_name == acro_id:
                                target_widget = pw
                                break
                        
                        if target_widget:
                            if target_widget.field_type == fitz.PDF_WIDGET_TYPE_TEXT:
                                target_widget.field_value = value
                                target_widget.update()
                                logger.info(f"   ✅ Text field {acro_id} (page {page_num + 1}): '{value}'")
                                
                            elif target_widget.field_type == fitz.PDF_WIDGET_TYPE_BUTTON:
                                if field_type == "checkbox":
                                    target_widget.field_value = True
                                    target_widget.update()
                                    logger.info(f"   ✅ Checkbox {acro_id} (page {page_num + 1}): checked")
                                else:
                                    # Try setting as text for button fields
                                    target_widget.field_value = value
                                    target_widget.update()
                                    logger.info(f"   ✅ Button {acro_id} (page {page_num + 1}): '{value}'")
                            
                            else:
                                # Try as text for unknown types
                                target_widget.field_value = value
                                target_widget.update()
                                logger.info(f"   ✅ Unknown field {acro_id} (page {page_num + 1}): '{value}'")
                    
                    successful_fills += 1
                    
                else:
                    failed_fills.append(f"{acro_id} ({slug}): Field not found in PDF")
                    logger.warning(f"   ❌ {acro_id} ({slug}): Field not found in PDF")
                    
            except Exception as e:
                failed_fills.append(f"{acro_id} ({slug}): {str(e)}")
                logger.error(f"   ❌ {acro_id} ({slug}): {e}")
        
        # Save filled PDF
        output_path.parent.mkdir(parents=True, exist_ok=True)
        doc.save(str(output_path))
        doc.close()
        
        logger.info(f"✅ Filled PDF saved: {output_path}")
        logger.info(f"📊 Fill results: {successful_fills}/{len(form_values)} successful")
        
        if failed_fills:
            logger.warning(f"❌ Failed fills:")
            for failure in failed_fills:
                logger.warning(f"   {failure}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fill PDF: {e}")
        return False


def verify_filled_pdf(pdf_path: Path) -> Dict[str, Any]:
    """Verify that fields were actually filled and are visible."""
    
    logger.info(f"🔍 Verifying filled PDF: {pdf_path}")
    
    try:
        doc = fitz.open(str(pdf_path))
        
        filled_fields = {}
        total_widgets = 0
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            widgets = list(page.widgets())
            
            for widget in widgets:
                field_name = widget.field_name
                if field_name:
                    total_widgets += 1
                    try:
                        field_value = widget.field_value
                        if field_value and str(field_value).strip():
                            if field_name not in filled_fields:
                                filled_fields[field_name] = []
                            filled_fields[field_name].append({
                                "page": page_num + 1,
                                "value": str(field_value)
                            })
                    except:
                        pass
        
        doc.close()
        
        verification_result = {
            "total_widgets": total_widgets,
            "filled_fields": len(filled_fields),
            "fill_rate": len(filled_fields) / total_widgets * 100 if total_widgets > 0 else 0,
            "filled_values": filled_fields
        }
        
        logger.info(f"📊 VERIFICATION RESULTS:")
        logger.info(f"   Total widgets: {total_widgets}")
        logger.info(f"   Filled fields: {len(filled_fields)}")
        logger.info(f"   Fill rate: {verification_result['fill_rate']:.1f}%")
        
        if filled_fields:
            logger.info(f"   Filled field examples:")
            for field_name, instances in list(filled_fields.items())[:5]:
                logger.info(f"     {field_name}: {instances[0]['value']}")
        
        return verification_result
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return {"error": str(e)}


def main():
    """Main CLI interface for fixed form filling."""
    
    parser = argparse.ArgumentParser(
        description="Fill PDF form using PyMuPDF for better visibility.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python src/fixed_form_filler.py --pdf "Input Data/Abdullah/abdullah_pa.pdf" --schema "schemas/abdullah_clean.yaml" --data "Output/extracted_data.json" --output "Output/fixed_filled_form.pdf"
        """
    )
    
    parser.add_argument(
        "--pdf", 
        type=Path, 
        required=True, 
        help="Path to the blank PA form PDF."
    )
    
    parser.add_argument(
        "--schema", 
        type=Path, 
        required=True, 
        help="Path to the schema YAML file."
    )
    
    parser.add_argument(
        "--data", 
        type=Path, 
        required=True, 
        help="Path to the extracted data JSON file."
    )
    
    parser.add_argument(
        "--output", 
        type=Path, 
        required=True, 
        help="Path to save the filled PDF."
    )
    
    args = parser.parse_args()
    
    print(f"📝 FIXED FORM FILLER (PyMuPDF)")
    print(f"=" * 50)
    print(f"PDF: {args.pdf}")
    print(f"Schema: {args.schema}")
    print(f"Data: {args.data}")
    print(f"Output: {args.output}")
    print(f"=" * 50)
    
    try:
        # Validate input files
        for file_path in [args.pdf, args.schema, args.data]:
            if not file_path.exists():
                print(f"❌ Error: File not found: {file_path}")
                return 1
        
        # Load schema and data
        schema = load_schema(args.schema)
        extracted_data = load_extracted_data(args.data)
        
        # Map data to AcroForm fields
        form_values = map_data_to_acroform(schema, extracted_data)
        
        if not form_values:
            print(f"❌ No fields mapped. Check schema and data compatibility.")
            return 1
        
        # Fill PDF form with PyMuPDF
        if fill_pdf_with_pymupdf(args.pdf, form_values, args.output):
            print(f"✅ Form filling complete!")
            
            # Verify the results
            verification = verify_filled_pdf(args.output)
            
            if "error" not in verification:
                print(f"📊 Verification: {verification['fill_rate']:.1f}% fields have visible values")
                print(f"💾 Filled PDF saved to: {args.output}")
                
                if verification['fill_rate'] >= 50:
                    print(f"🎉 SUCCESS: Fields should be visible in the PDF!")
                else:
                    print(f"⚠️ WARNING: Low fill rate - some fields may not be visible")
            
            return 0
        else:
            print(f"❌ Form filling failed.")
            return 1
        
    except Exception as e:
        print(f"❌ Form filling failed: {e}")
        logger.error(f"Form filling failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
