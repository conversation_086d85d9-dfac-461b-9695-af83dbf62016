"""
COMPLETE PA AUTOMATION PIPELINE
===============================

Demonstrates the complete 3-component architecture:

1. <PERSON><PERSON>a Harvester (pypdf) → Finds the questions (field names)
2. Data Extractor (Gemini Flash) → Finds the answers (patient data)  
3. Form Filler (pypdf/PyMuPDF) → Writes answers into questions

This shows the proper separation of concerns with Gemini Flash used ONLY
for the one task it's uniquely good at: extracting structured data from
messy, unstructured referral documents.
"""

import argparse
import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CompletePAPipeline:
    """Complete PA automation pipeline with proper tool separation."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.results = {}
    
    def step1_harvest_schema(self, pdf_path: Path, schema_output: Path) -> bool:
        """Step 1: Schema Harvester (pypdf) - Find the questions."""
        
        logger.info(f"📋 STEP 1: SCHEMA HARVESTER (pypdf)")
        logger.info(f"Purpose: Read blank PA form and find the questions (field names)")
        logger.info(f"Input: {pdf_path}")
        logger.info(f"Output: {schema_output}")
        
        cmd = [
            "python3", 
            str(self.src_dir / "clean_schema_harvester.py"),
            "--pdf", str(pdf_path),
            "--output", str(schema_output)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"✅ Schema harvester completed successfully")
            
            # Parse results
            if schema_output.exists():
                with open(schema_output, 'r') as f:
                    content = f.read()
                    field_count = content.count('acro_id:')
                    self.results['schema_fields'] = field_count
                    logger.info(f"📊 Discovered {field_count} form fields")
            
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Schema harvester failed: {e.stderr}")
            return False
    
    def step2_extract_data(self, schema_path: Path, referral_path: Path, data_output: Path, use_gemini: bool = False) -> bool:
        """Step 2: Data Extractor (Gemini Flash) - Find the answers."""
        
        logger.info(f"🤖 STEP 2: DATA EXTRACTOR (Gemini Flash)")
        logger.info(f"Purpose: Read messy referral package and find the answers (patient data)")
        logger.info(f"Schema: {schema_path}")
        logger.info(f"Referral: {referral_path}")
        logger.info(f"Output: {data_output}")
        
        if use_gemini:
            # Use real Gemini Flash
            cmd = [
                "python3",
                str(self.src_dir / "gemini_extractor.py"),
                "--schema", str(schema_path),
                "--referral", str(referral_path),
                "--output", str(data_output)
            ]
        else:
            # Use demo extractor for testing
            cmd = [
                "python3",
                str(self.src_dir / "dynamic_extractor.py"),
                "--schema", str(schema_path),
                "--referral", str(referral_path),
                "--output", str(data_output)
            ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"✅ Data extractor completed successfully")
            
            # Parse results
            if data_output.exists():
                with open(data_output, 'r') as f:
                    extracted_data = json.load(f)
                    self.results['extracted_fields'] = len(extracted_data)
                    logger.info(f"📊 Extracted {len(extracted_data)} data fields")
                    
                    # Show sample extractions
                    logger.info(f"📋 Sample extracted data:")
                    for key, value in list(extracted_data.items())[:5]:
                        logger.info(f"   {key}: {value}")
            
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Data extractor failed: {e.stderr}")
            return False
    
    def step3_fill_form(self, pdf_path: Path, schema_path: Path, data_path: Path, output_path: Path) -> bool:
        """Step 3: Form Filler (pypdf/PyMuPDF) - Write answers into questions."""
        
        logger.info(f"📝 STEP 3: FORM FILLER (PyMuPDF)")
        logger.info(f"Purpose: Take the answers and write them into the questions on the blank form")
        logger.info(f"PDF: {pdf_path}")
        logger.info(f"Schema: {schema_path}")
        logger.info(f"Data: {data_path}")
        logger.info(f"Output: {output_path}")
        
        cmd = [
            "python3",
            str(self.src_dir / "debug_and_fix_filler.py"),
            "--pdf", str(pdf_path),
            "--schema", str(schema_path),
            "--data", str(data_path),
            "--output", str(output_path)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"✅ Form filler completed successfully")
            
            # Parse results from output
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if 'Fill rate:' in line and '%' in line:
                    try:
                        # Extract fill rate and count
                        # Format: "📊 Fill rate: 90.0% (9/10)"
                        if '(' in line and ')' in line:
                            paren_content = line.split('(')[1].split(')')[0]
                            if '/' in paren_content:
                                filled, total = paren_content.split('/')
                                self.results['filled_fields'] = int(filled)
                                self.results['total_matches'] = int(total)
                                break
                    except:
                        pass
            
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Form filler failed: {e.stderr}")
            return False
    
    def run_complete_pipeline(self, pa_pdf: Path, referral_pdf: Path, output_dir: Path, use_gemini: bool = False) -> Dict[str, Any]:
        """Run the complete 3-step pipeline."""
        
        logger.info(f"🚀 COMPLETE PA AUTOMATION PIPELINE")
        logger.info(f"=" * 60)
        logger.info(f"PA Form: {pa_pdf}")
        logger.info(f"Referral: {referral_pdf}")
        logger.info(f"Output Dir: {output_dir}")
        logger.info(f"Use Gemini: {use_gemini}")
        logger.info(f"=" * 60)
        
        # Create output paths
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        schema_path = output_dir / f"schema_{timestamp}.yaml"
        extracted_data_path = output_dir / f"extracted_{timestamp}.json"
        filled_pdf_path = output_dir / f"filled_form_{timestamp}.pdf"
        
        pipeline_result = {
            "timestamp": datetime.now().isoformat(),
            "pa_pdf": str(pa_pdf),
            "referral_pdf": str(referral_pdf),
            "use_gemini": use_gemini,
            "steps_completed": [],
            "status": "failed",
            "outputs": {}
        }
        
        try:
            # Step 1: Schema Harvester (pypdf)
            logger.info(f"\n" + "="*60)
            if self.step1_harvest_schema(pa_pdf, schema_path):
                pipeline_result["steps_completed"].append("schema_harvest")
                pipeline_result["outputs"]["schema"] = str(schema_path)
            else:
                raise Exception("Schema harvesting failed")
            
            # Step 2: Data Extractor (Gemini Flash)
            logger.info(f"\n" + "="*60)
            if self.step2_extract_data(schema_path, referral_pdf, extracted_data_path, use_gemini):
                pipeline_result["steps_completed"].append("data_extraction")
                pipeline_result["outputs"]["extracted_data"] = str(extracted_data_path)
            else:
                raise Exception("Data extraction failed")
            
            # Step 3: Form Filler (pypdf/PyMuPDF)
            logger.info(f"\n" + "="*60)
            if self.step3_fill_form(pa_pdf, schema_path, extracted_data_path, filled_pdf_path):
                pipeline_result["steps_completed"].append("form_filling")
                pipeline_result["outputs"]["filled_pdf"] = str(filled_pdf_path)
            else:
                raise Exception("Form filling failed")
            
            # Success!
            pipeline_result["status"] = "success"
            pipeline_result["results"] = self.results
            
            logger.info(f"\n" + "="*60)
            logger.info(f"🎉 PIPELINE COMPLETED SUCCESSFULLY!")
            logger.info(f"📊 Results:")
            logger.info(f"   Schema fields: {self.results.get('schema_fields', 'N/A')}")
            logger.info(f"   Extracted fields: {self.results.get('extracted_fields', 'N/A')}")
            logger.info(f"   Filled fields: {self.results.get('filled_fields', 'N/A')}")
            logger.info(f"📁 Outputs:")
            logger.info(f"   Schema: {schema_path}")
            logger.info(f"   Extracted Data: {extracted_data_path}")
            logger.info(f"   Filled PDF: {filled_pdf_path}")
            
        except Exception as e:
            pipeline_result["error"] = str(e)
            logger.error(f"❌ Pipeline failed: {e}")
        
        return pipeline_result


def main():
    """Main CLI interface for complete pipeline."""
    
    parser = argparse.ArgumentParser(
        description="Complete PA Automation Pipeline with proper tool separation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Component Separation:
1. Schema Harvester (pypdf) → Finds the questions (field names)
2. Data Extractor (Gemini Flash) → Finds the answers (patient data)
3. Form Filler (pypdf/PyMuPDF) → Writes answers into questions

Examples:
    # Demo pipeline (no API key needed)
    python src/complete_pipeline.py --pa-pdf "Input Data/Abdullah/abdullah_pa.pdf" --referral "Input Data/Abdullah/referral.pdf" --output "Output"
    
    # Real Gemini Flash pipeline (requires GOOGLE_API_KEY)
    export GOOGLE_API_KEY="your-key-here"
    python src/complete_pipeline.py --pa-pdf "Input Data/Abdullah/abdullah_pa.pdf" --referral "Input Data/Abdullah/referral.pdf" --output "Output" --use-gemini
        """
    )
    
    parser.add_argument(
        "--pa-pdf",
        type=Path,
        required=True,
        help="Path to the blank PA form PDF"
    )
    
    parser.add_argument(
        "--referral",
        type=Path,
        required=True,
        help="Path to the referral document PDF"
    )
    
    parser.add_argument(
        "--output",
        type=Path,
        required=True,
        help="Output directory for results"
    )
    
    parser.add_argument(
        "--use-gemini",
        action="store_true",
        help="Use real Gemini Flash API (requires GOOGLE_API_KEY)"
    )
    
    parser.add_argument(
        "--src",
        type=Path,
        default="src",
        help="Source directory containing pipeline scripts"
    )
    
    args = parser.parse_args()
    
    print(f"🚀 COMPLETE PA AUTOMATION PIPELINE")
    print(f"=" * 60)
    print(f"Component Separation:")
    print(f"1. Schema Harvester (pypdf) → Finds the questions")
    print(f"2. Data Extractor (Gemini Flash) → Finds the answers")
    print(f"3. Form Filler (pypdf/PyMuPDF) → Writes answers into questions")
    print(f"=" * 60)
    print(f"PA PDF: {args.pa_pdf}")
    print(f"Referral: {args.referral}")
    print(f"Output: {args.output}")
    print(f"Use Gemini: {args.use_gemini}")
    print(f"=" * 60)
    
    try:
        # Validate input files
        for file_path in [args.pa_pdf, args.referral]:
            if not file_path.exists():
                print(f"❌ Error: File not found: {file_path}")
                return 1
        
        # Initialize pipeline
        pipeline = CompletePAPipeline(str(args.src))
        
        # Run complete pipeline
        result = pipeline.run_complete_pipeline(
            args.pa_pdf, 
            args.referral, 
            args.output, 
            args.use_gemini
        )
        
        # Print summary
        print(f"\n🎯 PIPELINE SUMMARY:")
        print(f"Status: {'✅ SUCCESS' if result['status'] == 'success' else '❌ FAILED'}")
        print(f"Steps completed: {len(result['steps_completed'])}/3")
        
        if result['status'] == 'success':
            print(f"📊 Performance:")
            results = result.get('results', {})
            schema_fields = results.get('schema_fields', 0)
            extracted_fields = results.get('extracted_fields', 0)
            filled_fields = results.get('filled_fields', 0)
            
            print(f"   Schema fields discovered: {schema_fields}")
            print(f"   Data fields extracted: {extracted_fields}")
            print(f"   Form fields filled: {filled_fields}")
            
            if schema_fields > 0:
                extraction_rate = (extracted_fields / schema_fields) * 100
                fill_rate = (filled_fields / extracted_fields) * 100 if extracted_fields > 0 else 0
                print(f"   Extraction rate: {extraction_rate:.1f}%")
                print(f"   Fill rate: {fill_rate:.1f}%")
            
            print(f"\n📁 Output files:")
            for output_type, output_path in result['outputs'].items():
                print(f"   {output_type}: {output_path}")
            
            print(f"\n🎉 SUCCESS: Complete pipeline working with proper tool separation!")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
        
        return 0 if result['status'] == 'success' else 1
        
    except Exception as e:
        print(f"\n❌ Pipeline failed: {e}")
        logger.error(f"Pipeline failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
