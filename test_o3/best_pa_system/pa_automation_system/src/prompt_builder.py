"""
PROMPT BUILDER
==============

Creates dynamic, structured prompts for <PERSON> based on a form's schema.
This is the core of the Prompt Engineering approach - building perfect,
custom-tailored instructions for each specific PA form.

This replaces generic prompts with precise, schema-driven instructions
that tell <PERSON> exactly what to extract and in what format.
"""

import json
import logging
from typing import Dict, Any, List

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def identify_extraction_targets(schema: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
    """
    Analyzes the schema to identify which fields need to be extracted from referrals.
    Returns a mapping of extraction targets with their descriptions.
    """
    
    logger.info(f"🎯 Identifying extraction targets from schema...")
    
    # Define patterns for important PA form fields that need extraction
    extraction_patterns = {
        # Patient Information
        "patient_first_name": {
            "patterns": ["first", "name", "patient", "fname"],
            "description": "<PERSON><PERSON>'s first name only",
            "example": "<PERSON>"
        },
        "patient_last_name": {
            "patterns": ["last", "name", "patient", "lname", "surname"],
            "description": "<PERSON><PERSON>'s last name only", 
            "example": "<PERSON>"
        },
        "patient_dob": {
            "patterns": ["birth", "dob", "date", "born"],
            "description": "Patient's date of birth in YYYY-MM-DD format",
            "example": "1985-03-15"
        },
        "patient_address": {
            "patterns": ["address", "street", "home"],
            "description": "Patient's complete mailing address",
            "example": "123 Main St, City, State 12345"
        },
        "patient_phone": {
            "patterns": ["phone", "cell", "telephone", "mobile"],
            "description": "Patient's phone number with area code",
            "example": "************"
        },
        
        # Insurance Information
        "member_id": {
            "patterns": ["member", "id", "insurance", "policy"],
            "description": "Insurance member ID number",
            "example": "ABC123456789"
        },
        "group_number": {
            "patterns": ["group", "number", "grp"],
            "description": "Insurance group number",
            "example": "GRP001"
        },
        
        # Prescriber Information
        "prescriber_name": {
            "patterns": ["prescriber", "physician", "doctor", "dr", "provider"],
            "description": "Full name of prescribing physician",
            "example": "Dr. Jane Smith"
        },
        "prescriber_npi": {
            "patterns": ["npi", "provider", "national"],
            "description": "10-digit National Provider Identifier",
            "example": "**********"
        },
        "prescriber_dea": {
            "patterns": ["dea", "drug", "enforcement"],
            "description": "DEA number for controlled substances",
            "example": "AB1234567"
        },
        
        # Medication Information
        "drug_name": {
            "patterns": ["drug", "medication", "medicine", "humira", "adalimumab"],
            "description": "Name of requested medication",
            "example": "Humira (adalimumab)"
        },
        "dosage": {
            "patterns": ["dose", "dosage", "strength", "mg"],
            "description": "Medication dosage and strength",
            "example": "40 mg"
        },
        "frequency": {
            "patterns": ["frequency", "times", "daily", "weekly"],
            "description": "How often medication is taken",
            "example": "Once weekly"
        },
        
        # Clinical Information
        "diagnosis": {
            "patterns": ["diagnosis", "condition", "icd", "disease"],
            "description": "Primary medical diagnosis",
            "example": "Rheumatoid Arthritis"
        },
        "date_of_service": {
            "patterns": ["service", "date", "treatment", "visit"],
            "description": "Date of medical service in YYYY-MM-DD format",
            "example": "2024-01-15"
        }
    }
    
    # Find matching fields in schema
    extraction_targets = {}
    
    for target_name, target_info in extraction_patterns.items():
        patterns = target_info["patterns"]
        description = target_info["description"]
        example = target_info["example"]
        
        # Find best matching field in schema
        best_match = None
        best_score = 0
        
        for slug, field_info in schema.items():
            if not isinstance(field_info, dict):
                continue
                
            human_name = str(field_info.get('human_name', '')).lower()
            acro_id = str(field_info.get('acro_id', '')).lower()
            slug_str = str(slug).lower()
            
            # Calculate match score
            score = 0
            for pattern in patterns:
                if pattern in human_name:
                    score += 3
                elif pattern in slug_str:
                    score += 2
                elif pattern in acro_id:
                    score += 1
            
            if score > best_score:
                best_score = score
                best_match = {
                    "slug": str(slug),
                    "acro_id": str(field_info.get('acro_id', '')),
                    "human_name": str(field_info.get('human_name', '')),
                    "type": str(field_info.get('type', 'text'))
                }
        
        if best_match and best_score > 0:
            extraction_targets[target_name] = {
                "slug": best_match["slug"],
                "acro_id": best_match["acro_id"],
                "human_name": best_match["human_name"],
                "description": description,
                "example": example,
                "type": best_match["type"]
            }
            logger.info(f"   ✅ {target_name} → {best_match['slug']} ({best_match['acro_id']})")
    
    logger.info(f"✅ Identified {len(extraction_targets)} extraction targets")
    return extraction_targets


def create_dynamic_extraction_prompt(schema: Dict[str, Any]) -> str:
    """
    Creates a dynamic, structured prompt for Gemini based on a form's schema.
    
    This is the core of Prompt Engineering - building perfect, custom-tailored
    instructions for each specific PA form.
    
    Args:
        schema: The schema dictionary loaded from the form's YAML file.
        
    Returns:
        A detailed prompt string to send to Gemini.
    """
    
    logger.info(f"📝 Building dynamic extraction prompt from schema...")
    
    # Step 1: Identify what needs to be extracted
    extraction_targets = identify_extraction_targets(schema)
    
    if not extraction_targets:
        raise ValueError("No extraction targets identified from schema")
    
    # Step 2: Create JSON stub showing exact output format
    json_stub = {}
    field_descriptions = []
    
    for target_name, target_info in extraction_targets.items():
        json_stub[target_name] = None  # Gemini will fill these nulls
        
        # Create detailed field description
        description = f'  "{target_name}": {target_info["description"]}'
        if target_info["example"]:
            description += f' (example: "{target_info["example"]}")'
        field_descriptions.append(description)
    
    # Step 3: Format JSON stub nicely
    json_format_string = json.dumps(json_stub, indent=2)
    
    # Step 4: Build comprehensive prompt
    prompt = f"""You are an expert medical data extraction specialist. Your task is to analyze a referral package and extract specific pieces of information needed to fill out a Prior Authorization form.

**CRITICAL INSTRUCTIONS:**
1. Read the entire referral document carefully
2. Extract ONLY the specific information requested below
3. If a piece of information cannot be found, use `null` for that field
4. Do not invent, guess, or infer any information not explicitly present
5. Return ONLY a valid JSON object with no additional text or formatting

**EXTRACTION TARGETS:**
{chr(10).join(field_descriptions)}

**REQUIRED OUTPUT FORMAT:**
```json
{json_format_string}
```

**EXTRACTION RULES:**
- For dates: Use YYYY-MM-DD format only
- For names: Extract exactly as written, no modifications
- For phone numbers: Include area code if available
- For addresses: Include complete address with city, state, ZIP
- For medication names: Include both brand and generic names if present
- For dosages: Include units (mg, ml, etc.)
- For diagnoses: Use the primary/main diagnosis mentioned

**QUALITY REQUIREMENTS:**
- Accuracy is more important than completeness
- If unsure about a value, use `null` rather than guessing
- Ensure all field names match exactly as shown above
- Verify JSON is valid before returning

Analyze the referral document and extract the requested information:"""
    
    logger.info(f"✅ Dynamic prompt created with {len(extraction_targets)} extraction targets")
    return prompt


def create_field_mapping_prompt(extraction_targets: Dict[str, Dict[str, str]]) -> str:
    """
    Creates a secondary prompt for mapping extracted data to form fields.
    This helps with post-processing and validation.
    """
    
    mapping_info = []
    for target_name, target_info in extraction_targets.items():
        mapping_info.append(f"  {target_name} → {target_info['slug']} (PDF field: {target_info['acro_id']})")
    
    mapping_prompt = f"""
FIELD MAPPING REFERENCE:
The extracted data will be mapped to PDF form fields as follows:

{chr(10).join(mapping_info)}

This mapping ensures extracted data is placed in the correct form fields.
"""
    
    return mapping_prompt


def validate_extraction_prompt(prompt: str) -> bool:
    """
    Validates that the extraction prompt meets quality standards.
    """
    
    required_elements = [
        "JSON",
        "null",
        "extract",
        "referral",
        "YYYY-MM-DD",
        "valid JSON object"
    ]
    
    for element in required_elements:
        if element not in prompt:
            logger.warning(f"❌ Prompt missing required element: {element}")
            return False
    
    if len(prompt) < 500:
        logger.warning(f"❌ Prompt too short: {len(prompt)} characters")
        return False
    
    logger.info(f"✅ Prompt validation passed: {len(prompt)} characters")
    return True


def main():
    """Test the prompt builder with a sample schema."""
    
    # Sample schema for testing
    sample_schema = {
        "first_name": {
            "acro_id": "T42",
            "human_name": "First Name:",
            "type": "text"
        },
        "address": {
            "acro_id": "T15", 
            "human_name": "Address:",
            "type": "text"
        },
        "humira_adalimumab": {
            "acro_id": "CB99a",
            "human_name": "Humira (adalimumab)",
            "type": "checkbox"
        }
    }
    
    print("🧪 Testing Prompt Builder...")
    
    try:
        prompt = create_dynamic_extraction_prompt(sample_schema)
        
        if validate_extraction_prompt(prompt):
            print("✅ Prompt builder test passed!")
            print(f"📏 Prompt length: {len(prompt)} characters")
            print("\n📝 Sample prompt preview:")
            print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
        else:
            print("❌ Prompt validation failed!")
            
    except Exception as e:
        print(f"❌ Prompt builder test failed: {e}")


if __name__ == "__main__":
    main()
