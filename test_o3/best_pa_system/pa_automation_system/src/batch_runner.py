"""
BATCH RUNNER
============

Complete PA automation pipeline that orchestrates:
1. Schema Harvester: Generate schema for any PDF form
2. Dynamic Extractor: Extract data based on schema
3. Form Filler: Fill PDF using schema mappings

This is the main script that meets the assignment requirements:
"The pipeline should be designed to generalize to any form and any drug, 
even those unseen during development."
"""

import argparse
import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PAAutomationPipeline:
    """Complete PA automation pipeline."""
    
    def __init__(self, src_dir: str = "src"):
        self.src_dir = Path(src_dir)
        self.results = []
    
    def run_schema_harvester(self, pdf_path: Path, schema_output: Path) -> bool:
        """Run schema harvester on a PDF form."""
        
        logger.info(f"🔍 Running schema harvester...")
        
        cmd = [
            "python3", 
            str(self.src_dir / "clean_schema_harvester.py"),
            "--pdf", str(pdf_path),
            "--output", str(schema_output)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"✅ Schema harvester completed")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Schema harvester failed: {e.stderr}")
            return False
    
    def run_dynamic_extractor(self, schema_path: Path, referral_path: Path, data_output: Path) -> bool:
        """Run dynamic extractor using schema."""
        
        logger.info(f"🤖 Running dynamic extractor...")
        
        cmd = [
            "python3",
            str(self.src_dir / "dynamic_extractor.py"),
            "--schema", str(schema_path),
            "--referral", str(referral_path),
            "--output", str(data_output)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"✅ Dynamic extractor completed")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Dynamic extractor failed: {e.stderr}")
            return False
    
    def run_form_filler(self, pdf_path: Path, schema_path: Path, data_path: Path, output_path: Path) -> bool:
        """Run form filler to create filled PDF."""
        
        logger.info(f"📝 Running form filler...")
        
        cmd = [
            "python3",
            str(self.src_dir / "form_filler.py"),
            "--pdf", str(pdf_path),
            "--schema", str(schema_path),
            "--data", str(data_path),
            "--output", str(output_path)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"✅ Form filler completed")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Form filler failed: {e.stderr}")
            return False
    
    def process_patient_folder(self, patient_folder: Path, output_dir: Path) -> Dict[str, Any]:
        """Process a complete patient folder through the pipeline."""
        
        patient_name = patient_folder.name
        logger.info(f"🚀 Processing patient: {patient_name}")
        
        result = {
            "patient_name": patient_name,
            "patient_folder": str(patient_folder),
            "timestamp": datetime.now().isoformat(),
            "status": "failed",
            "steps_completed": [],
            "error": None
        }
        
        try:
            # Find PA PDF and referral
            pa_pdfs = list(patient_folder.glob("*pa*.pdf")) or list(patient_folder.glob("*.pdf"))
            referral_pdfs = list(patient_folder.glob("*referral*.pdf"))
            
            if not pa_pdfs:
                raise FileNotFoundError(f"No PA PDF found in {patient_folder}")
            
            if not referral_pdfs:
                # Use any PDF as referral for now
                referral_pdfs = pa_pdfs
            
            pa_pdf = pa_pdfs[0]
            referral_pdf = referral_pdfs[0]
            
            logger.info(f"📄 PA PDF: {pa_pdf.name}")
            logger.info(f"📄 Referral PDF: {referral_pdf.name}")
            
            # Create output paths
            patient_output_dir = output_dir / patient_name
            patient_output_dir.mkdir(parents=True, exist_ok=True)
            
            schema_path = patient_output_dir / f"{patient_name}_schema.yaml"
            extracted_data_path = patient_output_dir / f"{patient_name}_extracted.json"
            filled_pdf_path = patient_output_dir / f"{patient_name}_filled.pdf"
            
            # Step 1: Harvest Schema
            logger.info(f"📋 Step 1: Harvesting schema...")
            if self.run_schema_harvester(pa_pdf, schema_path):
                result["steps_completed"].append("schema_harvest")
                result["schema_path"] = str(schema_path)
            else:
                raise Exception("Schema harvesting failed")
            
            # Step 2: Extract Data
            logger.info(f"🤖 Step 2: Extracting data...")
            if self.run_dynamic_extractor(schema_path, referral_pdf, extracted_data_path):
                result["steps_completed"].append("data_extraction")
                result["extracted_data_path"] = str(extracted_data_path)
            else:
                raise Exception("Data extraction failed")
            
            # Step 3: Fill Form
            logger.info(f"📝 Step 3: Filling form...")
            if self.run_form_filler(pa_pdf, schema_path, extracted_data_path, filled_pdf_path):
                result["steps_completed"].append("form_filling")
                result["filled_pdf_path"] = str(filled_pdf_path)
            else:
                raise Exception("Form filling failed")
            
            # Success!
            result["status"] = "success"
            logger.info(f"✅ Patient {patient_name} processed successfully!")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ Patient {patient_name} failed: {e}")
        
        return result
    
    def process_batch(self, input_dir: Path, output_dir: Path) -> Dict[str, Any]:
        """Process multiple patient folders."""
        
        logger.info(f"🏭 Starting batch processing...")
        logger.info(f"Input directory: {input_dir}")
        logger.info(f"Output directory: {output_dir}")
        
        # Find patient folders
        patient_folders = [p for p in input_dir.iterdir() if p.is_dir()]
        
        if not patient_folders:
            raise ValueError(f"No patient folders found in {input_dir}")
        
        logger.info(f"📁 Found {len(patient_folders)} patient folders")
        
        # Process each patient
        batch_results = []
        successful = 0
        failed = 0
        
        for patient_folder in patient_folders:
            result = self.process_patient_folder(patient_folder, output_dir)
            batch_results.append(result)
            
            if result["status"] == "success":
                successful += 1
            else:
                failed += 1
        
        # Create batch summary
        batch_summary = {
            "batch_timestamp": datetime.now().isoformat(),
            "input_directory": str(input_dir),
            "output_directory": str(output_dir),
            "total_patients": len(patient_folders),
            "successful": successful,
            "failed": failed,
            "success_rate": successful / len(patient_folders) * 100 if patient_folders else 0,
            "patient_results": batch_results
        }
        
        # Save batch results
        batch_results_path = output_dir / f"batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(batch_results_path, 'w') as f:
            json.dump(batch_summary, f, indent=2)
        
        logger.info(f"🎯 Batch processing complete!")
        logger.info(f"📊 Success rate: {batch_summary['success_rate']:.1f}% ({successful}/{len(patient_folders)})")
        logger.info(f"📄 Batch results saved: {batch_results_path}")
        
        return batch_summary


def main():
    """Main CLI interface for batch processing."""
    
    parser = argparse.ArgumentParser(
        description="PA Automation Pipeline - Complete batch processing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Process all patients in Input Data directory
    python src/batch_runner.py --input "Input Data" --output "Output"
    
    # Process single patient
    python src/batch_runner.py --input "Input Data/Abdullah" --output "Output"
        """
    )
    
    parser.add_argument(
        "--input",
        type=Path,
        required=True,
        help="Input directory containing patient folders"
    )
    
    parser.add_argument(
        "--output",
        type=Path,
        required=True,
        help="Output directory for results"
    )
    
    parser.add_argument(
        "--src",
        type=Path,
        default="src",
        help="Source directory containing pipeline scripts"
    )
    
    args = parser.parse_args()
    
    print(f"🏭 PA AUTOMATION PIPELINE")
    print(f"=" * 60)
    print(f"Input: {args.input}")
    print(f"Output: {args.output}")
    print(f"Source: {args.src}")
    print(f"=" * 60)
    
    try:
        # Validate input directory
        if not args.input.exists():
            print(f"❌ Error: Input directory not found: {args.input}")
            return 1
        
        # Create output directory
        args.output.mkdir(parents=True, exist_ok=True)
        
        # Initialize pipeline
        pipeline = PAAutomationPipeline(str(args.src))
        
        # Process batch
        if args.input.is_dir() and any(args.input.iterdir()):
            # Check if it's a single patient folder or multiple
            subdirs = [p for p in args.input.iterdir() if p.is_dir()]
            
            if subdirs:
                # Multiple patient folders
                results = pipeline.process_batch(args.input, args.output)
            else:
                # Single patient folder
                results = pipeline.process_patient_folder(args.input, args.output)
                results = {
                    "total_patients": 1,
                    "successful": 1 if results["status"] == "success" else 0,
                    "failed": 0 if results["status"] == "success" else 1,
                    "success_rate": 100 if results["status"] == "success" else 0,
                    "patient_results": [results]
                }
        else:
            print(f"❌ Error: No patient data found in {args.input}")
            return 1
        
        # Print summary
        print(f"\n🎯 PIPELINE RESULTS:")
        print(f"📊 Success rate: {results['success_rate']:.1f}%")
        print(f"✅ Successful: {results['successful']}")
        print(f"❌ Failed: {results['failed']}")
        
        if results['success_rate'] >= 90:
            print(f"\n🎉 EXCELLENT! System is production-ready.")
        elif results['success_rate'] >= 70:
            print(f"\n✅ GOOD RESULTS! Minor improvements possible.")
        else:
            print(f"\n🔧 NEEDS IMPROVEMENT: Review failed cases.")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Pipeline failed: {e}")
        logger.error(f"Pipeline failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
