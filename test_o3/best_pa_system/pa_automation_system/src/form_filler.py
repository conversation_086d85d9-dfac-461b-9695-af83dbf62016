"""
FORM FILLER
===========

Schema-driven form filler that takes:
1. Blank PA.pdf
2. Schema.yaml (field mappings)
3. Extracted_data.json (data to fill)

And produces a filled PDF using the acro_id mappings from the schema.
This is the final piece that makes the system truly scalable.
"""

import argparse
import json
import logging
from pathlib import Path
from typing import Dict, Any
import yaml
from datetime import datetime

from PyPDF2 import PdfReader, PdfWriter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_schema(schema_path: Path) -> Dict[str, Any]:
    """Load schema file."""
    
    logger.info(f"📋 Loading schema from: {schema_path}")
    
    with open(schema_path, 'r') as f:
        schema = yaml.safe_load(f)
    
    if not schema:
        raise ValueError(f"Empty schema file: {schema_path}")
    
    logger.info(f"✅ Schema loaded: {len(schema)} fields")
    return schema


def load_extracted_data(data_path: Path) -> Dict[str, Any]:
    """Load extracted data JSON."""
    
    logger.info(f"📊 Loading extracted data from: {data_path}")
    
    with open(data_path, 'r') as f:
        data = json.load(f)
    
    if not data:
        raise ValueError(f"Empty data file: {data_path}")
    
    logger.info(f"✅ Extracted data loaded: {len(data)} fields")
    for key, value in data.items():
        logger.info(f"   {key}: {value}")
    
    return data


def map_data_to_acroform(schema: Dict[str, Any], extracted_data: Dict[str, Any]) -> Dict[str, str]:
    """Map extracted data to PDF AcroForm field IDs using schema."""
    
    logger.info(f"🔗 Mapping data to AcroForm fields...")
    
    form_values = {}
    mapped_count = 0
    
    for slug, field_info in schema.items():
        if not isinstance(field_info, dict):
            continue
            
        # Get the PDF field ID from schema
        acro_id = str(field_info.get('acro_id', ''))
        field_type = str(field_info.get('type', 'text'))
        
        # Check if we have data for this field
        if str(slug) in extracted_data:
            value = extracted_data[str(slug)]
            
            if acro_id and value:
                form_values[acro_id] = {
                    "value": str(value),
                    "type": field_type,
                    "slug": str(slug)
                }
                mapped_count += 1
                logger.info(f"   ✅ {slug} → {acro_id}: '{value}' ({field_type})")
    
    logger.info(f"✅ Mapped {mapped_count} fields to AcroForm IDs")
    return form_values


def fill_pdf_form(pdf_path: Path, form_values: Dict[str, Dict], output_path: Path) -> bool:
    """Fill PDF form with mapped values."""
    
    logger.info(f"📝 Filling PDF form: {pdf_path}")
    
    try:
        # Load PDF
        reader = PdfReader(pdf_path)
        writer = PdfWriter()
        writer.clone_document_from_reader(reader)
        
        # Check if PDF has form fields
        pdf_fields = reader.get_fields()
        if not pdf_fields:
            raise ValueError(f"PDF has no form fields: {pdf_path}")
        
        logger.info(f"📋 PDF has {len(pdf_fields)} form fields")
        
        # Prepare values for filling
        fill_data = {}
        successful_fills = 0
        failed_fills = []
        
        for acro_id, field_data in form_values.items():
            value = field_data["value"]
            field_type = field_data["type"]
            slug = field_data["slug"]
            
            try:
                if acro_id in pdf_fields:
                    # Check PDF field type for compatibility
                    pdf_field_obj = pdf_fields[acro_id].get_object()
                    pdf_field_type = pdf_field_obj.get('/FT', '') if pdf_field_obj else ''
                    
                    if pdf_field_type == '/Tx':  # Text field
                        fill_data[acro_id] = value
                        successful_fills += 1
                        logger.info(f"   ✅ Text field {acro_id}: '{value}'")
                        
                    elif pdf_field_type == '/Btn':  # Button/checkbox field
                        if field_type == "checkbox":
                            # For checkboxes, use standard values
                            fill_data[acro_id] = '/Yes'
                            successful_fills += 1
                            logger.info(f"   ✅ Checkbox {acro_id}: checked (for '{value}')")
                        else:
                            # Try as text anyway
                            fill_data[acro_id] = value
                            successful_fills += 1
                            logger.info(f"   ✅ Button {acro_id}: '{value}'")
                    
                    else:
                        # Unknown type, try as text
                        fill_data[acro_id] = value
                        successful_fills += 1
                        logger.info(f"   ✅ Unknown field {acro_id}: '{value}' (PDF type: {pdf_field_type})")
                
                else:
                    failed_fills.append(f"{acro_id} ({slug}): Field not found in PDF")
                    logger.warning(f"   ❌ {acro_id} ({slug}): Field not found in PDF")
                    
            except Exception as e:
                failed_fills.append(f"{acro_id} ({slug}): {str(e)}")
                logger.error(f"   ❌ {acro_id} ({slug}): {e}")
        
        # Fill the form
        if fill_data:
            logger.info(f"📝 Writing {len(fill_data)} fields to PDF...")
            
            # Fill all fields at once
            writer.update_page_form_field_values(writer.pages[0], fill_data)
            
            # CRITICAL: Set need appearances for visibility
            writer.set_need_appearances_writer()
            
            # Save filled PDF
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, "wb") as f:
                writer.write(f)
            
            logger.info(f"✅ Filled PDF saved: {output_path}")
            logger.info(f"📊 Fill results: {successful_fills}/{len(form_values)} successful")
            
            if failed_fills:
                logger.warning(f"❌ Failed fills:")
                for failure in failed_fills:
                    logger.warning(f"   {failure}")
            
            return True
        
        else:
            logger.error(f"❌ No fields to fill")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to fill PDF: {e}")
        return False


def main():
    """Main CLI interface for form filling."""
    
    parser = argparse.ArgumentParser(
        description="Fill PDF form using schema and extracted data.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python src/form_filler.py --pdf "Input Data/Abdullah/abdullah_pa.pdf" --schema "schemas/abdullah_clean.yaml" --data "Output/extracted_data.json" --output "Output/filled_form.pdf"
        """
    )
    
    parser.add_argument(
        "--pdf", 
        type=Path, 
        required=True, 
        help="Path to the blank PA form PDF."
    )
    
    parser.add_argument(
        "--schema", 
        type=Path, 
        required=True, 
        help="Path to the schema YAML file."
    )
    
    parser.add_argument(
        "--data", 
        type=Path, 
        required=True, 
        help="Path to the extracted data JSON file."
    )
    
    parser.add_argument(
        "--output", 
        type=Path, 
        required=True, 
        help="Path to save the filled PDF."
    )
    
    args = parser.parse_args()
    
    print(f"📝 FORM FILLER")
    print(f"=" * 50)
    print(f"PDF: {args.pdf}")
    print(f"Schema: {args.schema}")
    print(f"Data: {args.data}")
    print(f"Output: {args.output}")
    print(f"=" * 50)
    
    try:
        # Validate input files
        for file_path in [args.pdf, args.schema, args.data]:
            if not file_path.exists():
                print(f"❌ Error: File not found: {file_path}")
                return 1
        
        # Load schema and data
        schema = load_schema(args.schema)
        extracted_data = load_extracted_data(args.data)
        
        # Map data to AcroForm fields
        form_values = map_data_to_acroform(schema, extracted_data)
        
        if not form_values:
            print(f"❌ No fields mapped. Check schema and data compatibility.")
            return 1
        
        # Fill PDF form
        if fill_pdf_form(args.pdf, form_values, args.output):
            print(f"✅ Form filling complete!")
            print(f"📊 Filled {len(form_values)} fields")
            print(f"💾 Filled PDF saved to: {args.output}")
            return 0
        else:
            print(f"❌ Form filling failed.")
            return 1
        
    except Exception as e:
        print(f"❌ Form filling failed: {e}")
        logger.error(f"Form filling failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
