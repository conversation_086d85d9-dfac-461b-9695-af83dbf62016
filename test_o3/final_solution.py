import json
import re
from pathlib import Path
from typing import Dict, Any

import pdfplumber
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject


class FinalPAFormFiller:
    """
    Final solution that correctly maps labels to fields
    """
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.data = json.load(open(json_path))
        self.reader = PdfReader(str(pdf_path))
        
    def analyze_form_layout(self):
        """
        Analyze the form to understand label-field relationships
        """
        print("=== ANALYZING FORM STRUCTURE ===")
        
        with pdfplumber.open(str(self.pdf_path)) as pdf:
            page = pdf.pages[0]  # First page has patient info
            
            # Extract all text with positions
            words = page.extract_words()
            
            # Find key labels
            labels = {}
            for word in words:
                text = word['text'].lower()
                if 'first' in text and 'name' in text:
                    labels['first_name'] = word
                elif 'last' in text and 'name' in text:
                    labels['last_name'] = word
                elif 'address' in text and ':' in text:
                    labels['address'] = word
                elif 'city' in text and ':' in text:
                    labels['city'] = word
                elif 'state' in text and ':' in text:
                    labels['state'] = word
                elif 'member' in text and 'id' in text:
                    labels['member_id'] = word
                    
            # Print label positions
            for name, label in labels.items():
                print(f"{name}: {label['text']} at x={label['x0']:.1f}, y={label['top']:.1f}")
        
        # Get widget positions
        fields = self.reader.get_fields()
        widget_list = list(fields.keys())
        print(f"\nWidgets in order: {widget_list}")
        
        # Based on analysis, create the correct mapping
        # The key insight: widgets appear in visual order on the form
        return self._create_visual_order_mapping(widget_list)
    
    def _create_visual_order_mapping(self, widget_list):
        """
        Map widgets based on their visual order on the form
        """
        # Based on the form layout and widget order
        mapping = {}
        
        # Header section
        if 'Request by T' in widget_list:
            mapping['Request by T'] = 'facility_name'
        if 'Phone T' in widget_list:
            mapping['Phone T'] = 'facility_phone'
        if 'Fax T' in widget_list:
            mapping['Fax T'] = 'facility_fax'
            
        # Patient section - these appear in visual order
        # Row 1: First Name, Last Name, DOB
        if 'T14' in widget_list:
            mapping['T14'] = 'patient_first_name'
        if 'T15' in widget_list:
            mapping['T15'] = 'patient_last_name'
        if 'T16' in widget_list:
            mapping['T16'] = 'patient_dob'
            
        # Row 2: Address, City, State, ZIP
        # Note: T17 is skipped in the widget list, so T19 is address
        if 'T19' in widget_list:
            mapping['T19'] = 'patient_address'
        if 'T20' in widget_list:
            mapping['T20'] = 'patient_city'
        if 'T21' in widget_list:
            mapping['T21'] = 'patient_state'
        if 'T21B' in widget_list:
            mapping['T21B'] = 'patient_zip'
            
        # Row 3: Phone fields
        if 'T21C' in widget_list:
            mapping['T21C'] = 'patient_home_phone'
        if 'T21D' in widget_list:
            mapping['T21D'] = 'patient_work_phone'
        if 'T21E' in widget_list:
            mapping['T21E'] = 'patient_cell_phone'
        if 'T21F' in widget_list:
            mapping['T21F'] = 'patient_email'
            
        # Insurance section
        if 'T11' in widget_list:
            mapping['T11'] = 'member_id'
        if 'T12' in widget_list:
            mapping['T12'] = 'group_number'
        if 'T13' in widget_list:
            mapping['T13'] = 'insured_name'
            
        # T17 and T18 appear to be out of order
        if 'T17' in widget_list:
            mapping['T17'] = 'patient_weight_lbs'
        if 'T18' in widget_list:
            mapping['T18'] = 'patient_weight_kgs'
            
        return mapping
    
    def extract_values(self) -> Dict[str, Any]:
        """
        Extract and format values from JSON data
        """
        values = {}
        
        # Patient demographics
        demo = self.data['tier_1_mandatory_fields']['patient_demographics']
        
        # Name
        full_name = demo['full_name']['value']
        # Handle the specific name format "Akshay H. chaudhari"
        parts = full_name.split()
        if len(parts) >= 3 and parts[1].endswith('.'):
            # First Middle. Last format
            values['patient_first_name'] = parts[0]
            values['patient_last_name'] = parts[2].capitalize()
        else:
            values['patient_first_name'] = parts[0] if parts else ''
            values['patient_last_name'] = parts[-1].capitalize() if len(parts) > 1 else ''
        
        # DOB
        values['patient_dob'] = demo['date_of_birth']['value']
        
        # Address - parse correctly
        addr = demo['address']['value']  # "1460 El Camino Real, Arlington, VA-22407"
        addr_parts = addr.split(',')
        values['patient_address'] = addr_parts[0].strip() if addr_parts else ''
        
        if len(addr_parts) >= 2:
            # Parse "Arlington, VA-22407"
            city_state_zip = addr_parts[1].strip()
            values['patient_city'] = 'Arlington'
            
            # Extract state and zip
            if 'VA-' in addr:
                values['patient_state'] = 'VA'
                zip_part = addr.split('VA-')[1]
                values['patient_zip'] = zip_part.strip()
        
        # Phone
        phones = demo['phone_numbers']['value']
        if phones:
            values['patient_home_phone'] = phones[0]
            values['patient_cell_phone'] = phones[0]
            values['patient_work_phone'] = ''  # Not in data
        
        values['patient_email'] = ''  # Not in data
        
        # Weight
        weight = demo.get('physical_measurements', {}).get('weight', {}).get('value', '')
        if weight:
            # "190 lbs, 86.18 kg"
            if 'lbs' in weight:
                lbs_part = weight.split('lbs')[0].strip()
                values['patient_weight_lbs'] = lbs_part
            if 'kg' in weight:
                kg_part = weight.split(',')[1].strip().split('kg')[0].strip()
                values['patient_weight_kgs'] = kg_part
        
        # Insurance
        ins = self.data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
        values['member_id'] = ins['member_id']['value']
        values['group_number'] = ''  # Not in data
        values['insured_name'] = full_name
        
        # Provider
        prov = self.data['tier_1_mandatory_fields']['prescriber_information']
        values['facility_name'] = prov['facility_name']['value']
        values['facility_phone'] = prov['phone_fax']['value'].get('phone', '')
        values['facility_fax'] = prov['phone_fax']['value'].get('fax', '')
        
        return values
    
    def fill_form(self, output_path: Path):
        """
        Fill the form with correct mappings
        """
        # Analyze form and get mapping
        mapping = self.analyze_form_layout()
        
        # Get values
        values = self.extract_values()
        
        # Create form values dict
        form_values = {}
        for widget_name, canonical_key in mapping.items():
            if canonical_key in values and values[canonical_key]:
                form_values[widget_name] = str(values[canonical_key])
                print(f"Filling {widget_name} -> {canonical_key}: {values[canonical_key]}")
        
        # Write PDF
        writer = PdfWriter()
        writer.clone_reader_document_root(self.reader)
        writer.append_pages_from_reader(self.reader)
        
        # Ensure AcroForm is copied
        if self.reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): self.reader.trailer['/Root']['/AcroForm']
            })
        
        # Update fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        print(f"\nSuccessfully filled {len(form_values)} fields")
        print(f"Output saved to {output_path}")


if __name__ == "__main__":
    filler = FinalPAFormFiller(
        Path("test_o3/pa.pdf"),
        Path("test_o3/akshey_extracted.json")
    )
    filler.fill_form(Path("test_o3/pa_final_solution.pdf")) 