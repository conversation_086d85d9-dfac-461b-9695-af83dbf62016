import json
from test_o3.auto_fill import derive

# Load data
js = json.load(open('test_o3/akshey_extracted.json'))
pool = derive(js)

print("=== DERIVED DATA ===")
for k, v in sorted(pool.items()):
    if v:  # Only show non-empty values
        print(f"{k}: {v}")

print("\n=== EXPECTED FIELD ORDER ===")
print("Based on the form layout:")
print("Row 1: First Name | Last Name | DOB")
print("Row 2: Address | City | State | ZIP") 
print("Row 3: Home Phone | Work Phone | Cell Phone | Email")
print("Row 4: Weight lbs | Weight kgs | Height inches | Height cms | Allergies")

print("\n=== WIDGET IDS IN ORDER ===")
widgets = ['Request by T', 'Phone T', 'Fax T', 'T14', 'T15', 'T16', 'T17', 'T19', 'T20', 'T21', 'T21B', 'T21C', 'T21D', 'T21E', 'T21F', 'T11', 'T12', 'T18', 'T13']
for i, w in enumerate(widgets):
    print(f"{i}: {w}") 