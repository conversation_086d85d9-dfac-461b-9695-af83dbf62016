"""
Test the complete system on <PERSON>'s PA form
"""

import json
from pathlib import Path
from canonical_schema import convert_extraction_to_canonical
from precise_field_mapper import PreciseFieldMapper


def test_amy_system():
    """Test complete system on <PERSON>'s data"""
    
    print("🧪 TESTING SYSTEM ON AMY'S PA FORM")
    print("=" * 60)
    
    # Load <PERSON>'s data
    amy_json_path = Path("../Input Data/Extracted_ground_truth/amy_structured.json")
    amy_pa_path = Path("../Input Data/Amy/PA.pdf")
    
    print(f"📄 Loading <PERSON>'s data from: {amy_json_path}")
    with open(amy_json_path, 'r') as f:
        amy_raw_data = json.load(f)
    
    print(f"📋 Loading <PERSON>'s PA form from: {amy_pa_path}")
    
    # Convert to canonical schema
    print("\n🔄 Converting <PERSON>'s data to canonical schema...")
    amy_canonical = convert_extraction_to_canonical(amy_raw_data)
    
    print(f"✅ Canonical conversion complete:")
    print(f"  Patient: {amy_canonical.patient_demographics.first_name} {amy_canonical.patient_demographics.last_name}")
    print(f"  DOB: {amy_canonical.patient_demographics.date_of_birth}")
    print(f"  Address: {amy_canonical.patient_demographics.address}")
    print(f"  Insurance: {amy_canonical.insurance_information.member_id}")
    print(f"  Provider: {amy_canonical.prescriber_information.first_name} {amy_canonical.prescriber_information.last_name}")
    
    # Fill Amy's form using precise mapper
    print("\n🎯 Filling Amy's PA form with precise mapping...")
    mapper = PreciseFieldMapper()
    
    output_path = Path("amy_pa_filled.pdf")
    filled_count = mapper.fill_form_precisely(
        amy_pa_path,
        amy_canonical,
        output_path
    )
    
    print(f"\n🎉 AMY'S FORM PROCESSING COMPLETE!")
    print(f"📊 Fields filled: {filled_count}")
    print(f"📄 Output: {output_path}")
    
    # Save Amy's canonical data for reference
    canonical_path = Path("amy_canonical_data.json")
    with open(canonical_path, 'w') as f:
        json.dump(amy_canonical.model_dump(), f, indent=2, default=str)
    
    print(f"💾 Amy's canonical data saved: {canonical_path}")
    
    return filled_count, amy_canonical


if __name__ == "__main__":
    filled_count, canonical = test_amy_system()