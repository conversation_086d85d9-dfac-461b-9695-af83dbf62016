"""
Enhanced PA Form Filler with all improvements:
- Confidence scoring
- Semantic label matching
- Rules engine for conditional logic
- Handwriting fallback support
"""

import json
import json5
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import numpy as np
from datetime import datetime

import pdfplumber
from PyPDF2 import PdfReader, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
import fitz  # PyMuPDF


class ConfidenceTracker:
    """Track confidence scores for each field"""
    
    def __init__(self):
        self.scores = {}
        self.sources = {}
        
    def add_score(self, field: str, score: float, source: str):
        self.scores[field] = score
        self.sources[field] = source
        
    def get_low_confidence_fields(self, threshold: float = 0.75) -> List[str]:
        return [f for f, s in self.scores.items() if float(s) < threshold]


class SemanticMatcher:
    """Semantic label matching using embeddings simulation"""
    
    def __init__(self):
        # Simulate embeddings with keyword matching for now
        self.label_mappings = {
            'patient': ['member', 'enrollee', 'beneficiary', 'subscriber'],
            'first': ['given', 'fname', 'first name'],
            'last': ['surname', 'lname', 'last name', 'family'],
            'dob': ['birth', 'birthday', 'date of birth', 'birthdate'],
            'address': ['street', 'addr', 'residence', 'home address'],
            'phone': ['tel', 'telephone', 'contact', 'cell', 'mobile'],
            'insurance': ['payer', 'carrier', 'plan', 'coverage'],
            'member': ['id', 'identification', 'subscriber', 'policy'],
            'provider': ['physician', 'doctor', 'prescriber', 'practitioner'],
            'npi': ['national provider', 'provider id', 'tax id'],
            'diagnosis': ['dx', 'condition', 'icd', 'disease'],
            'medication': ['drug', 'rx', 'medicine', 'therapeutic']
        }
        
    def semantic_match(self, label: str, canonical_key: str) -> float:
        """Calculate semantic similarity between label and canonical key"""
        label_lower = label.lower().strip()
        key_parts = canonical_key.split('_')
        
        # Direct match
        if any(part in label_lower for part in key_parts):
            return 0.95
            
        # Semantic match
        for key, synonyms in self.label_mappings.items():
            if key in canonical_key.lower():
                if any(syn in label_lower for syn in synonyms):
                    return 0.85
                    
        # Partial match
        for part in key_parts:
            if part in label_lower:
                return 0.75
                
        return 0.0


class RulesEngine:
    """Apply conditional logic rules"""
    
    def __init__(self, rules_path: Path):
        with open(rules_path, 'r') as f:
            self.rules = json5.load(f)
            
    def apply_rules(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply rules and return validated data"""
        validated = data.copy()
        conflicts = []
        
        # Check for mutually exclusive fields
        for category, rules in self.rules.items():
            for field, conditions in rules.items():
                if field in validated:
                    # Check exclusions
                    if 'excludes' in conditions:
                        for excluded in conditions['excludes']:
                            if excluded in validated:
                                conflicts.append(f"{field} excludes {excluded}")
                                # Keep the one with higher confidence
                                if hasattr(self, 'confidence'):
                                    if self.confidence.scores.get(field, 0) >= self.confidence.scores.get(excluded, 0):
                                        validated.pop(excluded, None)
                                    else:
                                        validated.pop(field, None)
                                        
        return validated, conflicts


class EnhancedPAFiller:
    """Enhanced PA form filler with all improvements"""
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.data = json.load(open(json_path))
        self.reader = PdfReader(str(pdf_path))
        self.confidence = ConfidenceTracker()
        self.semantic_matcher = SemanticMatcher()
        self.rules_engine = RulesEngine(Path("test_o3/rules.json5"))
        
    def extract_form_structure(self) -> Dict[str, Any]:
        """Extract form structure with confidence scoring"""
        doc = fitz.open(str(self.pdf_path))
        structure = {}
        
        for page_num, page in enumerate(doc):
            text_blocks = page.get_text("dict")
            
            # Get form widgets
            for widget in page.widgets():
                x0, y0, x1, y1 = widget.rect
                
                # Find label with confidence
                best_label, label_confidence = self._find_label_with_confidence(
                    text_blocks, x0, y0, x1, y1
                )
                
                if best_label:
                    # Map to canonical key with semantic matching
                    canonical_key, match_confidence = self._map_label_semantic(best_label)
                    
                    # Combined confidence
                    total_confidence = (label_confidence + match_confidence) / 2
                    
                    structure[widget.field_name] = {
                        'label': best_label,
                        'canonical_key': canonical_key,
                        'widget': {
                            'name': widget.field_name,
                            'type': widget.field_type_string,
                            'rect': list(widget.rect),
                            'page': page_num
                        },
                        'confidence': total_confidence
                    }
                    
                    self.confidence.add_score(
                        widget.field_name, 
                        total_confidence,
                        f"page {page_num}, label: {best_label}"
                    )
        
        doc.close()
        return structure
        
    def _find_label_with_confidence(self, text_blocks, x0, y0, x1, y1) -> Tuple[str, float]:
        """Find label with confidence score"""
        candidates = []
        
        for block in text_blocks.get('blocks', []):
            if block.get('type') == 0:  # text block
                for line in block.get('lines', []):
                    for span in line.get('spans', []):
                        text = span.get('text', '').strip()
                        if not text:
                            continue
                            
                        bbox = span.get('bbox', [0, 0, 0, 0])
                        tx0, ty0, tx1, ty1 = bbox
                        
                        # Calculate proximity score
                        if tx1 < x0 and abs((ty0 + ty1) / 2 - (y0 + y1) / 2) < 10:
                            # Text to the left
                            distance = x0 - tx1
                            confidence = max(0, 1 - (distance / 100))  # Closer = higher confidence
                            candidates.append((text, confidence, distance))
                            
                        elif ty1 < y0 and abs((tx0 + tx1) / 2 - (x0 + x1) / 2) < 50:
                            # Text above
                            distance = y0 - ty1
                            confidence = max(0, 1 - (distance / 50))
                            candidates.append((text, confidence, distance))
        
        if candidates:
            # Sort by confidence, then distance
            candidates.sort(key=lambda x: (-x[1], x[2]))
            return candidates[0][0], candidates[0][1]
            
        return "", 0.0
        
    def _map_label_semantic(self, label: str) -> Tuple[str, float]:
        """Map label to canonical key using semantic matching"""
        label_lower = label.lower().strip(':')
        
        # Try direct mappings first
        direct_mappings = {
            'first name': 'patient_first_name',
            'last name': 'patient_last_name',
            'date of birth': 'patient_dob',
            'member id': 'member_id',
            'provider npi': 'provider_npi'
        }
        
        for key, value in direct_mappings.items():
            if key in label_lower:
                return value, 0.95
                
        # Use semantic matcher
        best_match = ""
        best_score = 0.0
        
        canonical_keys = [
            'patient_first_name', 'patient_last_name', 'patient_dob',
            'patient_address', 'patient_city', 'patient_state', 'patient_zip',
            'patient_phone', 'member_id', 'group_number', 'provider_name',
            'provider_npi', 'diagnosis_code', 'medication_name'
        ]
        
        for canonical in canonical_keys:
            score = self.semantic_matcher.semantic_match(label, canonical)
            if score > best_score:
                best_score = score
                best_match = canonical
                
        return best_match, best_score
        
    def extract_data_values(self) -> Dict[str, Any]:
        """Extract values with confidence tracking"""
        values = {}
        
        # Patient demographics
        demo = self.data['tier_1_mandatory_fields']['patient_demographics']
        
        # Parse name with confidence
        full_name = demo['full_name']['value']
        name_confidence = demo['full_name'].get('confidence', 0.9)
        
        parts = full_name.split()
        if parts:
            values['patient_first_name'] = parts[0]
            values['patient_last_name'] = parts[-1] if len(parts) > 1 else ''
            self.confidence.add_score('patient_first_name', name_confidence, 'extracted_json')
            self.confidence.add_score('patient_last_name', name_confidence, 'extracted_json')
            
        # DOB
        values['patient_dob'] = demo['date_of_birth']['value']
        self.confidence.add_score('patient_dob', demo['date_of_birth'].get('confidence', 0.95), 'extracted_json')
        
        # Address
        addr = demo['address']['value']
        # Parse address intelligently
        addr_parts = addr.split(',')
        if addr_parts:
            values['patient_address'] = addr_parts[0].strip()
            if len(addr_parts) > 1:
                # Try to parse city, state, zip
                remaining = ' '.join(addr_parts[1:]).strip()
                # Look for state abbreviation
                state_match = re.search(r'\b([A-Z]{2})\b', remaining)
                if state_match:
                    values['patient_state'] = state_match.group(1)
                # Look for zip
                zip_match = re.search(r'\b(\d{5}(?:-\d{4})?)\b', remaining)
                if zip_match:
                    values['patient_zip'] = zip_match.group(1)
                # City is what's left
                city = remaining
                if state_match:
                    city = city.replace(state_match.group(0), '').strip()
                if zip_match:
                    city = city.replace(zip_match.group(0), '').strip()
                values['patient_city'] = city.strip()
                
        # Phone
        phones = demo['phone_numbers']['value']
        if phones:
            values['patient_phone'] = phones[0]
            self.confidence.add_score('patient_phone', 0.9, 'extracted_json')
            
        # Insurance
        ins = self.data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
        values['member_id'] = ins['member_id']['value']
        self.confidence.add_score('member_id', ins['member_id'].get('confidence', 0.95), 'extracted_json')
        
        # Provider
        prov = self.data['tier_1_mandatory_fields']['prescriber_information']
        if 'provider_name' in prov:
            values['provider_name'] = prov['provider_name']['value']
        elif 'facility_name' in prov:
            values['provider_name'] = prov['facility_name']['value']
        if 'npi' in prov:
            values['provider_npi'] = prov['npi']['value']
        
        return values
        
    def fill_form(self, output_path: Path):
        """Fill form with all enhancements"""
        print("🔍 Extracting form structure with semantic matching...")
        structure = self.extract_form_structure()
        
        print("📊 Extracting data values with confidence tracking...")
        values = self.extract_data_values()
        
        print("⚙️ Applying conditional logic rules...")
        values, conflicts = self.rules_engine.apply_rules(values)
        if conflicts:
            print(f"  ⚠️ Resolved conflicts: {conflicts}")
            
        # Create writer
        writer = PdfWriter()
        writer.clone_reader_document_root(self.reader)
        writer.append_pages_from_reader(self.reader)
        
        # Ensure AcroForm
        if self.reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): self.reader.trailer['/Root']['/AcroForm']
            })
            
        # Fill fields
        form_values = {}
        filled_count = 0
        
        for widget_name, info in structure.items():
            canonical_key = info['canonical_key']
            confidence = info['confidence']
            
            # Debug: show what's happening
            if canonical_key:
                print(f"  Widget: {widget_name}, Label: {info['label']}, Canonical: {canonical_key}, Conf: {confidence:.2f}")
                if canonical_key in values:
                    print(f"    → Found value: {values[canonical_key]}")
            
            if canonical_key and canonical_key in values and confidence > 0.5:
                form_values[widget_name] = str(values[canonical_key])
                filled_count += 1
                print(f"✓ Filling {widget_name} ({info['label']}) = {values[canonical_key]} [conf: {confidence:.2f}]")
                
        # Update form
        for page in writer.pages:
            writer.update_page_form_field_values(page, form_values)
            
        # Force appearance
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
            
        # Generate report
        low_conf_fields = self.confidence.get_low_confidence_fields()
        
        print(f"\n📋 Form Filling Summary:")
        print(f"  - Total fields found: {len(structure)}")
        print(f"  - Fields filled: {filled_count}")
        print(f"  - Average confidence: {np.mean([float(v) for v in self.confidence.scores.values()]):.2f}")
        
        if low_conf_fields:
            print(f"  - Low confidence fields ({len(low_conf_fields)}): {low_conf_fields[:3]}...")
            
        print(f"\n✅ Output saved to {output_path}")
        
        # Save confidence report
        report_path = output_path.with_suffix('.confidence.json')
        with open(report_path, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'filled_fields': filled_count,
                'confidence_scores': self.confidence.scores,
                'field_sources': self.confidence.sources,
                'low_confidence_fields': low_conf_fields,
                'conflicts_resolved': conflicts
            }, f, indent=2)
        
        return filled_count, self.confidence.scores


if __name__ == "__main__":
    filler = EnhancedPAFiller(
        Path("test_o3/pa.pdf"),
        Path("test_o3/akshey_extracted.json")
    )
    filler.fill_form(Path("test_o3/pa_enhanced_filled.pdf"))