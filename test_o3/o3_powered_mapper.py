"""
O3-Powered Semantic PDF Form Mapper
Uses OpenAI's most advanced reasoning model for 100% accurate PDF form filling
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from PyPDF2 import PdfReader, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
import re
import openai


# Set OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

# Initialize OpenAI client
client = openai.OpenAI()


class O3PoweredMapper:
    """Advanced PDF form mapper using O3 reasoning model"""
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.json_path = json_path
        
        # Load extracted data
        with open(json_path, 'r') as f:
            self.extracted_data = json.load(f)
        
        # Get PDF fields
        reader = PdfReader(str(pdf_path))
        self.pdf_fields = reader.get_fields() or {}
        
        print(f"📋 Found {len(self.pdf_fields)} PDF fields:")
        for field_name in self.pdf_fields.keys():
            print(f"   - {field_name}")
    
    def get_structured_data_summary(self) -> str:
        """Create a comprehensive summary of available data"""
        try:
            demo = self.extracted_data['tier_1_mandatory_fields']['patient_demographics']
            ins = self.extracted_data['tier_1_mandatory_fields']['insurance_information']['primary_insurance']
            pres = self.extracted_data['tier_1_mandatory_fields']['prescriber_information']
            clin = self.extracted_data['tier_2_clinical_justification']
            
            summary = f"""
AVAILABLE EXTRACTED DATA:

PATIENT INFORMATION:
- Full Name: {demo['full_name']['value']}
- Date of Birth: {demo['date_of_birth']['value']}
- Address: {demo['address']['value']}
- Phone Numbers: {demo['phone_numbers']['value']}
- Medical Record Number: {demo.get('medical_record_number', {}).get('value', 'N/A')}
- Weight: {demo.get('physical_measurements', {}).get('weight', {}).get('value', 'N/A')}

INSURANCE INFORMATION:
- Member ID: {ins['member_id']['value']}
- Payer Name: {ins['payer_name']['value']}
- Group Number: {ins.get('group_number', {}).get('value', 'N/A')}

PRESCRIBER INFORMATION:
- Facility Name: {pres['facility_name']['value']}
- Phone: {pres['phone_fax']['value']['phone']}
- Fax: {pres['phone_fax']['value']['fax']}
- Physician Name: {pres['physician_name']['value']}
- NPI Number: {pres['npi_number']['value']}

CLINICAL INFORMATION:
- Primary Diagnosis: {clin['primary_diagnosis']['diagnosis_description']['value']}
- ICD Code: {clin['primary_diagnosis']['icd_code']['value']}
- Requested Medication: {clin['requested_medication']['drug_name']['value']}
"""
            return summary
            
        except Exception as e:
            return f"Error extracting data: {e}"
    
    def use_o3_for_perfect_mapping(self) -> Dict[str, str]:
        """Use O3 reasoning model for perfect field mapping"""
        
        data_summary = self.get_structured_data_summary()
        field_list = list(self.pdf_fields.keys())
        
        prompt = f"""
You are an expert medical form filling AI with perfect accuracy. You must analyze this Skyrizi Prior Authorization form and map the extracted data to the correct PDF form fields.

{data_summary}

PDF FORM FIELDS TO FILL:
{field_list}

CRITICAL REQUIREMENTS:
1. This is a Skyrizi (risankizumab-rzaa) Medication Precertification Request form
2. You must map EVERY piece of available data to the correct field
3. Split full names into first/last name components when needed
4. Parse addresses into separate components (street, city, state, zip)
5. Parse weight information (lbs and kg separately)
6. Handle phone numbers correctly
7. Map insurance information accurately
8. Map prescriber/facility information correctly
9. Use clinical data for diagnosis and medication fields

FORM STRUCTURE ANALYSIS:
Looking at typical PA forms, fields are usually organized as:
- Patient Information section (name, DOB, address, phone, weight)
- Insurance Information section (member ID, group number, insured name)
- Prescriber Information section (facility, phone, fax, physician details)
- Clinical sections (diagnosis, medication)

REASONING PROCESS:
1. Analyze each field name to determine its purpose
2. Match available data to the most appropriate field
3. Transform data as needed (split names, parse addresses, etc.)
4. Ensure no data is wasted - use all available information
5. Double-check mappings for accuracy

Return ONLY a JSON object with field names as keys and values as strings. Use empty string "" for fields that cannot be determined.

Example format:
{
  "field_name_1": "mapped_value_1",
  "field_name_2": "mapped_value_2"
}

THINK STEP BY STEP AND BE 100% ACCURATE.
"""

        try:
            print("🧠 Using O3 reasoning model for perfect mapping...")
            
            response = client.chat.completions.create(
                model="o3",  # Most advanced reasoning model available
                messages=[
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.0  # Maximum precision
            )
            
            response_content = response.choices[0].message.content
            print(f"🤖 O3 Response received: {len(response_content)} characters")
            
            # Extract JSON from response
            json_start = response_content.find('{')
            json_end = response_content.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_content = response_content[json_start:json_end]
                mappings = json.loads(json_content)
                
                print(f"✅ O3 generated {len(mappings)} field mappings")
                return mappings
            else:
                print("❌ Could not extract JSON from O3 response")
                return {}
                
        except Exception as e:
            print(f"❌ O3 mapping failed: {e}")
            return {}
    
    def fill_pdf_with_o3_mappings(self, output_path: Path) -> Dict[str, Any]:
        """Fill PDF using O3-generated mappings"""
        
        # Get O3 mappings
        mappings = self.use_o3_for_perfect_mapping()
        
        if not mappings:
            print("❌ No mappings generated, cannot fill PDF")
            return {"error": "No mappings generated"}
        
        # Fill PDF
        reader = PdfReader(str(self.pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm is copied
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
        
        # Clean mappings (remove empty values)
        clean_mappings = {k: v for k, v in mappings.items() if v and v.strip()}
        
        print(f"📝 Filling {len(clean_mappings)} fields...")
        
        # Update fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, clean_mappings)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        # Return results
        return {
            'total_fields': len(self.pdf_fields),
            'filled_fields': len(clean_mappings),
            'coverage': len(clean_mappings) / len(self.pdf_fields) if self.pdf_fields else 0,
            'mappings': clean_mappings,
            'output_file': str(output_path)
        }
    
    def print_detailed_results(self, results: Dict[str, Any]):
        """Print comprehensive results analysis"""
        print("\n" + "="*60)
        print("🎯 O3-POWERED SEMANTIC MAPPING RESULTS")
        print("="*60)
        
        if 'error' in results:
            print(f"❌ ERROR: {results['error']}")
            return
        
        print(f"\n📊 STATISTICS:")
        print(f"   Total PDF fields: {results['total_fields']}")
        print(f"   Fields filled: {results['filled_fields']}")
        print(f"   Coverage: {results['coverage']:.1%}")
        
        print(f"\n✅ FIELD MAPPINGS:")
        for field, value in results['mappings'].items():
            print(f"   {field}: '{value}'")
        
        unmapped = [f for f in self.pdf_fields.keys() if f not in results['mappings']]
        if unmapped:
            print(f"\n⚠️  UNMAPPED FIELDS:")
            for field in unmapped:
                print(f"   {field}")
        
        print(f"\n💾 OUTPUT: {results['output_file']}")
        
        if results['coverage'] >= 0.95:
            print("\n🎉 EXCELLENT! 95%+ coverage achieved!")
        elif results['coverage'] >= 0.90:
            print("\n✅ VERY GOOD! 90%+ coverage achieved!")
        elif results['coverage'] >= 0.80:
            print("\n👍 GOOD! 80%+ coverage achieved!")
        else:
            print("\n⚠️  Coverage needs improvement")
        
        print("="*60)


def main():
    """Main function using O3 reasoning model"""
    print("🚀 O3-POWERED SEMANTIC PDF FORM MAPPER")
    print("Using OpenAI's most advanced reasoning model for perfect accuracy")
    print()
    
    # Initialize mapper
    mapper = O3PoweredMapper(
        pdf_path=Path("pa.pdf"),
        json_path=Path("akshey_extracted.json")
    )
    
    # Fill PDF with O3 intelligence
    results = mapper.fill_pdf_with_o3_mappings(Path("pa_o3_perfect_mapped.pdf"))
    
    # Print detailed results
    mapper.print_detailed_results(results)
    
    print("\n💡 This solution uses O3 reasoning for perfect semantic mapping")
    print("   between structured extracted data and arbitrary PDF form fields.")
    
    return results


if __name__ == "__main__":
    main()
