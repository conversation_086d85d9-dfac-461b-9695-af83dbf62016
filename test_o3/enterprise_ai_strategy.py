"""
Enterprise AI Strategy for Mandolin PA Form Automation
Scalable solution for any PA form format with 100% automation
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union
from PyPDF2 import PdfReader, PdfWriter
from PyPDF2.generic import NameObject, BooleanObject
import re
import openai
from dataclasses import dataclass
from enum import Enum


# Set OpenAI API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

# Initialize OpenAI client
client = openai.OpenAI()


class FieldType(Enum):
    TEXT = "text"
    CHECKBOX = "checkbox"
    RADIO = "radio"
    DATE = "date"
    DROPDOWN = "dropdown"


class ConfidenceLevel(Enum):
    HIGH = "high"      # 95%+
    MEDIUM = "medium"  # 80-94%
    LOW = "low"        # <80%


@dataclass
class FieldMapping:
    pdf_field: str
    extracted_data_path: str
    value: Any
    confidence: float
    field_type: FieldType
    reasoning: str
    transformation_applied: str = None
    conditional_logic: str = None


@dataclass
class FormAnalysis:
    form_type: str
    insurance_company: str
    medication: str
    total_fields: int
    field_types: Dict[str, FieldType]
    conditional_fields: Dict[str, str]
    required_fields: List[str]


class EnterpriseAIMapper:
    """Enterprise-grade AI mapper for any PA form format"""
    
    def __init__(self, pdf_path: Path, json_path: Path):
        self.pdf_path = pdf_path
        self.json_path = json_path
        
        # Load extracted data
        with open(json_path, 'r') as f:
            self.extracted_data = json.load(f)
        
        # Analyze PDF form
        self.form_analysis = self._analyze_form_structure()
        
        print("🏢 ENTERPRISE AI STRATEGY FOR PA FORM AUTOMATION")
        print("=" * 60)
        print(f"📋 Form Type: {self.form_analysis.form_type}")
        print(f"🏥 Insurance: {self.form_analysis.insurance_company}")
        print(f"💊 Medication: {self.form_analysis.medication}")
        print(f"📊 Total Fields: {self.form_analysis.total_fields}")
        print()
    
    def _analyze_form_structure(self) -> FormAnalysis:
        """Analyze PDF form structure using AI"""
        
        reader = PdfReader(str(self.pdf_path))
        fields = reader.get_fields() or {}
        
        # Extract form text for analysis
        form_text = ""
        for page in reader.pages:
            form_text += page.extract_text()
        
        # Use AI to analyze form structure
        analysis_prompt = f"""
        Analyze this PA form structure and extract key information:
        
        FORM TEXT (first 2000 chars):
        {form_text[:2000]}
        
        FORM FIELDS:
        {list(fields.keys())}
        
        Extract:
        1. Form type (e.g., "Prior Authorization", "Precertification")
        2. Insurance company name
        3. Medication name
        4. Field types (text, checkbox, radio, date, dropdown)
        5. Conditional field relationships
        6. Required fields
        
        Return JSON format:
        {{
            "form_type": "string",
            "insurance_company": "string", 
            "medication": "string",
            "field_types": {{"field_name": "field_type"}},
            "conditional_fields": {{"field_name": "condition"}},
            "required_fields": ["field1", "field2"]
        }}
        """
        
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing medical forms. Return only valid JSON."},
                    {"role": "user", "content": analysis_prompt}
                ],
                temperature=0.1
            )
            
            response_content = response.choices[0].message.content
            json_start = response_content.find('{')
            json_end = response_content.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                analysis_data = json.loads(response_content[json_start:json_end])
                
                # Convert to proper types
                field_types = {}
                for field, ftype in analysis_data.get("field_types", {}).items():
                    try:
                        field_types[field] = FieldType(ftype)
                    except ValueError:
                        field_types[field] = FieldType.TEXT
                
                return FormAnalysis(
                    form_type=analysis_data.get("form_type", "Prior Authorization"),
                    insurance_company=analysis_data.get("insurance_company", "Unknown"),
                    medication=analysis_data.get("medication", "Unknown"),
                    total_fields=len(fields),
                    field_types=field_types,
                    conditional_fields=analysis_data.get("conditional_fields", {}),
                    required_fields=analysis_data.get("required_fields", [])
                )
            
        except Exception as e:
            print(f"⚠️ Form analysis failed: {e}")
        
        # Fallback analysis
        return FormAnalysis(
            form_type="Prior Authorization",
            insurance_company="Unknown",
            medication="Unknown", 
            total_fields=len(fields),
            field_types={},
            conditional_fields={},
            required_fields=[]
        )
    
    def create_intelligent_mappings(self) -> List[FieldMapping]:
        """Create intelligent field mappings using advanced AI"""
        
        # Get PDF fields
        reader = PdfReader(str(self.pdf_path))
        pdf_fields = reader.get_fields() or {}
        
        # Create comprehensive prompt for AI mapping
        mapping_prompt = f"""
        You are an expert AI system for mapping extracted medical data to PA form fields.
        
        TASK: Map extracted JSON data to PDF form fields with 100% accuracy.
        
        EXTRACTED DATA:
        {json.dumps(self.extracted_data, indent=2)}
        
        PDF FORM FIELDS:
        {list(pdf_fields.keys())}
        
        FORM CONTEXT:
        - Form Type: {self.form_analysis.form_type}
        - Insurance: {self.form_analysis.insurance_company}
        - Medication: {self.form_analysis.medication}
        
        REQUIREMENTS:
        1. Map EVERY piece of extracted data to appropriate fields
        2. Split compound data (full name → first/last, address → components)
        3. Handle data transformations (dates, weights, etc.)
        4. Identify field types (text, checkbox, radio)
        5. Apply conditional logic where needed
        6. Provide confidence scores (0.0-1.0)
        7. Explain reasoning for each mapping
        
        RETURN FORMAT (JSON array):
        [
            {{
                "pdf_field": "field_name",
                "extracted_data_path": "path.to.data",
                "value": "mapped_value",
                "confidence": 0.95,
                "field_type": "text|checkbox|radio|date|dropdown",
                "reasoning": "explanation",
                "transformation_applied": "description or null",
                "conditional_logic": "if applicable or null"
            }}
        ]
        
        THINK STEP BY STEP AND BE 100% ACCURATE.
        """
        
        try:
            print("🧠 Creating intelligent mappings with advanced AI...")
            
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are the world's best medical form mapping AI. Return only valid JSON array."},
                    {"role": "user", "content": mapping_prompt}
                ],
                temperature=0.0
            )
            
            response_content = response.choices[0].message.content
            
            # Extract JSON array
            json_start = response_content.find('[')
            json_end = response_content.rfind(']') + 1
            
            if json_start >= 0 and json_end > json_start:
                mappings_data = json.loads(response_content[json_start:json_end])
                
                # Convert to FieldMapping objects
                mappings = []
                for mapping in mappings_data:
                    try:
                        field_mapping = FieldMapping(
                            pdf_field=mapping["pdf_field"],
                            extracted_data_path=mapping["extracted_data_path"],
                            value=mapping["value"],
                            confidence=float(mapping["confidence"]),
                            field_type=FieldType(mapping["field_type"]),
                            reasoning=mapping["reasoning"],
                            transformation_applied=mapping.get("transformation_applied"),
                            conditional_logic=mapping.get("conditional_logic")
                        )
                        mappings.append(field_mapping)
                    except Exception as e:
                        print(f"⚠️ Skipping invalid mapping: {e}")
                
                print(f"✅ Created {len(mappings)} intelligent mappings")
                return mappings
            
        except Exception as e:
            print(f"❌ AI mapping failed: {e}")
        
        return []
    
    def validate_mappings(self, mappings: List[FieldMapping]) -> Dict[str, Any]:
        """Validate mappings with confidence scoring"""
        
        validation_results = {
            "total_mappings": len(mappings),
            "high_confidence": 0,
            "medium_confidence": 0,
            "low_confidence": 0,
            "validation_errors": [],
            "coverage_score": 0.0,
            "quality_score": 0.0
        }
        
        for mapping in mappings:
            if mapping.confidence >= 0.95:
                validation_results["high_confidence"] += 1
            elif mapping.confidence >= 0.80:
                validation_results["medium_confidence"] += 1
            else:
                validation_results["low_confidence"] += 1
                validation_results["validation_errors"].append(
                    f"Low confidence mapping: {mapping.pdf_field} ({mapping.confidence:.2f})"
                )
        
        # Calculate scores
        total_fields = self.form_analysis.total_fields
        validation_results["coverage_score"] = len(mappings) / total_fields if total_fields > 0 else 0
        
        avg_confidence = sum(m.confidence for m in mappings) / len(mappings) if mappings else 0
        validation_results["quality_score"] = avg_confidence
        
        return validation_results
    
    def fill_pdf_enterprise(self, output_path: Path) -> Dict[str, Any]:
        """Fill PDF with enterprise-grade validation"""
        
        # Create mappings
        mappings = self.create_intelligent_mappings()
        
        if not mappings:
            return {"error": "No mappings created"}
        
        # Validate mappings
        validation = self.validate_mappings(mappings)
        
        # Convert to simple dict for PDF filling
        field_values = {}
        for mapping in mappings:
            if mapping.confidence >= 0.80:  # Only use high/medium confidence
                field_values[mapping.pdf_field] = str(mapping.value)
        
        # Fill PDF
        reader = PdfReader(str(self.pdf_path))
        writer = PdfWriter()
        writer.clone_reader_document_root(reader)
        writer.append_pages_from_reader(reader)
        
        # Ensure AcroForm is copied
        if reader.trailer['/Root'].get('/AcroForm'):
            writer._root_object.update({
                NameObject('/AcroForm'): reader.trailer['/Root']['/AcroForm']
            })
        
        # Update fields
        for page in writer.pages:
            writer.update_page_form_field_values(page, field_values)
        
        # Force appearance rebuild
        writer._root_object.update({
            NameObject("/NeedAppearances"): BooleanObject(True)
        })
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        return {
            "mappings": mappings,
            "validation": validation,
            "field_values": field_values,
            "output_file": str(output_path)
        }
    
    def print_enterprise_results(self, results: Dict[str, Any]):
        """Print enterprise-grade results"""
        
        if "error" in results:
            print(f"❌ ERROR: {results['error']}")
            return
        
        mappings = results["mappings"]
        validation = results["validation"]
        
        print("🎯 ENTERPRISE AI MAPPING RESULTS")
        print("=" * 60)
        
        print(f"\n📊 VALIDATION METRICS:")
        print(f"   Total mappings: {validation['total_mappings']}")
        print(f"   High confidence (95%+): {validation['high_confidence']}")
        print(f"   Medium confidence (80-94%): {validation['medium_confidence']}")
        print(f"   Low confidence (<80%): {validation['low_confidence']}")
        print(f"   Coverage score: {validation['coverage_score']:.1%}")
        print(f"   Quality score: {validation['quality_score']:.1%}")
        
        print(f"\n✅ INTELLIGENT MAPPINGS:")
        for mapping in mappings[:10]:  # Show first 10
            confidence_emoji = "🟢" if mapping.confidence >= 0.95 else "🟡" if mapping.confidence >= 0.80 else "🔴"
            print(f"   {confidence_emoji} {mapping.pdf_field}: '{mapping.value}' ({mapping.confidence:.2f})")
            print(f"      └─ {mapping.reasoning}")
        
        if len(mappings) > 10:
            print(f"   ... and {len(mappings) - 10} more mappings")
        
        if validation["validation_errors"]:
            print(f"\n⚠️ VALIDATION WARNINGS:")
            for error in validation["validation_errors"][:5]:
                print(f"   • {error}")
        
        print(f"\n💾 OUTPUT: {results['output_file']}")
        
        if validation["coverage_score"] >= 0.95 and validation["quality_score"] >= 0.90:
            print("\n🏆 ENTERPRISE GRADE: Ready for production!")
        elif validation["coverage_score"] >= 0.85 and validation["quality_score"] >= 0.80:
            print("\n✅ HIGH QUALITY: Good for deployment with monitoring")
        else:
            print("\n⚠️ NEEDS REVIEW: Manual validation recommended")
        
        print("=" * 60)


def main():
    """Main function for enterprise AI strategy"""
    print("🚀 ENTERPRISE AI STRATEGY FOR MANDOLIN")
    print("Scalable solution for ANY PA form format")
    print()
    
    # Initialize enterprise mapper
    mapper = EnterpriseAIMapper(
        pdf_path=Path("pa.pdf"),
        json_path=Path("akshey_extracted.json")
    )
    
    # Fill PDF with enterprise AI
    results = mapper.fill_pdf_enterprise(Path("pa_enterprise_ai.pdf"))
    
    # Print enterprise results
    mapper.print_enterprise_results(results)
    
    print("\n💡 ENTERPRISE STRATEGY BENEFITS:")
    print("   • Works with ANY PA form format")
    print("   • Handles form variations automatically")
    print("   • Scales to hundreds of insurance companies")
    print("   • No manual configuration required")
    print("   • Enterprise-grade validation and monitoring")
    print("   • 100% automation ready")
    
    return results


if __name__ == "__main__":
    main()
