{"timestamp": "2025-06-12T20:47:25.833736", "filled_fields": 0, "confidence_scores": {"Indicate CB.1": 0.4678740310668945, "Indicate T.2": 0.4933533477783203, "Indicate T.3": 0.38435150146484376, "Indicate T.4": 0.38435150146484376, "Indicate CB.5": 0.4829257965087891, "Indicate T.6": 0.49570693969726565, "Indicate T.7": 0.3540419006347656, "Indicate T.8": 0.3933497619628906, "Request by T": 0.4896059417724609, "Phone T": 0.12287139892578125, "Fax T": 0.1143553924560547, "T11": 0.49636039733886717, "T12": 0.49638053894042966, "T13": 0.4965703582763672, "T14": 0.17895973205566407, "T15": 0.17895973205566407, "T16": 0.44914535522460936, "T17": 0.490723876953125, "T18": 0.4964000701904297, "T19": 0.4482865905761719, "T20": 0.4379472351074219, "T21": 0.4464866638183594, "T21B": 0.4927799987792969, "T21C": 0.4782874298095703, "T21D": 0.4906617736816406, "T21E": 0.44710128784179687, "T21F": 0.4966679382324219, "Insurance Info T.1": 0.4979850769042969, "Insurance Info CB.4": 0.41766693115234377, "Insurance Info CB.5": 0.4882733154296875, "Insurance Info T.2": 0.49805038452148437, "Insurance Info T.6": 0.49924301147460937, "Insurance Info T.7": 0.49813888549804686, "Insurance Info T.3": 0.49815372467041014, "Insurance Info T.8": 0.48621139526367185, "Insurance Info CB.9": 0.4870175552368164, "Insurance Info CB.10": 0.48550392150878907, "Insurance Info T.11": 0.4588334655761719, "Insurance Info CB.12": 0.47513336181640625, "Insurance Info CB.13": 0.4757794189453125, "Insurance Info T.14": 0.13403839111328125, "Presc Info T.1": 0.3371331787109375, "Presc Info T.7": 0.18425354003906252, "Presc Info T.11": 0.435379753112793, "Presc Info T.12": 0.46501998901367186, "Presc Info T.17": 0.4595086669921875, "Presc Info T.2": 0.3374673461914063, "Presc Info CB.3": 0.48815765380859377, "Presc Info CB.4": 0.4881550598144531, "Presc Info CB.5": 0.4878753662109375, "Presc Info CB.6": 0.49985565185546876, "Presc Info T.8": 0.18468719482421875, "Presc Info T.9": 0.4500555419921875, "Presc Info T.10": 0.4500555419921875, "Presc Info T.13": 0.456087646484375, "Presc Info T.14": 0.45828460693359374, "Presc Info T.15": 0.461617431640625, "Presc Info T.16": 0.4976132202148438, "Presc Info T.18": 0.4598187255859375, "Presc Info T.19": 0.4598187255859375, "Presc Info CB.20": 0.48821311950683594, "Presc Info CB.21": 0.45678230285644533, "Presc Info T.23": 0.4996837615966797, "Provider Admin CB.1": 0.4613861083984375, "Provider Admin CB.2": 0.34039840698242185, "Provider Admin CB.3": 0.46585357666015625, "Provider Admin T.4": 0.4207452392578125, "Provider Admin T.5": 0.4842242431640625, "Provider Admin CB.6": 0.4752154541015625, "Provider Admin T.7": 0.41931373596191407, "Provider Admin T.8": 0.4832464599609375, "Provider Admin CB.9.0": 0.46266754150390627, "Provider Admin T.10.0.0": 0.3461163330078125, "Provider Admin T.10.1.0": 0.4897006607055664, "Provider Admin CB.11": 0.0, "Provider Admin CB.12": 0.3248486328125, "Provider Admin CB.13": 0.46587371826171875, "Provider Admin CB.14": 0.46587371826171875, "Provider Admin T.17.0": 0.456854248046875, "Provider Admin T.17.1": 0.28988433837890626, "Provider Admin T.18": 0.43064285278320313, "Provider Admin T.19": 0.4577195739746094, "Provider Admin T.20": 0.49629806518554687, "Provider Admin T.21": 0.49063232421875, "Product T.1": 0.48354278564453124, "Product T.2": 0.4898968505859375, "Diagnosis T.1": 0.49367782592773435, "Diagnosis T.2": 0.4883479309082031, "Diagnosis T.3": 0.49381973266601564, "Clinical CB.0": 0.4752410888671875, "Clinical CB.1": 0.4786538124084473, "Clinical CB.2": 0.4808935546875, "Clinical CB.3": 0.4807135009765625, "Clinical CB.4": 0.38993469238281253, "Clinical CB.5": 0.48686134338378906, "Clinical CB.6": 0.48216552734375, "Clinical CB.7": 0.4788407897949219, "Clinical CB.8": 0.4688029479980469, "Clinical CB.9": 0.48939559936523436, "Clinical CB.10": 0.4858575439453125, "Clinical CB.11": 0.48931198120117186, "Clinical CB.12": 0.4931817626953125, "Clinical CB.13": 0.4915106201171875, "Clinical CB.14": 0.489921875, "Clinical CB.15": 0.4981915283203125, "Clinical CB.19": 0.4973081970214844, "Clinical CB.20": 0.49944448471069336, "Clinical CB.21": 0.48902938842773436, "Clinical CB.22a": 0.49944448471069336, "Clinical CB.23a": 0.4895805358886719, "Clinical CB.24a": 0.4910188102722168, "Clinical CB.25": 0.4901219177246094, "Clinical CB.26": 0.49103466033935544, "Clinical T.16": 0.4954010009765625, "Clinical T.17": 0.49515365600585937, "Clinical T.18": 0.47601272583007814, "Clinical CB.27": 0.2497227478027344, "Clinical CB.28a": 0.48252426147460936, "Clinical T.42": 0.495941162109375, "Clinical T.43": 0.47596221923828125, "Clinical T.44": 0.3424822998046875, "patient_first_name": "1.0", "patient_last_name": "1.0", "patient_dob": "1.0", "patient_phone": 0.9, "member_id": "1.0"}, "field_sources": {"Indicate CB.1": "page 0, label: !", "Indicate T.2": "page 0, label: !\"#$%#!&'!#%($#)(*#+!!\"#$%#!,$#(!", "Indicate T.3": "page 0, label: F:,,qLkH,?zqC]zIq’HqTxC4,HIH?qO8?q,H1k’,HqLxBq4BHTHBIkLkTOIkx8qBH", "Indicate T.4": "page 0, label: F:,,qLkH,?zqC]zIq’HqTxC4,HIH?qO8?q,H1k’,HqLxBq4BHTHBIkLkTOIkx8qBH", "Indicate CB.5": "page 0, label: !\"#$%#&'()'*$+#,", "Indicate T.6": "page 0, label: !.&*#/*0$#/&*!&'!#1(%$234!5$#(!&'!6$7#!#%($#)(*#!", "Indicate T.7": "page 0, label: !.&*#/*0$#/&*!&'!#1(%$234!5$#(!&'!6$7#!#%($#)(*#!", "Indicate T.8": "page 0, label: ! -! !", "Request by T": "page 0, label: !-#*#-+'.'*$+'/(&0#12#%+#)&34,", "Phone T": "page 0, label: BkHCDhq", "Fax T": "page 0, label: y,HOzHqXzHqjH?kTOBHq[HD]HzIqGxBCq", "T11": "page 1, label: ;vugbcpvu?Yf,u", "T12": "page 1, label: ;vuMYpvu?Yf,u", "T13": "page 1, label: qYvb,", "T14": "page 0, label: !! !", "T15": "page 0, label: ! 81&*(+!", "T16": "page 0, label: .xU#u", "T17": "page 0, label: Rwq#u", "T18": "page 1, label: qYvb,", "T19": "page 0, label: (bv)#u", "T20": "page 0, label: z", "T21": "page 0, label: Rwq#u", "T21B": "page 0, label: ;vu(@cc,;vu<,bAjv#", "T21C": "page 0, label: uzc", "T21D": "page 0, label: ;vu/,bAjv#", "T21E": "page 0, label: (,", "T21F": "page 0, label: >>,cAb,p#", "Insurance Info T.1": "page 0, label: #u", "Insurance Info CB.4": "page 0, label: DYvb,;vujYE,uzvj,cuCzE,cYA,Fu", "Insurance Info CB.5": "page 0, label: u", "Insurance Info T.2": "page 0, label: #u", "Insurance Info T.6": "page 0, label: DYvb,;vujYE,uzvj,cuCzE,cYA,Fu", "Insurance Info T.7": "page 0, label: u", "Insurance Info T.3": "page 0, label: #u", "Insurance Info T.8": "page 0, label: (Yccb,cu?Yf,#u", "Insurance Info CB.9": "page 0, label: u", "Insurance Info CB.10": "page 0, label: G,pu", "Insurance Info T.11": "page 0, label: w", "Insurance Info CB.12": "page 0, label: ?v@SR.S@D", "Insurance Info CB.13": "page 0, label: G,p", "Insurance Info T.14": "page 0, label: u?z uw", "Presc Info T.1": "page 0, label: u?zuuuw", "Presc Info T.7": "page 0, label: u?zuuuw", "Presc Info T.11": "page 0, label: qjz", "Presc Info T.12": "page 0, label: gY", "Presc Info T.17": "page 0, label: gY", "Presc Info T.2": "page 0, label: ?v@SR.S@D", "Presc Info CB.3": "page 0, label: !\"#$%&'()$*+'", "Presc Info CB.4": "page 0, label: KL.L", "Presc Info CB.5": "page 0, label: LxL", "Presc Info CB.6": "page 0, label: u", "Presc Info T.8": "page 0, label: ?v@SR.S@D", "Presc Info T.9": "page 0, label: KL.L", "Presc Info T.10": "page 0, label: LxL", "Presc Info T.13": "page 0, label: (bv)#u", "Presc Info T.14": "page 0, label: ?qwu", "Presc Info T.15": "page 0, label: u", "Presc Info T.16": "page 0, label: Nqw?#", "Presc Info T.18": "page 0, label: J#u", "Presc Info T.19": "page 0, label: Nqw?#", "Presc Info CB.20": "page 0, label: !\"#$%&',)$*+'", "Presc Info CB.21": "page 0, label: P.SgOCvBgvOCTCBSSg", "Presc Info T.23": "page 0, label: #u", "Provider Admin CB.1": "page 0, label: E", "Provider Admin CB.2": "page 0, label: >HOY’fb;bpv,c,’u", "Provider Admin CB.3": "page 0, label: uT,", "Provider Admin T.4": "page 0, label: qjz", "Provider Admin T.5": "page 0, label: qjz", "Provider Admin CB.6": "page 0, label: (,", "Provider Admin T.7": "page 0, label: qjz", "Provider Admin T.8": "page 0, label: qjz", "Provider Admin CB.9.0": "page 0, label: -", "Provider Admin T.10.0.0": "page 0, label: ;,#uu", "Provider Admin T.10.1.0": "page 0, label: -’’c,pp#", "Provider Admin CB.11": "page 0, label: ?qwu", "Provider Admin CB.12": "page 0, label: CbY;PpuxHHbC,u", "Provider Admin CB.13": "page 0, label: uqj)pb", "Provider Admin CB.14": "page 0, label: u", "Provider Admin T.17.0": "page 0, label: uxvj,cu", "Provider Admin T.17.1": "page 0, label: uxvj,cu", "Provider Admin T.18": "page 0, label: qjz", "Provider Admin T.19": "page 0, label: ugY", "Provider Admin T.20": "page 0, label: EmD", "Provider Admin T.21": "page 0, label: qw?#", "Product T.1": "page 0, label: 5/%#,", "Product T.2": "page 0, label: 6-#12#(*4,", "Diagnosis T.1": "page 0, label: <PERSON><PERSON>@vD", "Diagnosis T.2": "page 0, label: IvRCB@.OUIEKNIKC@vD", "Diagnosis T.3": "page 0, label: GgbvOIEKNIKC@vD", "Clinical CB.0": "page 0, label: K’W-E", "Clinical CB.1": "page 0, label: OHz", "Clinical CB.2": "page 0, label: q", "Clinical CB.3": "page 0, label: q", "Clinical CB.4": "page 0, label: qq", "Clinical CB.5": "page 0, label: OHzq", "Clinical CB.6": "page 0, label: CkIFk8qRqCx8IFzqxLqk8kIkOIk81qIFHBO4GNq", "Clinical CB.7": "page 0, label: UqIHzIq", "Clinical CB.8": "page 0, label: GqFWX[:h", "Clinical CB.9": "page 0, label: FHqBHz],IzqxLqIFHqI]’HBT],xzkzqFRShqIHzI[", "Clinical CB.10": "page 0, label: GqFWX[:h", "Clinical CB.11": "page 0, label: BHqq", "Clinical CB.12": "page 0, label: !\"#$%&'(')*+#", "Clinical CB.13": "page 0, label: q,OIH8Iq", "Clinical CB.14": "page 0, label: q,OIH8Iq", "Clinical CB.15": "page 0, label: q,OIH8Iq", "Clinical CB.19": "page 1, label: =W’E6F1-G51TO1T-3=G9-", "Clinical CB.20": "page 1, label: q", "Clinical CB.21": "page 1, label: q", "Clinical CB.22a": "page 1, label: q", "Clinical CB.23a": "page 1, label: qqq", "Clinical CB.24a": "page 1, label: OHzq", "Clinical CB.25": "page 1, label: qq", "Clinical CB.26": "page 1, label: OHzq", "Clinical T.16": "page 1, label: CHHQzqMHq_qO8?q`[q", "Clinical T.17": "page 1, label: [q", "Clinical T.18": "page 1, label: G[", "Clinical CB.27": "page 1, label: q", "Clinical CB.28a": "page 1, label: qy,HOzHqk8?kTOIHqCOk8IH8O8THq?xzH", "Clinical T.42": "page 1, label: N.gvD", "Clinical T.43": "page 1, label: u u u u u", "Clinical T.44": "page 1, label: u u u u u", "patient_first_name": "extracted_json", "patient_last_name": "extracted_json", "patient_dob": "extracted_json", "patient_phone": "extracted_json", "member_id": "extracted_json"}, "low_confidence_fields": ["Indicate CB.1", "Indicate T.2", "Indicate T.3", "Indicate T.4", "Indicate CB.5", "Indicate T.6", "Indicate T.7", "Indicate T.8", "Request by T", "Phone T", "Fax T", "T11", "T12", "T13", "T14", "T15", "T16", "T17", "T18", "T19", "T20", "T21", "T21B", "T21C", "T21D", "T21E", "T21F", "Insurance Info T.1", "Insurance Info CB.4", "Insurance Info CB.5", "Insurance Info T.2", "Insurance Info T.6", "Insurance Info T.7", "Insurance Info T.3", "Insurance Info T.8", "Insurance Info CB.9", "Insurance Info CB.10", "Insurance Info T.11", "Insurance Info CB.12", "Insurance Info CB.13", "Insurance Info T.14", "Presc Info T.1", "Presc Info T.7", "Presc Info T.11", "Presc Info T.12", "Presc Info T.17", "Presc Info T.2", "Presc Info CB.3", "Presc Info CB.4", "Presc Info CB.5", "Presc Info CB.6", "Presc Info T.8", "Presc Info T.9", "Presc Info T.10", "Presc Info T.13", "Presc Info T.14", "Presc Info T.15", "Presc Info T.16", "Presc Info T.18", "Presc Info T.19", "Presc Info CB.20", "Presc Info CB.21", "Presc Info T.23", "Provider Admin CB.1", "Provider Admin CB.2", "Provider Admin CB.3", "Provider Admin T.4", "Provider Admin T.5", "Provider Admin CB.6", "Provider Admin T.7", "Provider Admin T.8", "Provider Admin CB.9.0", "Provider Admin T.10.0.0", "Provider Admin T.10.1.0", "Provider Admin CB.11", "Provider Admin CB.12", "Provider Admin CB.13", "Provider Admin CB.14", "Provider Admin T.17.0", "Provider Admin T.17.1", "Provider Admin T.18", "Provider Admin T.19", "Provider Admin T.20", "Provider Admin T.21", "Product T.1", "Product T.2", "Diagnosis T.1", "Diagnosis T.2", "Diagnosis T.3", "Clinical CB.0", "Clinical CB.1", "Clinical CB.2", "Clinical CB.3", "Clinical CB.4", "Clinical CB.5", "Clinical CB.6", "Clinical CB.7", "Clinical CB.8", "Clinical CB.9", "Clinical CB.10", "Clinical CB.11", "Clinical CB.12", "Clinical CB.13", "Clinical CB.14", "Clinical CB.15", "Clinical CB.19", "Clinical CB.20", "Clinical CB.21", "Clinical CB.22a", "Clinical CB.23a", "Clinical CB.24a", "Clinical CB.25", "Clinical CB.26", "Clinical T.16", "Clinical T.17", "Clinical T.18", "Clinical CB.27", "Clinical CB.28a", "Clinical T.42", "Clinical T.43", "Clinical T.44"], "conflicts_resolved": []}