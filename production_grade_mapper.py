"""
PRODUCTION-<PERSON><PERSON>DE FIELD MAPPER
Implements multi-signal scoring with hierarchical context and hard guards
Fixes the root cause of field mapping failures
"""

import json
import hashlib
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
from PyPDF2 import PdfReader, PdfWriter
import fitz
import numpy as np
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Type validation patterns
TYPE_PATTERNS = {
    "date": re.compile(r"^\d{4}-\d{2}-\d{2}$|^\d{2}/\d{2}/\d{4}$|^\d{1,2}/\d{1,2}/\d{4}$"),
    "phone": re.compile(r"^\d{3}[-.\s]?\d{3}[-.\s]?\d{4}$"),
    "zip": re.compile(r"^\d{5}(-\d{4})?$"),
    "state": re.compile(r"^[A-Z]{2}$"),
    "npi": re.compile(r"^\d{10}$"),
    "checkbox": re.compile(r"^(Yes|No|☑︎|✓|true|false)$", re.I),
    "name": re.compile(r"^[A-Za-z\s\'\-\.]{1,50}$"),
    "member_id": re.compile(r"^[A-Za-z0-9\-]{5,20}$")
}

# Canonical field schema with type definitions
CANONICAL_SCHEMA = {
    "patient_first_name": {"type": "name", "section": "patient", "priority": 1},
    "patient_last_name": {"type": "name", "section": "patient", "priority": 1},
    "patient_dob": {"type": "date", "section": "patient", "priority": 1},
    "patient_address": {"type": "text", "section": "patient", "priority": 2},
    "patient_city": {"type": "text", "section": "patient", "priority": 2},
    "patient_state": {"type": "state", "section": "patient", "priority": 2},
    "patient_zip": {"type": "zip", "section": "patient", "priority": 2},
    "patient_phone": {"type": "phone", "section": "patient", "priority": 2},
    "insurance_member_id": {"type": "member_id", "section": "insurance", "priority": 1},
    "insurance_group": {"type": "text", "section": "insurance", "priority": 2},
    "provider_name": {"type": "name", "section": "provider", "priority": 1},
    "provider_npi": {"type": "npi", "section": "provider", "priority": 1},
    "provider_phone": {"type": "phone", "section": "provider", "priority": 2},
    "provider_fax": {"type": "phone", "section": "provider", "priority": 3}
}

@dataclass
class SpatialToken:
    """Represents a text token with spatial information"""
    text: str
    bbox: List[float]  # [x1, y1, x2, y2]
    font_size: float
    is_bold: bool
    center_x: float
    center_y: float

@dataclass
class WidgetContext:
    """Rich context for a form widget"""
    widget_id: str
    bbox: List[float]
    direct_neighbors: List[str]  # Text tokens within 20px
    section_header: Optional[str]  # Nearest section header
    row_label: Optional[str]  # If in a table, the row label
    full_context: str  # Concatenated context for embedding

@dataclass
class FieldMapping:
    """A validated field mapping with confidence scores"""
    widget_id: str
    canonical_key: str
    confidence: float
    embedding_score: float
    jaccard_score: float
    proximity_score: float
    type_compatibility: float
    context: WidgetContext

class SpatialContextAnalyzer:
    """Analyzes PDF spatial layout for hierarchical context"""
    
    def __init__(self):
        self.section_headers = [
            "patient information", "insurance information", "prescriber information",
            "provider information", "clinical information", "medication information"
        ]
    
    def extract_spatial_tokens(self, pdf_path: Path) -> List[SpatialToken]:
        """Extract all text tokens with spatial and formatting information"""
        doc = fitz.open(str(pdf_path))
        page = doc[0]
        text_dict = page.get_text("dict")
        
        tokens = []
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if len(text) > 1:  # Skip single characters
                            bbox = span["bbox"]
                            token = SpatialToken(
                                text=text,
                                bbox=list(bbox),
                                font_size=span.get("size", 10),
                                is_bold="bold" in str(span.get("flags", 0)),
                                center_x=(bbox[0] + bbox[2]) / 2,
                                center_y=(bbox[1] + bbox[3]) / 2
                            )
                            tokens.append(token)
        
        doc.close()
        return tokens
    
    def build_widget_contexts(self, pdf_path: Path, form_fields: Dict) -> Dict[str, WidgetContext]:
        """Build rich context for each widget using spatial analysis"""
        logger.info("🔍 Building hierarchical widget contexts")
        
        # Extract spatial tokens
        tokens = self.extract_spatial_tokens(pdf_path)
        
        # Build R-tree style spatial index (simplified)
        spatial_index = {token.text.lower(): token for token in tokens}
        
        widget_contexts = {}
        
        for widget_id, field_obj in form_fields.items():
            # Get widget position (fallback to estimated position if no rect)
            rect = field_obj.get("/Rect", [0, 0, 0, 0])
            if rect == [0, 0, 0, 0] or not rect:
                # Estimate position based on widget ID patterns
                rect = self._estimate_widget_position(widget_id)
            
            widget_center_x = (rect[0] + rect[2]) / 2
            widget_center_y = (rect[1] + rect[3]) / 2
            
            # Find context components
            direct_neighbors = self._find_direct_neighbors(
                tokens, widget_center_x, widget_center_y, radius=30
            )
            
            section_header = self._find_section_header(
                tokens, widget_center_x, widget_center_y
            )
            
            row_label = self._find_row_label(
                tokens, widget_center_x, widget_center_y
            )
            
            # Build full context string
            context_parts = []
            if section_header:
                context_parts.append(f"Section: {section_header}")
            if row_label:
                context_parts.append(f"Label: {row_label}")
            if direct_neighbors:
                context_parts.append(f"Near: {' '.join(direct_neighbors)}")
            
            full_context = " | ".join(context_parts)
            
            widget_context = WidgetContext(
                widget_id=widget_id,
                bbox=rect,
                direct_neighbors=direct_neighbors,
                section_header=section_header,
                row_label=row_label,
                full_context=full_context
            )
            
            widget_contexts[widget_id] = widget_context
            logger.info(f"📍 {widget_id:15s}: {full_context}")
        
        return widget_contexts
    
    def _estimate_widget_position(self, widget_id: str) -> List[float]:
        """Estimate widget position based on ID patterns for forms with empty rects"""
        # For T-series fields, estimate based on typical PA form layout
        if widget_id.startswith('T'):
            try:
                field_num = int(widget_id[1:])
                # Rough estimation based on common PA form layouts
                y_pos = 200 + (field_num * 25)  # Vertical spacing
                x_pos = 100 + ((field_num % 3) * 200)  # Horizontal spacing
                return [x_pos, y_pos, x_pos + 150, y_pos + 20]
            except ValueError:
                pass
        
        # Default fallback position
        return [100, 100, 250, 120]
    
    def _find_direct_neighbors(self, tokens: List[SpatialToken], x: float, y: float, radius: float = 30) -> List[str]:
        """Find text tokens within radius of widget position"""
        neighbors = []
        for token in tokens:
            distance = ((token.center_x - x) ** 2 + (token.center_y - y) ** 2) ** 0.5
            if distance <= radius:
                neighbors.append(token.text)
        return neighbors
    
    def _find_section_header(self, tokens: List[SpatialToken], x: float, y: float) -> Optional[str]:
        """Find the nearest section header above the widget"""
        best_header = None
        min_distance = float('inf')
        
        for token in tokens:
            # Look for section headers (larger font, bold, or contains key terms)
            is_header = (
                token.font_size > 12 or 
                token.is_bold or
                any(header in token.text.lower() for header in self.section_headers)
            )
            
            if is_header and token.center_y < y:  # Must be above the widget
                distance = ((token.center_x - x) ** 2 + (y - token.center_y) ** 2) ** 0.5
                if distance < min_distance:
                    min_distance = distance
                    best_header = token.text
        
        return best_header
    
    def _find_row_label(self, tokens: List[SpatialToken], x: float, y: float) -> Optional[str]:
        """Find row label if widget is in a table structure"""
        # Look for text tokens to the left of the widget on the same row
        row_labels = []
        
        for token in tokens:
            # Same row (within 10px vertically) and to the left
            if abs(token.center_y - y) <= 10 and token.center_x < x:
                distance = x - token.center_x
                if distance <= 200:  # Within reasonable label distance
                    row_labels.append((distance, token.text))
        
        # Return closest label to the left
        if row_labels:
            row_labels.sort(key=lambda x: x[0])
            return row_labels[0][1]
        
        return None

class MultiSignalMapper:
    """Multi-signal field mapper with hierarchical context"""
    
    def __init__(self):
        self.context_analyzer = SpatialContextAnalyzer()
        
        # Synonyms for embedding similarity
        self.canonical_synonyms = {
            "patient_first_name": ["first name", "fname", "given name", "patient first"],
            "patient_last_name": ["last name", "lname", "surname", "patient last"],
            "patient_dob": ["date of birth", "dob", "birth date", "birthdate"],
            "patient_phone": ["patient phone", "home phone", "phone", "telephone"],
            "patient_address": ["address", "street", "home address", "residence"],
            "patient_city": ["city", "town"],
            "patient_state": ["state", "st"],
            "patient_zip": ["zip", "postal", "zip code"],
            "insurance_member_id": ["member id", "insurance id", "policy", "member number"],
            "provider_name": ["provider", "physician", "doctor", "prescriber"],
            "provider_npi": ["npi", "provider id", "physician id"],
            "provider_phone": ["provider phone", "office phone", "clinic phone"]
        }
    
    def create_validated_mappings(self, pdf_path: Path, form_fields: Dict, patient_data: Dict) -> List[FieldMapping]:
        """Create validated field mappings using multi-signal scoring"""
        logger.info("🎯 Creating multi-signal field mappings")
        
        # Build hierarchical contexts
        widget_contexts = self.context_analyzer.build_widget_contexts(pdf_path, form_fields)
        
        # Extract patient values for type compatibility checking
        patient_values = self._extract_patient_values(patient_data)
        
        mappings = []
        used_canonical_keys = set()
        
        # Sort widgets by confidence to assign best matches first
        widget_scores = []
        
        for widget_id, context in widget_contexts.items():
            # Skip non-text fields
            field_obj = form_fields.get(widget_id)
            field_type = field_obj.get("/FT", "") if field_obj else ""
            if "/Tx" not in str(field_type):
                continue
            
            best_mapping = None
            best_score = 0
            
            for canonical_key in CANONICAL_SCHEMA.keys():
                if canonical_key in used_canonical_keys:
                    continue
                
                # Calculate multi-signal score
                score_components = self._calculate_multi_signal_score(
                    context, canonical_key, patient_values
                )
                
                total_score = (
                    0.45 * score_components['embedding'] +
                    0.25 * score_components['jaccard'] +
                    0.15 * score_components['proximity'] +
                    0.15 * score_components['type_compatibility']
                )
                
                if total_score > best_score:
                    best_score = total_score
                    best_mapping = FieldMapping(
                        widget_id=widget_id,
                        canonical_key=canonical_key,
                        confidence=total_score,
                        embedding_score=score_components['embedding'],
                        jaccard_score=score_components['jaccard'],
                        proximity_score=score_components['proximity'],
                        type_compatibility=score_components['type_compatibility'],
                        context=context
                    )
            
            # Only accept high-confidence mappings
            if best_mapping and best_score >= 0.75:
                mappings.append(best_mapping)
                used_canonical_keys.add(best_mapping.canonical_key)
                logger.info(
                    f"✅ {widget_id:15s} → {best_mapping.canonical_key:20s} "
                    f"(conf: {best_score:.3f} = emb:{best_mapping.embedding_score:.2f} "
                    f"+ jac:{best_mapping.jaccard_score:.2f} + prox:{best_mapping.proximity_score:.2f} "
                    f"+ type:{best_mapping.type_compatibility:.2f})"
                )
            else:
                logger.warning(f"❌ {widget_id:15s} → LOW CONFIDENCE ({best_score:.3f})")
        
        logger.info(f"🎯 Created {len(mappings)} validated mappings")
        return mappings
    
    def _calculate_multi_signal_score(self, context: WidgetContext, canonical_key: str, patient_values: Dict) -> Dict[str, float]:
        """Calculate multi-signal score components"""
        
        # 1. Embedding similarity score
        embedding_score = self._calculate_embedding_similarity(context.full_context, canonical_key)
        
        # 2. Jaccard string similarity
        jaccard_score = self._calculate_jaccard_similarity(context.full_context, canonical_key)
        
        # 3. Proximity to section header
        proximity_score = self._calculate_proximity_score(context, canonical_key)
        
        # 4. Type compatibility
        type_compatibility = self._calculate_type_compatibility(canonical_key, patient_values.get(canonical_key))
        
        return {
            'embedding': embedding_score,
            'jaccard': jaccard_score,
            'proximity': proximity_score,
            'type_compatibility': type_compatibility
        }
    
    def _calculate_embedding_similarity(self, context: str, canonical_key: str) -> float:
        """Calculate embedding similarity using synonyms"""
        context_lower = context.lower()
        synonyms = self.canonical_synonyms.get(canonical_key, [canonical_key])
        
        best_score = 0
        for synonym in synonyms:
            if synonym in context_lower:
                # Longer matches get higher scores
                score = len(synonym) / max(len(context_lower), 1)
                best_score = max(best_score, score)
        
        return min(best_score * 2, 1.0)  # Boost and cap at 1.0
    
    def _calculate_jaccard_similarity(self, context: str, canonical_key: str) -> float:
        """Calculate Jaccard similarity between context and canonical key"""
        context_words = set(context.lower().split())
        key_words = set(canonical_key.split('_'))
        
        if not context_words or not key_words:
            return 0.0
        
        intersection = len(context_words & key_words)
        union = len(context_words | key_words)
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_proximity_score(self, context: WidgetContext, canonical_key: str) -> float:
        """Calculate proximity score based on section context"""
        if not context.section_header:
            return 0.5  # Neutral score if no section found
        
        section_header = context.section_header.lower()
        schema_section = CANONICAL_SCHEMA.get(canonical_key, {}).get('section', '')
        
        # Perfect section match
        if schema_section in section_header:
            return 1.0
        
        # Partial section match
        section_keywords = {
            'patient': ['patient', 'member', 'enrollee'],
            'insurance': ['insurance', 'coverage', 'plan', 'payer'],
            'provider': ['provider', 'physician', 'doctor', 'prescriber']
        }
        
        keywords = section_keywords.get(schema_section, [])
        for keyword in keywords:
            if keyword in section_header:
                return 0.8
        
        return 0.3  # Low score for wrong section
    
    def _calculate_type_compatibility(self, canonical_key: str, value: Any) -> float:
        """Calculate type compatibility score"""
        if not value:
            return 0.5  # Neutral if no value to check
        
        expected_type = CANONICAL_SCHEMA.get(canonical_key, {}).get('type')
        if not expected_type or expected_type not in TYPE_PATTERNS:
            return 0.8  # Neutral for unknown types
        
        pattern = TYPE_PATTERNS[expected_type]
        matches = bool(pattern.match(str(value)))
        
        return 1.0 if matches else 0.0
    
    def _extract_patient_values(self, patient_data: Dict) -> Dict[str, str]:
        """Extract patient values for type compatibility checking"""
        values = {}
        
        if 'tier_1_mandatory_fields' in patient_data:
            tier1 = patient_data['tier_1_mandatory_fields']
            
            # Patient demographics
            if 'patient_demographics' in tier1:
                demo = tier1['patient_demographics']
                
                full_name = demo.get('full_name', {}).get('value', '')
                if full_name:
                    name_parts = full_name.split()
                    values['patient_first_name'] = name_parts[0] if name_parts else ''
                    values['patient_last_name'] = name_parts[-1] if len(name_parts) > 1 else ''
                
                values['patient_dob'] = demo.get('date_of_birth', {}).get('value', '')
                
                address = demo.get('address', {}).get('value', '')
                if address:
                    values['patient_address'] = address
                    # Parse address components
                    addr_match = re.match(r'(.+),\s*(.+),\s*([A-Z]{2})[- ]?(\d{5})', address)
                    if addr_match:
                        values['patient_city'] = addr_match.group(2).strip()
                        values['patient_state'] = addr_match.group(3).strip()
                        values['patient_zip'] = addr_match.group(4).strip()
                
                phones = demo.get('phone_numbers', {}).get('value', [])
                if phones:
                    values['patient_phone'] = phones[0]
            
            # Insurance
            if 'insurance_information' in tier1:
                ins = tier1['insurance_information'].get('primary_insurance', {})
                values['insurance_member_id'] = ins.get('member_id', {}).get('value', '')
            
            # Provider
            if 'prescriber_information' in tier1:
                prov = tier1['prescriber_information']
                values['provider_name'] = prov.get('ordering_physician', {}).get('value', '')
                values['provider_npi'] = prov.get('npi', {}).get('value', '')
        
        return {k: v for k, v in values.items() if v and str(v).strip()}

def type_ok(canonical_key: str, value: Any) -> bool:
    """Type validation guard"""
    expected_type = CANONICAL_SCHEMA.get(canonical_key, {}).get("type")
    if not expected_type or expected_type not in TYPE_PATTERNS:
        return True  # Allow unknown types
    
    pattern = TYPE_PATTERNS[expected_type]
    return bool(pattern.match(str(value)))

def safe_assign(widget_id: str, canonical_key: str, value: Any, form_values: Dict) -> bool:
    """Safe assignment with type validation"""
    if not type_ok(canonical_key, value):
        logger.error(f"❌ TYPE MISMATCH: {canonical_key} → {widget_id} (value: {value})")
        return False
    
    form_values[widget_id] = str(value)
    logger.info(f"✅ SAFE ASSIGN: {widget_id} = {value}")
    return True

class ProductionGradeProcessor:
    """Production-grade processor with multi-signal mapping and hard guards"""
    
    def __init__(self):
        self.mapper = MultiSignalMapper()
    
    def process_form_production_grade(self, pdf_path: Path, patient_data: Dict, output_path: Path) -> Dict:
        """Process form with production-grade reliability"""
        logger.info(f"🚀 PRODUCTION-GRADE PROCESSING: {pdf_path}")
        
        try:
            # Get form fields
            reader = PdfReader(str(pdf_path))
            form_fields = reader.get_fields() or {}
            
            # Create validated mappings
            mappings = self.mapper.create_validated_mappings(pdf_path, form_fields, patient_data)
            
            if not mappings:
                return {'success': False, 'error': 'No validated mappings found'}
            
            # Extract patient values
            patient_values = self.mapper._extract_patient_values(patient_data)
            
            # Safe assignment with type guards
            form_values = {}
            assignment_results = []
            
            for mapping in mappings:
                value = patient_values.get(mapping.canonical_key)
                if value:
                    success = safe_assign(mapping.widget_id, mapping.canonical_key, value, form_values)
                    assignment_results.append({
                        'widget_id': mapping.widget_id,
                        'canonical_key': mapping.canonical_key,
                        'value': value,
                        'success': success,
                        'confidence': mapping.confidence
                    })
            
            # Fill PDF
            if form_values:
                filled_count = self._fill_pdf_safely(pdf_path, form_values, output_path)
                
                successful_assignments = [r for r in assignment_results if r['success']]
                
                return {
                    'success': True,
                    'total_mappings': len(mappings),
                    'safe_assignments': len(successful_assignments),
                    'filled_count': filled_count,
                    'output_path': str(output_path),
                    'assignment_details': successful_assignments,
                    'error_rate': (len(assignment_results) - len(successful_assignments)) / len(assignment_results) if assignment_results else 0
                }
            else:
                return {'success': False, 'error': 'No safe assignments possible'}
                
        except Exception as e:
            logger.error(f"❌ Production-grade processing failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _fill_pdf_safely(self, pdf_path: Path, form_values: Dict, output_path: Path) -> int:
        """Fill PDF with additional safety checks"""
        reader = PdfReader(str(pdf_path))
        writer = PdfWriter()
        
        # Copy pages
        for page in reader.pages:
            writer.add_page(page)
        
        # Safe form filling
        logger.info("✍️ PRODUCTION-GRADE FILLING:")
        for widget_id, value in form_values.items():
            logger.info(f"  🔒 {widget_id:15s} = {value}")
        
        try:
            for page in writer.pages:
                writer.update_page_form_field_values(page, form_values)
        except Exception as e:
            logger.error(f"❌ PDF filling error: {e}")
            raise
        
        # Save
        with open(output_path, 'wb') as f:
            writer.write(f)
        
        return len(form_values)

def test_production_grade_processor():
    """Test production-grade processor"""
    print("🚀 TESTING PRODUCTION-GRADE FIELD MAPPER")
    print("="*70)
    print("✅ Multi-signal scoring (embedding + Jaccard + proximity + type)")
    print("✅ Hierarchical context analysis")
    print("✅ Hard type validation guards")
    print("✅ Safe assignment with error rate tracking")
    print("-"*70)
    
    processor = ProductionGradeProcessor()
    
    # Test Abdullah
    data_path = "Input Data/Extracted_ground_truth/abdullah_structured.json"
    pdf_path = Path("Input Data/Adbulla/PA.pdf")
    output_path = Path("abdullah_PRODUCTION_GRADE.pdf")
    
    with open(data_path, 'r') as f:
        patient_data = json.load(f)
    
    result = processor.process_form_production_grade(pdf_path, patient_data, output_path)
    
    print(f"\n🎯 PRODUCTION-GRADE RESULTS:")
    print(f"Success: {result['success']}")
    
    if result['success']:
        print(f"Total Mappings: {result['total_mappings']}")
        print(f"Safe Assignments: {result['safe_assignments']}")
        print(f"Filled Count: {result['filled_count']}")
        print(f"Error Rate: {result['error_rate']:.1%}")
        print(f"Output: {result['output_path']}")
        
        print(f"\n📋 SAFE ASSIGNMENTS:")
        for assignment in result['assignment_details']:
            print(f"  {assignment['widget_id']:15s} → {assignment['canonical_key']:20s} = {assignment['value']} (conf: {assignment['confidence']:.3f})")
    else:
        print(f"Error: {result['error']}")
    
    return result

if __name__ == "__main__":
    test_production_grade_processor()