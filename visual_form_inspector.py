"""
VISUAL FORM INSPECTOR
Actually look at the PDF visually to understand field layout
"""

import fitz
from pathlib import Path

def visual_inspect_form(pdf_path: Path, output_image: str):
    """Extract visual layout of form to understand field positions"""
    print(f"🔍 VISUAL INSPECTION: {pdf_path}")
    
    doc = fitz.open(str(pdf_path))
    page = doc[0]
    
    # Get page as image
    mat = fitz.Matrix(2, 2)  # 2x zoom
    pix = page.get_pixmap(matrix=mat)
    pix.save(output_image)
    
    # Get all text with exact positions
    text_dict = page.get_text("dict")
    
    print("\n📋 TEXT LAYOUT ANALYSIS:")
    print("-" * 50)
    
    # Extract all text blocks
    all_text = []
    for block in text_dict.get("blocks", []):
        if "lines" in block:
            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"].strip()
                    if text and len(text) > 1:
                        bbox = span["bbox"]
                        all_text.append({
                            "text": text,
                            "x": bbox[0], 
                            "y": bbox[1],
                            "bbox": bbox
                        })
    
    # Sort by y position (top to bottom)
    all_text.sort(key=lambda x: x["y"])
    
    # Look for patient information sections
    patient_keywords = [
        "patient", "member", "first", "last", "name", "birth", "dob", 
        "address", "city", "state", "zip", "phone", "id"
    ]
    
    print("Key text elements (potential field labels):")
    for i, text_item in enumerate(all_text):
        text_lower = text_item["text"].lower()
        if any(keyword in text_lower for keyword in patient_keywords):
            x, y = text_item["x"], text_item["y"]
            print(f"  {text_item['text']:40s} at ({x:4.0f}, {y:4.0f})")
            
            # Look for nearby text that might be field values
            nearby = []
            for other in all_text:
                if (abs(other["x"] - x) < 200 and abs(other["y"] - y) < 30 and 
                    other["text"] != text_item["text"]):
                    nearby.append(other["text"])
            
            if nearby:
                print(f"    → nearby: {', '.join(nearby[:3])}")
    
    # Get form fields and try to correlate with text
    from PyPDF2 import PdfReader
    reader = PdfReader(str(pdf_path))
    form_fields = reader.get_fields() or {}
    
    print(f"\n📊 FORM FIELDS vs TEXT CORRELATION:")
    print("-" * 50)
    
    # Show some key fields that might be for patient info
    key_fields = ["T11", "T14", "T15", "T16", "T17", "T19", "T20", "T21"]
    
    for field_id in key_fields:
        if field_id in form_fields:
            field_obj = form_fields[field_id]
            rect = field_obj.get("/Rect", [0, 0, 0, 0])
            print(f"Field {field_id:10s}: rect={rect}")
            
            # Try to find text near this field position
            if rect and rect != [0, 0, 0, 0]:
                field_x, field_y = rect[0], rect[1]
                nearby_labels = []
                for text_item in all_text:
                    if (abs(text_item["x"] - field_x) < 100 and 
                        abs(text_item["y"] - field_y) < 50):
                        nearby_labels.append(text_item["text"])
                
                if nearby_labels:
                    print(f"  → near text: {', '.join(nearby_labels[:3])}")
    
    doc.close()
    print(f"\n📷 Visual layout saved to: {output_image}")

if __name__ == "__main__":
    print("🚀 VISUAL FORM ANALYSIS")
    print("=" * 60)
    
    # Analyze Abdullah's form
    visual_inspect_form(Path("Input Data/Adbulla/PA.pdf"), "abdullah_form_layout.png")
    
    print("\n" + "=" * 60)
    
    # Analyze Akshay's form
    visual_inspect_form(Path("Input Data/Akshay/pa.pdf"), "akshay_form_layout.png")