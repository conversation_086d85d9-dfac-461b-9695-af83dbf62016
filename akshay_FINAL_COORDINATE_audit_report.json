{"timestamp": "2025-06-13T12:47:51.764774", "input_form": "Input Data/Akshay/pa.pdf", "output_form": "akshay_FINAL_COORDINATE.pdf", "form_type": "aetna_skyrizi", "filled_fields_count": 14, "filled_fields": [{"field": "insurance_member_id", "value": "W123456789", "position": [120, 180]}, {"field": "insurance_group", "value": "GRP98765", "position": [350, 180]}, {"field": "patient_first_name", "value": "<PERSON><PERSON><PERSON>", "position": [120, 220]}, {"field": "patient_last_name", "value": "<PERSON><PERSON><PERSON>", "position": [320, 220]}, {"field": "patient_dob", "value": "1987-02-17", "position": [120, 250]}, {"field": "patient_phone", "value": "************", "position": [350, 250]}, {"field": "patient_address", "value": "1460 El Camino Real", "position": [120, 280]}, {"field": "patient_city", "value": "Arlington", "position": [120, 310]}, {"field": "patient_state", "value": "VA", "position": [320, 310]}, {"field": "patient_zip", "value": "22407", "position": [420, 310]}, {"field": "provider_name", "value": "<PERSON>, MD", "position": [120, 380]}, {"field": "provider_npi", "value": "**********", "position": [120, 410]}, {"field": "provider_phone", "value": "************", "position": [120, 440]}, {"field": "provider_fax", "value": "************", "position": [350, 440]}], "errors_count": 0, "errors": [], "available_patient_data": ["patient_first_name", "patient_last_name", "patient_dob", "patient_address", "patient_city", "patient_state", "patient_zip", "patient_phone", "insurance_member_id", "insurance_group", "provider_name", "provider_npi", "provider_phone", "provider_fax"], "success_rate": "100.0%"}