# embedding_matcher.py
import json
import re
import functools
import os
import logging
from typing import List, Tuple, Optional
import math

logger = logging.getLogger(__name__)

# Simple embedding simulation using word overlap until we install sentence-transformers
try:
    import faiss
    import numpy as np
    from sentence_transformers import SentenceTransformer
    MODEL_BI = SentenceTransformer('all-MiniLM-L6-v2')  # 384-D, 7 ms/label
    USE_EMBEDDINGS = True
except ImportError:
    logger.warning("Sentence transformers not available, using fallback similarity")
    USE_EMBEDDINGS = False

# Type validation patterns
TYPE_RE = {
    'dob': re.compile(r'\d{2}/\d{2}/\d{4}|\d{4}-\d{2}-\d{2}|\d{1,2}/\d{1,2}/\d{4}'),
    'phone': re.compile(r'\d{3}[- ]\d{3}[- ]\d{4}|\(\d{3}\)\s?\d{3}[- ]\d{4}'),
    'zip': re.compile(r'\d{5}(-\d{4})?'),
    'state': re.compile(r'^[A-Z]{2}$'),
    'npi': re.compile(r'^\d{10}$'),
    'name': re.compile(r'^[A-Za-z\s\'-\.]{1,50}$')
}

@functools.lru_cache(maxsize=None)
def load_canonical():
    """Load canonical aliases and build similarity index"""
    try:
        with open('canonical_aliases.json', 'r') as f:
            aliases = json.load(f)
    except FileNotFoundError:
        logger.error("canonical_aliases.json not found")
        return [], None, None
    
    canon = list(aliases.keys())
    
    if USE_EMBEDDINGS:
        # Create rich text representations for embedding
        texts = []
        for k in canon:
            base_text = k.replace('_', ' ')
            alias_text = ' ; '.join(aliases[k])
            full_text = f"{base_text} ; {alias_text}"
            texts.append(full_text)
        
        logger.info(f"Building embeddings for {len(canon)} canonical fields...")
        emb = MODEL_BI.encode(texts, normalize_embeddings=True)
        
        # Build FAISS index for fast similarity search
        index = faiss.IndexFlatIP(emb.shape[1])
        index.add(emb.astype('float32'))
        
        logger.info(f"FAISS index built with {index.ntotal} vectors")
        return canon, index, emb
    else:
        # Fallback: use text-based similarity
        logger.info(f"Using text-based similarity for {len(canon)} canonical fields")
        return canon, aliases, None

def type_compatible(canonical_key: str, sample_value: str) -> float:
    """Check type compatibility between canonical field and sample value"""
    if not sample_value:
        return 0.5  # Neutral if no value
    
    # Map canonical keys to types
    type_mapping = {
        'patient_dob': 'dob',
        'patient_phone': 'phone', 
        'provider_phone': 'phone',
        'provider_fax': 'phone',
        'patient_zip': 'zip',
        'patient_state': 'state',
        'provider_npi': 'npi',
        'patient_first_name': 'name',
        'patient_last_name': 'name',
        'provider_first_name': 'name',
        'provider_last_name': 'name'
    }
    
    expected_type = type_mapping.get(canonical_key)
    if not expected_type or expected_type not in TYPE_RE:
        return 0.8  # Neutral for unknown types
    
    pattern = TYPE_RE[expected_type]
    matches = bool(pattern.match(str(sample_value).strip()))
    return 1.0 if matches else 0.2

def text_similarity(text1: str, text2: str) -> float:
    """Calculate text similarity using word overlap (fallback method)"""
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    if not words1 or not words2:
        return 0.0
    
    intersection = len(words1 & words2)
    union = len(words1 | words2)
    
    return intersection / union if union > 0 else 0.0

def match(label_text: str, sample_value: str = '', k: int = 5) -> List[Tuple[str, float]]:
    """
    Match a form field label to canonical field using embeddings or text similarity
    
    Args:
        label_text: The form field label/context text
        sample_value: Optional sample value for type validation
        k: Number of top candidates to return
    
    Returns:
        List of (canonical_key, confidence_score) tuples
    """
    canon, index_or_aliases, _ = load_canonical()
    
    if not canon:
        logger.error("Failed to load canonical mappings")
        return []
    
    # Clean and prepare query text
    query_text = label_text.lower().strip()
    
    candidates = []
    
    if USE_EMBEDDINGS and index_or_aliases is not None:
        # Use embedding-based matching
        query_embedding = MODEL_BI.encode([query_text], normalize_embeddings=True)
        similarities, indices = index_or_aliases.search(query_embedding.astype('float32'), k)
        
        for j, i in enumerate(indices[0]):
            if i < len(canon):  # Valid index
                canonical_key = canon[i]
                embedding_score = float(similarities[0][j])
                
                # Type compatibility bonus
                type_score = type_compatible(canonical_key, sample_value)
                
                # Combined confidence score
                confidence = (0.8 * embedding_score) + (0.2 * type_score)
                
                candidates.append((canonical_key, confidence))
    else:
        # Use text-based similarity fallback
        aliases = index_or_aliases
        
        for canonical_key in canon:
            # Calculate similarity with canonical key and aliases
            key_text = canonical_key.replace('_', ' ')
            alias_texts = aliases.get(canonical_key, [])
            
            # Best similarity among key and aliases
            similarities = [text_similarity(query_text, key_text)]
            similarities.extend([text_similarity(query_text, alias) for alias in alias_texts])
            
            text_score = max(similarities)
            
            # Type compatibility bonus
            type_score = type_compatible(canonical_key, sample_value)
            
            # Combined confidence score
            confidence = (0.7 * text_score) + (0.3 * type_score)
            
            candidates.append((canonical_key, confidence))
    
    # Sort by confidence and return top k
    candidates.sort(key=lambda x: x[1], reverse=True)
    candidates = candidates[:k]
    
    logger.debug(f"Label '{label_text}' -> Top match: {candidates[0] if candidates else 'None'}")
    return candidates

def batch_match(field_contexts: dict, patient_values: dict = None, confidence_threshold: float = 0.3) -> dict:
    """
    Batch match multiple form fields to canonical fields
    
    Args:
        field_contexts: Dict of {widget_id: context_text}
        patient_values: Optional patient data for type validation
        confidence_threshold: Minimum confidence to accept a match
    
    Returns:
        Dict of {widget_id: canonical_key} mappings
    """
    mappings = {}
    used_canonical = set()
    
    # Get all candidates for all fields
    all_candidates = []
    for widget_id, context in field_contexts.items():
        sample_value = ""
        if patient_values:
            # Try to find relevant sample value for type checking
            for canonical_key, value in patient_values.items():
                if any(word in context.lower() for word in canonical_key.split('_')):
                    sample_value = str(value)
                    break
        
        candidates = match(context, sample_value, k=3)
        for canonical_key, confidence in candidates:
            all_candidates.append((confidence, widget_id, canonical_key, context))
    
    # Sort all candidates by confidence and assign greedily
    all_candidates.sort(key=lambda x: x[0], reverse=True)
    
    for confidence, widget_id, canonical_key, context in all_candidates:
        if (confidence >= confidence_threshold and 
            widget_id not in mappings and 
            canonical_key not in used_canonical):
            
            mappings[widget_id] = canonical_key
            used_canonical.add(canonical_key)
            
            logger.info(f"✅ EMBEDDING MATCH: {widget_id:15s} → {canonical_key:20s} (conf: {confidence:.3f})")
    
    logger.info(f"🎯 Created {len(mappings)} embedding-based mappings")
    return mappings

def save_mapping_cache(pdf_hash: str, mappings: dict, cache_dir: str = "embedding_cache"):
    """Save successful mappings for future use"""
    import os
    os.makedirs(cache_dir, exist_ok=True)
    
    cache_file = os.path.join(cache_dir, f"mappings_{pdf_hash}.json")
    with open(cache_file, 'w') as f:
        json.dump(mappings, f, indent=2)
    
    logger.info(f"💾 Cached mappings to {cache_file}")

def load_mapping_cache(pdf_hash: str, cache_dir: str = "embedding_cache") -> Optional[dict]:
    """Load cached mappings if available"""
    import os
    cache_file = os.path.join(cache_dir, f"mappings_{pdf_hash}.json")
    
    if os.path.exists(cache_file):
        with open(cache_file, 'r') as f:
            mappings = json.load(f)
        logger.info(f"📁 Loaded cached mappings from {cache_file}")
        return mappings
    
    return None

# Test function
def test_embedding_matcher():
    """Test the embedding matcher with sample field labels"""
    print("🧪 TESTING EMBEDDING MATCHER")
    print("=" * 50)
    
    test_labels = [
        "First Name",
        "Last Name", 
        "Date of Birth",
        "Home Address",
        "City",
        "State",
        "ZIP Code",
        "Phone Number",
        "Member ID",
        "Provider NPI"
    ]
    
    for label in test_labels:
        matches = match(label, k=3)
        print(f"'{label}' -> {matches[0][0]} (conf: {matches[0][1]:.3f})")
    
    print("\n🎯 Embedding matcher test complete!")

if __name__ == "__main__":
    test_embedding_matcher()