"""
SCHEMA HARVESTER
================

Analyzes any blank PA form PDF and creates a schema (map) of all fillable fields.
This is the foundation for handling "unseen forms" - the core requirement.

Usage:
    python src/schema_harvester.py --pdf "Input Data/Patient A/PA.pdf" --output "schemas/patient_a_schema.yaml"
"""

import argparse
import re
import unicodedata
import logging
from pathlib import Path
from typing import Dict, Any

import yaml
from PyPDF2 import PdfReader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_slug(text: str) -> str:
    """Creates a clean, URL-friendly slug from a string."""
    if not text:
        return "unknown_field"
    
    # Normalize unicode and convert to ASCII
    text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('ascii')
    # Remove non-alphanumeric characters except spaces and hyphens
    text = re.sub(r'[^\w\s-]', '', text).strip().lower()
    # Replace spaces and hyphens with underscores
    text = re.sub(r'[-\s]+', '_', text)
    # Remove leading/trailing underscores
    text = text.strip('_')
    
    return text if text else "unknown_field"


def extract_field_info(field_name: str, field_obj: Any) -> Dict[str, Any]:
    """Extract detailed information about a PDF form field."""
    
    try:
        # Get the field object
        obj = field_obj.get_object()
        
        # Extract field properties
        field_type = obj.get('/FT', '')
        field_flags = obj.get('/Ff', 0)
        field_value = obj.get('/V', '')
        field_tooltip = obj.get('/TU', '')
        field_default = obj.get('/DV', '')
        
        # Determine human-readable field type
        if field_type == '/Tx':
            human_type = "text"
        elif field_type == '/Btn':
            human_type = "checkbox" if (field_flags & 32768) else "button"
        elif field_type == '/Ch':
            human_type = "choice"
        else:
            human_type = "unknown"
        
        # Extract human-readable name (try multiple sources)
        human_name = field_tooltip or field_name or "Unknown Field"
        
        # Clean up the human name
        if human_name.startswith('/'):
            human_name = human_name[1:]
        
        return {
            "acro_id": field_name,
            "human_name": human_name,
            "type": human_type,
            "tooltip": field_tooltip,
            "default_value": str(field_default) if field_default else "",
            "current_value": str(field_value) if field_value else "",
            "flags": field_flags,
            "pdf_type": str(field_type),
            "is_readonly": bool(field_flags & 1),
            "is_required": bool(field_flags & 2)
        }
        
    except Exception as e:
        logger.warning(f"Error extracting info for field {field_name}: {e}")
        return {
            "acro_id": field_name,
            "human_name": field_name,
            "type": "unknown",
            "tooltip": "",
            "default_value": "",
            "current_value": "",
            "flags": 0,
            "pdf_type": "unknown",
            "is_readonly": False,
            "is_required": False
        }


def harvest_schema(pdf_path: Path) -> Dict[str, Any]:
    """
    Analyzes a PDF form and extracts a comprehensive schema of its fillable fields.

    Args:
        pdf_path: The path to the blank PA form PDF.

    Returns:
        A dictionary representing the form's schema.
    """
    logger.info(f"🔍 Harvesting schema from '{pdf_path}'...")
    
    try:
        reader = PdfReader(pdf_path)
        fields = reader.get_fields()

        if not fields:
            logger.warning("❌ No AcroForm widgets found in this PDF. It may be a flat, non-interactive form.")
            return {
                "metadata": {
                    "pdf_path": str(pdf_path),
                    "total_fields": 0,
                    "has_acroform": False,
                    "error": "No fillable fields found"
                },
                "fields": {}
            }

        schema_fields = {}
        field_stats = {
            "text": 0,
            "checkbox": 0,
            "button": 0,
            "choice": 0,
            "unknown": 0
        }

        for field_name, field_obj in fields.items():
            # Extract detailed field information
            field_info = extract_field_info(field_name, field_obj)
            
            # Generate a clean slug for the field
            human_name = field_info["human_name"]
            slug = generate_slug(human_name)
            
            # Handle duplicate slugs
            original_slug = slug
            counter = 1
            while slug in schema_fields:
                slug = f"{original_slug}_{counter}"
                counter += 1
            
            # Store field information
            schema_fields[slug] = field_info
            
            # Update statistics
            field_type = field_info["type"]
            if field_type in field_stats:
                field_stats[field_type] += 1
            else:
                field_stats["unknown"] += 1
            
            logger.debug(f"  📋 {slug} -> {field_name} ({field_type})")

        # Create comprehensive schema
        schema = {
            "metadata": {
                "pdf_path": str(pdf_path),
                "pdf_name": pdf_path.name,
                "total_fields": len(schema_fields),
                "has_acroform": True,
                "field_statistics": field_stats,
                "harvest_timestamp": "2025-01-01T00:00:00"  # Will be updated by caller
            },
            "fields": schema_fields
        }
        
        logger.info(f"✅ Successfully discovered {len(schema_fields)} fields:")
        for field_type, count in field_stats.items():
            if count > 0:
                logger.info(f"   {field_type}: {count}")
        
        return schema

    except Exception as e:
        logger.error(f"❌ Failed to harvest schema from '{pdf_path}': {e}")
        return {
            "metadata": {
                "pdf_path": str(pdf_path),
                "total_fields": 0,
                "has_acroform": False,
                "error": str(e)
            },
            "fields": {}
        }


def save_schema(schema: Dict[str, Any], output_path: Path) -> bool:
    """Save schema to YAML file with proper formatting."""
    
    try:
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Update timestamp
        from datetime import datetime
        schema["metadata"]["harvest_timestamp"] = datetime.now().isoformat()
        
        # Save to YAML with clean formatting
        with open(output_path, 'w') as f:
            yaml.dump(schema, f, indent=2, sort_keys=False, default_flow_style=False)
        
        logger.info(f"💾 Schema successfully saved to: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to save schema to '{output_path}': {e}")
        return False


def main():
    """Main CLI interface for schema harvesting."""
    
    parser = argparse.ArgumentParser(
        description="Harvest a schema from a fillable PDF form.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python src/schema_harvester.py --pdf "Input Data/Patient A/PA.pdf" --output "schemas/patient_a_schema.yaml"
    python src/schema_harvester.py --pdf forms/aetna_form.pdf --output schemas/aetna_schema.yaml
        """
    )
    
    parser.add_argument(
        "--pdf", 
        type=Path, 
        required=True, 
        help="Path to the blank PA form PDF."
    )
    
    parser.add_argument(
        "--output", 
        type=Path, 
        required=True, 
        help="Path to save the generated YAML schema file."
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose logging."
    )
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate input file
    if not args.pdf.exists():
        print(f"❌ Error: PDF file not found: {args.pdf}")
        return 1
    
    print(f"🔍 SCHEMA HARVESTER")
    print(f"=" * 50)
    print(f"Input PDF: {args.pdf}")
    print(f"Output Schema: {args.output}")
    print(f"=" * 50)
    
    # Harvest schema
    schema_data = harvest_schema(args.pdf)
    
    if not schema_data.get("fields"):
        print(f"❌ No fields found in PDF. Cannot create schema.")
        return 1
    
    # Save schema
    if save_schema(schema_data, args.output):
        print(f"✅ Schema harvesting complete!")
        print(f"📊 Discovered {schema_data['metadata']['total_fields']} fillable fields")
        print(f"💾 Schema saved to: {args.output}")
        return 0
    else:
        print(f"❌ Failed to save schema.")
        return 1


if __name__ == "__main__":
    exit(main())
