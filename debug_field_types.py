"""
Debug field types to fix detection logic
"""

from pathlib import Path
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>

def debug_field_types(pdf_path: Path):
    """Debug actual field types in PDF"""
    print(f"🔍 DEBUGGING FIELD TYPES: {pdf_path}")
    print("="*60)
    
    reader = PdfReader(str(pdf_path))
    form_fields = reader.get_fields() or {}
    
    # Analyze field types
    type_counts = {}
    text_fields = []
    
    for field_name, field_obj in form_fields.items():
        field_type = field_obj.get("/FT", "unknown")
        field_type_str = str(field_type)
        
        # Count types
        if field_type_str not in type_counts:
            type_counts[field_type_str] = 0
        type_counts[field_type_str] += 1
        
        # Check if it's a text field
        if "/Tx" in field_type_str or "text" in field_type_str.lower():
            rect = field_obj.get("/Rect", [])
            text_fields.append({
                'name': field_name,
                'type': field_type_str,
                'rect': rect
            })
    
    print(f"📊 FIELD TYPE DISTRIBUTION:")
    for field_type, count in sorted(type_counts.items()):
        print(f"  {field_type:20s}: {count:3d} fields")
    
    print(f"\n📝 TEXT FIELDS FOUND:")
    print(f"Total text fields: {len(text_fields)}")
    
    if text_fields:
        print("First 10 text fields:")
        for i, field in enumerate(text_fields[:10]):
            print(f"  {i+1:2d}. {field['name']:15s} | Type: {field['type']:10s} | Rect: {field['rect']}")
        
        # Check for empty rects
        empty_rects = [f for f in text_fields if not f['rect'] or f['rect'] == [0, 0, 0, 0]]
        print(f"\n⚠️  Fields with empty/zero rects: {len(empty_rects)}/{len(text_fields)}")
        
        if empty_rects:
            print("Examples of empty rect fields:")
            for i, field in enumerate(empty_rects[:5]):
                print(f"  {field['name']:15s} | Rect: {field['rect']}")

def test_all_pdfs():
    """Test all PDFs to understand field types"""
    pdfs = [
        ("Abdullah", "Input Data/Adbulla/PA.pdf"),
        ("Akshay", "Input Data/Akshay/pa.pdf"),
        ("Amy", "Input Data/Amy/PA.pdf")
    ]
    
    for name, pdf_path in pdfs:
        print(f"\n👤 {name.upper()}")
        print("-" * 40)
        if Path(pdf_path).exists():
            debug_field_types(Path(pdf_path))
        else:
            print(f"❌ File not found: {pdf_path}")

if __name__ == "__main__":
    test_all_pdfs()