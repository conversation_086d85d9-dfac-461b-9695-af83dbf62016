"""
COORDINATE-BASED FORM FILLER
Actually works by placing text at specific x,y coordinates
"""

import fitz
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CoordinateBasedFiller:
    """Fill forms using exact coordinate placement"""
    
    def __init__(self):
        # These will be determined by visual analysis
        self.coordinate_mappings = {}
        
    def analyze_form_layout(self, pdf_path: Path) -> Dict:
        """Analyze form layout to understand structure"""
        logger.info(f"🔍 ANALYZING FORM LAYOUT: {pdf_path}")
        
        doc = fitz.open(str(pdf_path))
        page = doc[0]
        
        # Extract all text with positions
        text_dict = page.get_text("dict")
        text_elements = []
        
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            text_elements.append({
                                "text": text,
                                "x": bbox[0],
                                "y": bbox[1],
                                "width": bbox[2] - bbox[0],
                                "height": bbox[3] - bbox[1],
                                "bbox": bbox
                            })
        
        # Sort by position (top to bottom, left to right)
        text_elements.sort(key=lambda x: (x["y"], x["x"]))
        
        # Look for field labels and nearby spaces
        field_indicators = [
            "first name", "last name", "name", "date of birth", "dob", "birth",
            "address", "street", "city", "state", "zip", "postal", "phone", 
            "telephone", "member", "id", "subscriber", "policy", "group",
            "provider", "physician", "doctor", "npi", "prescriber"
        ]
        
        potential_fields = []
        
        for element in text_elements:
            text_lower = element["text"].lower()
            
            # Check if this looks like a field label
            for indicator in field_indicators:
                if indicator in text_lower:
                    # Look for nearby blank spaces (potential fill areas)
                    fill_areas = self._find_nearby_fill_areas(element, text_elements)
                    
                    potential_fields.append({
                        "label": element["text"],
                        "label_pos": (element["x"], element["y"]),
                        "fill_areas": fill_areas,
                        "indicator": indicator
                    })
                    break
        
        doc.close()
        
        logger.info(f"📋 Found {len(potential_fields)} potential field areas")
        for field in potential_fields[:10]:  # Show first 10
            logger.info(f"  '{field['label']}' at ({field['label_pos'][0]:.0f}, {field['label_pos'][1]:.0f}) -> {len(field['fill_areas'])} fill areas")
        
        return {
            "potential_fields": potential_fields,
            "all_text": text_elements,
            "page_size": page.rect
        }
    
    def _find_nearby_fill_areas(self, label_element: Dict, all_text: List[Dict]) -> List[Dict]:
        """Find potential fill areas near a field label"""
        label_x, label_y = label_element["x"], label_element["y"]
        label_width = label_element["width"]
        
        fill_areas = []
        
        # Look to the right of the label (most common)
        right_x = label_x + label_width + 10
        
        # Check different positions to the right
        for offset_x in [50, 100, 150, 200, 250]:
            test_x = right_x + offset_x
            
            # Check if there's text already at this position
            occupied = False
            for text_elem in all_text:
                if (abs(text_elem["x"] - test_x) < 30 and 
                    abs(text_elem["y"] - label_y) < 15):
                    occupied = True
                    break
            
            if not occupied:
                fill_areas.append({
                    "x": test_x,
                    "y": label_y,
                    "type": "right_of_label"
                })
        
        # Look below the label  
        for offset_y in [15, 25, 35]:
            test_y = label_y + offset_y
            
            occupied = False
            for text_elem in all_text:
                if (abs(text_elem["x"] - label_x) < 50 and 
                    abs(text_elem["y"] - test_y) < 10):
                    occupied = True
                    break
            
            if not occupied:
                fill_areas.append({
                    "x": label_x,
                    "y": test_y,
                    "type": "below_label"
                })
        
        return fill_areas
    
    def create_coordinate_mapping(self, pdf_path: Path) -> Dict:
        """Create coordinate mapping for a specific form"""
        analysis = self.analyze_form_layout(pdf_path)
        
        # Create mapping based on field analysis
        mapping = {}
        
        for field in analysis["potential_fields"]:
            label = field["label"].lower()
            fill_areas = field["fill_areas"]
            
            if not fill_areas:
                continue
            
            # Use the first (closest) fill area
            fill_pos = fill_areas[0]
            
            # Map to canonical field based on label content
            canonical_field = self._map_label_to_canonical(label)
            
            if canonical_field:
                mapping[canonical_field] = {
                    "x": fill_pos["x"],
                    "y": fill_pos["y"],
                    "label": field["label"],
                    "confidence": self._calculate_confidence(label, canonical_field)
                }
        
        return mapping
    
    def _map_label_to_canonical(self, label: str) -> Optional[str]:
        """Map a field label to canonical field name"""
        label_lower = label.lower()
        
        if "first" in label_lower and "name" in label_lower:
            return "patient_first_name"
        elif "last" in label_lower and "name" in label_lower:
            return "patient_last_name"
        elif "date" in label_lower and ("birth" in label_lower or "dob" in label_lower):
            return "patient_dob"
        elif "address" in label_lower and "first" not in label_lower:
            return "patient_address"
        elif "city" in label_lower:
            return "patient_city"
        elif "state" in label_lower:
            return "patient_state"
        elif "zip" in label_lower or "postal" in label_lower:
            return "patient_zip"
        elif "phone" in label_lower or "telephone" in label_lower:
            return "patient_phone"
        elif "member" in label_lower and "id" in label_lower:
            return "insurance_member_id"
        elif "npi" in label_lower:
            return "provider_npi"
        elif "provider" in label_lower or "physician" in label_lower or "doctor" in label_lower:
            return "provider_name"
        
        return None
    
    def _calculate_confidence(self, label: str, canonical: str) -> float:
        """Calculate confidence in the mapping"""
        label_words = set(label.lower().split())
        canonical_words = set(canonical.split("_"))
        
        intersection = len(label_words & canonical_words)
        union = len(label_words | canonical_words)
        
        return intersection / union if union > 0 else 0.0
    
    def fill_form_with_coordinates(self, pdf_path: Path, patient_data: Dict, output_path: Path) -> Dict:
        """Fill form using coordinate-based text placement"""
        logger.info(f"🎯 COORDINATE-BASED FILLING: {pdf_path}")
        
        # Extract patient values
        patient_values = self._extract_patient_values(patient_data)
        
        # Create coordinate mapping for this form
        coord_mapping = self.create_coordinate_mapping(pdf_path)
        
        if not coord_mapping:
            return {"success": False, "error": "No coordinate mappings found"}
        
        logger.info(f"📍 Found {len(coord_mapping)} coordinate mappings")
        
        # Open PDF for modification
        doc = fitz.open(str(pdf_path))
        page = doc[0]
        
        filled_fields = []
        
        # Place text at coordinates
        for canonical_field, coord_info in coord_mapping.items():
            if canonical_field in patient_values:
                value = str(patient_values[canonical_field]).strip()
                if value:
                    x, y = coord_info["x"], coord_info["y"]
                    
                    # Insert text at coordinate
                    point = fitz.Point(x, y)
                    page.insert_text(
                        point,
                        value,
                        fontsize=10,
                        color=(0, 0, 0),  # Black
                        fontname="helv"   # Helvetica
                    )
                    
                    filled_fields.append({
                        "field": canonical_field,
                        "value": value,
                        "position": (x, y),
                        "label": coord_info["label"],
                        "confidence": coord_info["confidence"]
                    })
                    
                    logger.info(f"✅ PLACED: {canonical_field} = '{value}' at ({x:.0f}, {y:.0f})")
        
        # Save the modified PDF
        doc.save(str(output_path))
        doc.close()
        
        logger.info(f"📄 Saved filled form: {output_path}")
        
        return {
            "success": True,
            "method": "coordinate_placement",
            "filled_fields": len(filled_fields),
            "field_details": filled_fields,
            "output_path": str(output_path),
            "coordinate_mappings": len(coord_mapping)
        }
    
    def _extract_patient_values(self, patient_data: Dict) -> Dict[str, str]:
        """Extract patient values from structured data"""
        values = {}
        
        if 'tier_1_mandatory_fields' in patient_data:
            tier1 = patient_data['tier_1_mandatory_fields']
            
            # Patient demographics
            if 'patient_demographics' in tier1:
                demo = tier1['patient_demographics']
                
                # Names
                full_name = demo.get('full_name', {}).get('value', '')
                if full_name:
                    name_parts = full_name.split()
                    values['patient_first_name'] = name_parts[0] if name_parts else ''
                    values['patient_last_name'] = name_parts[-1] if len(name_parts) > 1 else ''
                
                # DOB
                values['patient_dob'] = demo.get('date_of_birth', {}).get('value', '')
                
                # Address parsing
                address = demo.get('address', {}).get('value', '')
                if address:
                    import re
                    addr_match = re.match(r'(.+),\s*(.+),\s*([A-Z]{2})[- ]?(\d{5})', address)
                    if addr_match:
                        values['patient_address'] = addr_match.group(1).strip()
                        values['patient_city'] = addr_match.group(2).strip()
                        values['patient_state'] = addr_match.group(3).strip()
                        values['patient_zip'] = addr_match.group(4).strip()
                    else:
                        values['patient_address'] = address
                
                # Phone
                phones = demo.get('phone_numbers', {}).get('value', [])
                if phones:
                    values['patient_phone'] = phones[0]
            
            # Insurance
            if 'insurance_information' in tier1:
                ins = tier1['insurance_information'].get('primary_insurance', {})
                values['insurance_member_id'] = ins.get('member_id', {}).get('value', '')
            
            # Provider
            if 'prescriber_information' in tier1:
                prov = tier1['prescriber_information']
                values['provider_name'] = prov.get('ordering_physician', {}).get('value', '')
                values['provider_npi'] = prov.get('npi', {}).get('value', '')
        
        # Clean values
        clean_values = {k: v for k, v in values.items() if v and str(v).strip()}
        
        logger.info(f"📋 Extracted {len(clean_values)} patient values")
        return clean_values
    
    def create_visual_comparison(self, original_path: Path, filled_path: Path, comparison_path: str):
        """Create side-by-side comparison of original vs filled form"""
        logger.info(f"📸 Creating visual comparison: {comparison_path}")
        
        # Open both PDFs
        orig_doc = fitz.open(str(original_path))
        filled_doc = fitz.open(str(filled_path))
        
        orig_page = orig_doc[0]
        filled_page = filled_doc[0]
        
        # Convert to images
        mat = fitz.Matrix(1.5, 1.5)  # 1.5x zoom
        orig_pix = orig_page.get_pixmap(matrix=mat)
        filled_pix = filled_page.get_pixmap(matrix=mat)
        
        # Save images
        orig_img_path = comparison_path.replace(".png", "_original.png")
        filled_img_path = comparison_path.replace(".png", "_filled.png")
        
        orig_pix.save(orig_img_path)
        filled_pix.save(filled_img_path)
        
        orig_doc.close()
        filled_doc.close()
        
        logger.info(f"📷 Saved comparison images: {orig_img_path}, {filled_img_path}")

def test_coordinate_based_system():
    """Test coordinate-based form filling"""
    print("🚀 COORDINATE-BASED FORM FILLING TEST")
    print("=" * 70)
    print("✅ Visual form analysis")
    print("✅ Coordinate-based text placement") 
    print("✅ Actual text insertion into PDF")
    print("✅ Visual validation of results")
    print("-" * 70)
    
    filler = CoordinateBasedFiller()
    
    patients = [
        ("Abdullah", "Input Data/Extracted_ground_truth/abdullah_structured.json", "Input Data/Adbulla/PA.pdf"),
        ("Akshay", "Input Data/Extracted_ground_truth/akshay_structured.json", "Input Data/Akshay/pa.pdf")
    ]
    
    results = []
    
    for patient_name, data_path, pdf_path in patients:
        print(f"\n👤 {patient_name.upper()}")
        print("-" * 40)
        
        if not Path(data_path).exists() or not Path(pdf_path).exists():
            print(f"❌ Files not found for {patient_name}")
            continue
        
        # Load patient data
        with open(data_path, 'r') as f:
            patient_data = json.load(f)
        
        # Fill form using coordinates
        output_path = Path(f"{patient_name.lower()}_COORDINATE_FILLED.pdf")
        result = filler.fill_form_with_coordinates(Path(pdf_path), patient_data, output_path)
        
        print(f"Success: {result['success']}")
        
        if result['success']:
            print(f"Method: {result['method']}")
            print(f"Coordinate Mappings: {result['coordinate_mappings']}")
            print(f"Filled Fields: {result['filled_fields']}")
            print(f"Output: {result['output_path']}")
            
            print(f"\n📍 Field Placements:")
            for field_detail in result['field_details']:
                x, y = field_detail['position']
                print(f"  {field_detail['field']:20s} = '{field_detail['value']}' at ({x:.0f}, {y:.0f})")
            
            # Create visual comparison
            comparison_path = f"{patient_name.lower()}_comparison.png"
            filler.create_visual_comparison(Path(pdf_path), output_path, comparison_path)
            
        else:
            print(f"Error: {result['error']}")
        
        results.append(result)
    
    # Summary
    successful = [r for r in results if r.get('success', False)]
    total_fields = sum(r.get('filled_fields', 0) for r in successful)
    
    print(f"\n🎯 COORDINATE-BASED RESULTS:")
    print(f"Patients Processed: {len(results)}")
    print(f"Success Rate: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.1f}%)")
    print(f"Total Fields Placed: {total_fields}")
    print(f"🎉 COORDINATE SYSTEM TESTED!")
    
    return results

if __name__ == "__main__":
    test_coordinate_based_system()