"""
STEP 3: DIRECT PA FORM FILLER
Uses structured data to fill PA forms directly based on form requirements
"""

import json
import fitz
from pathlib import Path
from typing import Dict, List, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DirectPAFiller:
    """Fill PA forms directly using structured patient data"""
    
    def __init__(self):
        # Enhanced field mapping based on PA form requirements
        self.field_mappers = {
            "medicare_rituxan_pa": self._get_medicare_field_mapping(),
            "aetna_skyrizi_pa": self._get_aetna_field_mapping()
        }
    
    def fill_pa_form_directly(self, pa_form_path: Path, structured_data: Dict, 
                             pa_requirements: Dict, output_path: Path) -> Dict:
        """Fill PA form using structured data and form requirements"""
        logger.info(f"🎯 DIRECT PA FILLING: {pa_form_path}")
        
        form_type = pa_requirements['form_type']
        logger.info(f"📋 Form Type: {form_type}")
        
        # Get field mapping for this form type
        if form_type not in self.field_mappers:
            return {
                "success": False,
                "error": f"No field mapping available for form type: {form_type}"
            }
        
        field_mapper = self.field_mappers[form_type]
        fill_coordinates = pa_requirements.get('fill_coordinates', {})
        
        # Extract values from structured data
        extracted_values = self._extract_from_structured_data(structured_data, field_mapper)
        
        # Fill the PA form
        result = self._fill_form_with_coordinates(
            pa_form_path, extracted_values, fill_coordinates, output_path
        )
        
        result.update({
            "form_type": form_type,
            "pa_form_path": str(pa_form_path),
            "structured_data_fields": len(self._get_all_structured_fields(structured_data)),
            "mapped_fields": len(extracted_values),
            "field_mapping": extracted_values
        })
        
        return result
    
    def _get_medicare_field_mapping(self) -> Dict:
        """Get field mapping for Medicare forms"""
        return {
            "insurance_member_id": [
                "tier_1_mandatory_fields.insurance_information.primary_insurance.member_id.value",
                "tier_1_mandatory_fields.insurance_information.primary_insurance.subscriber_id.value"
            ],
            "patient_first_name": [
                "tier_1_mandatory_fields.patient_demographics.full_name.value"  # Will split
            ],
            "patient_last_name": [
                "tier_1_mandatory_fields.patient_demographics.full_name.value"  # Will split
            ],
            "patient_dob": [
                "tier_1_mandatory_fields.patient_demographics.date_of_birth.value"
            ],
            "patient_address": [
                "tier_1_mandatory_fields.patient_demographics.address.value"  # Will split
            ],
            "patient_city": [
                "tier_1_mandatory_fields.patient_demographics.address.value"  # Will split
            ],
            "patient_state": [
                "tier_1_mandatory_fields.patient_demographics.address.value"  # Will split
            ],
            "patient_zip": [
                "tier_1_mandatory_fields.patient_demographics.address.value"  # Will split
            ],
            "patient_phone": [
                "tier_1_mandatory_fields.patient_demographics.phone_numbers.value"  # Will take first
            ],
            "provider_name": [
                "tier_1_mandatory_fields.prescriber_information.ordering_physician.value"
            ],
            "provider_npi": [
                "tier_1_mandatory_fields.prescriber_information.npi.value"
            ],
            "provider_phone": [
                "tier_1_mandatory_fields.prescriber_information.phone_fax.value.phone"
            ],
            "provider_fax": [
                "tier_1_mandatory_fields.prescriber_information.phone_fax.value.fax"
            ],
            "medication": [
                "tier_1_mandatory_fields.clinical_information.requested_medication.value",
                "tier_1_mandatory_fields.clinical_information.medication.value"
            ],
            "diagnosis": [
                "tier_1_mandatory_fields.clinical_information.diagnosis.value",
                "tier_1_mandatory_fields.clinical_information.primary_diagnosis.value"
            ]
        }
    
    def _get_aetna_field_mapping(self) -> Dict:
        """Get field mapping for Aetna forms"""
        return {
            "insurance_member_id": [
                "tier_1_mandatory_fields.insurance_information.primary_insurance.member_id.value"
            ],
            "insurance_group": [
                "tier_1_mandatory_fields.insurance_information.primary_insurance.group_number.value"
            ],
            "patient_first_name": [
                "tier_1_mandatory_fields.patient_demographics.full_name.value"
            ],
            "patient_last_name": [
                "tier_1_mandatory_fields.patient_demographics.full_name.value"
            ],
            "patient_dob": [
                "tier_1_mandatory_fields.patient_demographics.date_of_birth.value"
            ],
            "patient_phone": [
                "tier_1_mandatory_fields.patient_demographics.phone_numbers.value"
            ],
            "patient_address": [
                "tier_1_mandatory_fields.patient_demographics.address.value"
            ],
            "patient_city": [
                "tier_1_mandatory_fields.patient_demographics.address.value"
            ],
            "patient_state": [
                "tier_1_mandatory_fields.patient_demographics.address.value"
            ],
            "patient_zip": [
                "tier_1_mandatory_fields.patient_demographics.address.value"
            ],
            "provider_name": [
                "tier_1_mandatory_fields.prescriber_information.ordering_physician.value"
            ],
            "provider_npi": [
                "tier_1_mandatory_fields.prescriber_information.npi.value"
            ],
            "provider_phone": [
                "tier_1_mandatory_fields.prescriber_information.phone_fax.value.phone"
            ],
            "provider_fax": [
                "tier_1_mandatory_fields.prescriber_information.phone_fax.value.fax"
            ]
        }
    
    def _extract_from_structured_data(self, data: Dict, field_mapper: Dict) -> Dict:
        """Extract values from structured data using field mapping"""
        extracted = {}
        
        for pa_field, data_paths in field_mapper.items():
            value = None
            
            # Try each data path until we find a value
            for path in data_paths:
                value = self._get_nested_value(data, path)
                if value:
                    break
            
            if value:
                # Apply field-specific processing
                processed_value = self._process_field_value(pa_field, value)
                if processed_value:
                    extracted[pa_field] = processed_value
                    logger.info(f"  ✅ {pa_field}: {processed_value}")
                else:
                    logger.warning(f"  ⚠️ {pa_field}: Processing failed for '{value}'")
            else:
                logger.warning(f"  ❌ {pa_field}: Not found in structured data")
        
        return extracted
    
    def _get_nested_value(self, data: Dict, path: str) -> Optional[str]:
        """Get value from nested dictionary using dot notation"""
        try:
            keys = path.split('.')
            current = data
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            
            return str(current) if current is not None else None
            
        except Exception:
            return None
    
    def _process_field_value(self, field_name: str, value: str) -> Optional[str]:
        """Process field value based on field type"""
        if not value or not str(value).strip():
            return None
        
        value_str = str(value).strip()
        
        if field_name in ["patient_first_name", "patient_last_name"]:
            # Split full name
            if " " in value_str:
                name_parts = value_str.split()
                if field_name == "patient_first_name":
                    return name_parts[0]
                else:  # last_name
                    return name_parts[-1]
            else:
                return value_str if field_name == "patient_first_name" else ""
        
        elif field_name in ["patient_address", "patient_city", "patient_state", "patient_zip"]:
            # Parse address
            import re
            addr_match = re.match(r'(.+),\s*(.+),\s*([A-Z]{2})[- ]?(\d{5})', value_str)
            if addr_match:
                if field_name == "patient_address":
                    return addr_match.group(1).strip()
                elif field_name == "patient_city":
                    return addr_match.group(2).strip()
                elif field_name == "patient_state":
                    return addr_match.group(3).strip()
                elif field_name == "patient_zip":
                    return addr_match.group(4).strip()
            else:
                # If parsing fails, return full address for address field
                return value_str if field_name == "patient_address" else ""
        
        elif field_name == "patient_phone":
            # Handle phone list
            if isinstance(value, list):
                return value[0] if value else ""
            elif value_str.startswith('[') and value_str.endswith(']'):
                # Parse string representation of list
                try:
                    import ast
                    phone_list = ast.literal_eval(value_str)
                    return phone_list[0] if phone_list else ""
                except:
                    return value_str
            else:
                return value_str
        
        else:
            return value_str
    
    def _fill_form_with_coordinates(self, pa_form_path: Path, extracted_values: Dict, 
                                  coordinates: Dict, output_path: Path) -> Dict:
        """Fill PA form using coordinate-based placement"""
        
        if not coordinates:
            return {
                "success": False,
                "error": "No fill coordinates available for this form type"
            }
        
        # Open PDF
        doc = fitz.open(str(pa_form_path))
        page = doc[0]
        
        filled_count = 0
        filled_fields = []
        errors = []
        
        # Fill each field
        for field_name, value in extracted_values.items():
            if field_name in coordinates:
                x, y = coordinates[field_name]
                
                try:
                    # Insert text at coordinates
                    point = fitz.Point(x, y)
                    page.insert_text(
                        point,
                        str(value),
                        fontsize=10,
                        color=(0, 0, 0),  # Black
                        fontname="helv"
                    )
                    
                    filled_count += 1
                    filled_fields.append({
                        "field": field_name,
                        "value": str(value),
                        "position": (x, y)
                    })
                    
                    logger.info(f"  📍 FILLED: {field_name} = '{value}' at ({x:.0f}, {y:.0f})")
                    
                except Exception as e:
                    error_msg = f"Failed to fill {field_name}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"  ❌ {error_msg}")
            else:
                logger.warning(f"  ⚠️ No coordinates for {field_name}")
        
        # Save filled PDF
        doc.save(str(output_path))
        doc.close()
        
        # Create visual proof
        visual_path = f"{output_path.stem}_visual_proof.png"
        self._create_visual_proof(output_path, visual_path)
        
        return {
            "success": True,
            "filled_count": filled_count,
            "total_extractable": len(extracted_values),
            "total_coordinates": len(coordinates),
            "output_path": str(output_path),
            "visual_proof": visual_path,
            "filled_fields": filled_fields,
            "errors": errors
        }
    
    def _create_visual_proof(self, pdf_path: Path, image_path: str):
        """Create visual proof image"""
        doc = fitz.open(str(pdf_path))
        page = doc[0]
        
        mat = fitz.Matrix(2, 2)  # 2x zoom
        pix = page.get_pixmap(matrix=mat)
        pix.save(image_path)
        
        doc.close()
        logger.info(f"  📷 Visual proof: {image_path}")
    
    def _get_all_structured_fields(self, data: Dict) -> List[str]:
        """Get all available fields in structured data"""
        fields = []
        
        def extract_fields(obj, prefix=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{prefix}.{key}" if prefix else key
                    if isinstance(value, dict):
                        extract_fields(value, current_path)
                    else:
                        fields.append(current_path)
        
        extract_fields(data)
        return fields

def test_direct_pa_filling():
    """Test direct PA form filling"""
    print("🚀 STEP 3: DIRECT PA FORM FILLING")
    print("=" * 60)
    
    filler = DirectPAFiller()
    
    # Test cases with structured data
    test_cases = [
        {
            "name": "Abdullah",
            "pa_form": "../Input Data/Adbulla/PA.pdf",
            "structured_data": "../Input Data/Extracted_ground_truth/abdullah_structured.json",
            "pa_analysis": "analysis_abdullahs_medicare_form.json"
        },
        {
            "name": "Akshay",
            "pa_form": "../Input Data/Akshay/pa.pdf", 
            "structured_data": "../Input Data/Extracted_ground_truth/akshay_structured.json",
            "pa_analysis": "analysis_akshays_aetna_form.json"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n👤 {test_case['name'].upper()}")
        print("-" * 40)
        
        # Load structured data
        if not Path(test_case['structured_data']).exists():
            print(f"❌ Structured data not found: {test_case['structured_data']}")
            continue
            
        with open(test_case['structured_data'], 'r') as f:
            structured_data = json.load(f)
        
        # Load PA analysis
        if not Path(test_case['pa_analysis']).exists():
            print(f"❌ PA analysis not found: {test_case['pa_analysis']}")
            continue
            
        with open(test_case['pa_analysis'], 'r') as f:
            pa_requirements = json.load(f)
        
        # Override form type for better matching
        if test_case['name'] == "Abdullah":
            pa_requirements['form_type'] = "medicare_rituxan_pa"
        elif test_case['name'] == "Akshay":
            pa_requirements['form_type'] = "aetna_skyrizi_pa"
            # Add coordinates for Akshay's form
            pa_requirements['fill_coordinates'] = {
                "insurance_member_id": (120, 180),
                "insurance_group": (350, 180),
                "patient_first_name": (120, 220),
                "patient_last_name": (320, 220),
                "patient_dob": (120, 250),
                "patient_phone": (350, 250),
                "patient_address": (120, 280),
                "patient_city": (120, 310),
                "patient_state": (320, 310),
                "patient_zip": (420, 310),
                "provider_name": (120, 380),
                "provider_npi": (120, 410),
                "provider_phone": (120, 440),
                "provider_fax": (350, 440)
            }
        
        # Fill PA form
        output_path = Path(f"{test_case['name'].lower()}_PA_DRIVEN_FILLED.pdf")
        result = filler.fill_pa_form_directly(
            Path(test_case['pa_form']),
            structured_data,
            pa_requirements,
            output_path
        )
        
        results.append(result)
        
        if result['success']:
            print(f"✅ Success!")
            print(f"Form Type: {result['form_type']}")
            print(f"Filled: {result['filled_count']}/{result['total_coordinates']} fields")
            print(f"Available Data: {result['structured_data_fields']} fields")
            print(f"Mapped: {result['mapped_fields']} fields")
            print(f"Output: {result['output_path']}")
            print(f"Visual Proof: {result['visual_proof']}")
            
            if result['errors']:
                print(f"\n⚠️ Errors:")
                for error in result['errors']:
                    print(f"  - {error}")
            
        else:
            print(f"❌ Failed: {result['error']}")
        
        # Save detailed results
        result_file = f"direct_fill_result_{test_case['name'].lower()}.json"
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"\n💾 Results saved: {result_file}")
    
    # Summary
    successful = [r for r in results if r.get('success', False)]
    total_filled = sum(r.get('filled_count', 0) for r in successful)
    
    print(f"\n🎯 DIRECT PA FILLING COMPLETE!")
    print(f"Success Rate: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.0f}%)")
    print(f"Total Fields Filled: {total_filled}")
    print(f"✅ PA-DRIVEN APPROACH VALIDATED!")
    
    return results

if __name__ == "__main__":
    test_direct_pa_filling()