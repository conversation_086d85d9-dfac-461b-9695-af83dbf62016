{"success": true, "filled_count": 14, "total_extractable": 14, "total_coordinates": 14, "output_path": "akshay_PA_DRIVEN_FILLED.pdf", "visual_proof": "akshay_PA_DRIVEN_FILLED_visual_proof.png", "filled_fields": [{"field": "insurance_member_id", "value": "W123456789", "position": [120, 180]}, {"field": "insurance_group", "value": "GRP98765", "position": [350, 180]}, {"field": "patient_first_name", "value": "<PERSON><PERSON><PERSON>", "position": [120, 220]}, {"field": "patient_last_name", "value": "<PERSON><PERSON><PERSON>", "position": [320, 220]}, {"field": "patient_dob", "value": "1987-02-17", "position": [120, 250]}, {"field": "patient_phone", "value": "************", "position": [350, 250]}, {"field": "patient_address", "value": "1460 El Camino Real", "position": [120, 280]}, {"field": "patient_city", "value": "Arlington", "position": [120, 310]}, {"field": "patient_state", "value": "VA", "position": [320, 310]}, {"field": "patient_zip", "value": "22407", "position": [420, 310]}, {"field": "provider_name", "value": "<PERSON>, MD", "position": [120, 380]}, {"field": "provider_npi", "value": "**********", "position": [120, 410]}, {"field": "provider_phone", "value": "************", "position": [120, 440]}, {"field": "provider_fax", "value": "************", "position": [350, 440]}], "errors": [], "form_type": "aetna_skyrizi_pa", "pa_form_path": "../Input Data/Akshay/pa.pdf", "structured_data_fields": 86, "mapped_fields": 14, "field_mapping": {"insurance_member_id": "W123456789", "insurance_group": "GRP98765", "patient_first_name": "<PERSON><PERSON><PERSON>", "patient_last_name": "<PERSON><PERSON><PERSON>", "patient_dob": "1987-02-17", "patient_phone": "************", "patient_address": "1460 El Camino Real", "patient_city": "Arlington", "patient_state": "VA", "patient_zip": "22407", "provider_name": "<PERSON>, MD", "provider_npi": "**********", "provider_phone": "************", "provider_fax": "************"}}