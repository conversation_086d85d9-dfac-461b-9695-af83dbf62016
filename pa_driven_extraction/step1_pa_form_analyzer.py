"""
STEP 1: PA FORM ANALYZER
Analyzes PA forms to identify what fields need to be filled
"""

import fitz
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PAFormAnalyzer:
    """Analyzes PA forms to determine required fields"""
    
    def analyze_pa_form(self, pa_form_path: Path) -> Dict:
        """Analyze PA form to identify required fields"""
        logger.info(f"🔍 ANALYZING PA FORM: {pa_form_path}")
        
        doc = fitz.open(str(pa_form_path))
        page = doc[0]
        
        # Get all text elements with positions
        text_dict = page.get_text("dict")
        text_elements = []
        
        for block in text_dict.get("blocks", []):
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]
                            text_elements.append({
                                "text": text,
                                "x": bbox[0],
                                "y": bbox[1],
                                "x2": bbox[2],
                                "y2": bbox[3],
                                "font_size": span.get("size", 10)
                            })
        
        # Sort by position
        text_elements.sort(key=lambda x: (x["y"], x["x"]))
        
        # Identify field requirements
        required_fields = self._identify_required_fields(text_elements)
        
        # Determine form type
        form_type = self._determine_form_type(text_elements, pa_form_path.name)
        
        # Create field extraction requirements
        extraction_requirements = self._create_extraction_requirements(required_fields, form_type)
        
        doc.close()
        
        analysis_result = {
            "form_path": str(pa_form_path),
            "form_type": form_type,
            "required_fields": required_fields,
            "extraction_requirements": extraction_requirements,
            "fill_coordinates": self._get_fill_coordinates(form_type)
        }
        
        logger.info(f"📋 Found {len(required_fields)} required fields for {form_type}")
        
        return analysis_result
    
    def _identify_required_fields(self, text_elements: List[Dict]) -> List[Dict]:
        """Identify what fields this PA form requires"""
        required_fields = []
        
        # Common PA form field patterns
        field_patterns = [
            # Patient identification
            {"pattern": r"(patient|member).*name", "field_type": "patient_name", "priority": 1},
            {"pattern": r"first.*name", "field_type": "patient_first_name", "priority": 1},
            {"pattern": r"last.*name", "field_type": "patient_last_name", "priority": 1},
            {"pattern": r"date.*birth|dob", "field_type": "patient_dob", "priority": 1},
            {"pattern": r"member.*id|subscriber.*id|policy.*number", "field_type": "insurance_member_id", "priority": 1},
            {"pattern": r"group.*number|group.*id", "field_type": "insurance_group", "priority": 2},
            
            # Patient contact
            {"pattern": r"phone|telephone", "field_type": "patient_phone", "priority": 2},
            {"pattern": r"address", "field_type": "patient_address", "priority": 2},
            {"pattern": r"city", "field_type": "patient_city", "priority": 2},
            {"pattern": r"state", "field_type": "patient_state", "priority": 2},
            {"pattern": r"zip.*code|postal.*code", "field_type": "patient_zip", "priority": 2},
            
            # Provider information
            {"pattern": r"provider.*name|physician.*name|doctor.*name|prescriber.*name", "field_type": "provider_name", "priority": 1},
            {"pattern": r"npi", "field_type": "provider_npi", "priority": 1},
            {"pattern": r"provider.*phone|physician.*phone|office.*phone", "field_type": "provider_phone", "priority": 2},
            {"pattern": r"fax", "field_type": "provider_fax", "priority": 3},
            
            # Clinical information
            {"pattern": r"diagnosis|icd", "field_type": "diagnosis", "priority": 2},
            {"pattern": r"medication|drug.*name", "field_type": "medication", "priority": 1},
            {"pattern": r"dosage|dose", "field_type": "dosage", "priority": 2},
            {"pattern": r"frequency", "field_type": "frequency", "priority": 3},
        ]
        
        for element in text_elements:
            text_lower = element["text"].lower()
            
            # Skip very long text (likely paragraphs)
            if len(element["text"]) > 100:
                continue
            
            for pattern_info in field_patterns:
                if re.search(pattern_info["pattern"], text_lower):
                    # Check if we already have this field type
                    existing = next((f for f in required_fields if f["field_type"] == pattern_info["field_type"]), None)
                    
                    if not existing:
                        required_fields.append({
                            "field_type": pattern_info["field_type"],
                            "label_text": element["text"],
                            "label_position": (element["x"], element["y"]),
                            "priority": pattern_info["priority"],
                            "pattern_matched": pattern_info["pattern"]
                        })
                        logger.info(f"  📌 Found field: {pattern_info['field_type']} - '{element['text']}'")
                    break
        
        # Sort by priority
        required_fields.sort(key=lambda x: x["priority"])
        
        return required_fields
    
    def _determine_form_type(self, text_elements: List[Dict], filename: str) -> str:
        """Determine the type of PA form"""
        all_text = " ".join([elem["text"].lower() for elem in text_elements])
        
        # Form type indicators
        if "rituxan" in all_text or "riabni" in all_text:
            return "medicare_rituxan_pa"
        elif "skyrizi" in all_text:
            return "aetna_skyrizi_pa"
        elif "aetna" in all_text and "medicare" in all_text:
            return "aetna_medicare_pa"
        elif "aetna" in all_text:
            return "aetna_general_pa"
        elif "medicare" in all_text:
            return "medicare_general_pa"
        elif "abdullah" in filename.lower():
            return "medicare_rituxan_pa"
        elif "akshay" in filename.lower():
            return "aetna_skyrizi_pa"
        else:
            return "unknown_pa_form"
    
    def _create_extraction_requirements(self, required_fields: List[Dict], form_type: str) -> Dict:
        """Create specific extraction requirements for referral processing"""
        requirements = {
            "form_type": form_type,
            "required_extractions": {},
            "extraction_prompts": {},
            "validation_rules": {}
        }
        
        for field in required_fields:
            field_type = field["field_type"]
            
            # Create specific extraction requirements
            requirements["required_extractions"][field_type] = {
                "description": self._get_field_description(field_type),
                "expected_format": self._get_expected_format(field_type),
                "extraction_priority": field["priority"],
                "pa_form_label": field["label_text"]
            }
            
            # Create extraction prompts
            requirements["extraction_prompts"][field_type] = self._create_extraction_prompt(field_type)
            
            # Create validation rules
            requirements["validation_rules"][field_type] = self._get_validation_rules(field_type)
        
        return requirements
    
    def _get_field_description(self, field_type: str) -> str:
        """Get human-readable description of field"""
        descriptions = {
            "patient_first_name": "Patient's first/given name",
            "patient_last_name": "Patient's last/family name", 
            "patient_dob": "Patient's date of birth",
            "insurance_member_id": "Insurance member/subscriber ID number",
            "insurance_group": "Insurance group number",
            "patient_phone": "Patient's phone number",
            "patient_address": "Patient's street address",
            "patient_city": "Patient's city",
            "patient_state": "Patient's state (2-letter code)",
            "patient_zip": "Patient's ZIP/postal code",
            "provider_name": "Healthcare provider's full name",
            "provider_npi": "Provider's NPI number (10 digits)",
            "provider_phone": "Provider's office phone number",
            "provider_fax": "Provider's fax number",
            "diagnosis": "Primary diagnosis/condition",
            "medication": "Prescribed medication name",
            "dosage": "Medication dosage",
            "frequency": "Medication frequency"
        }
        return descriptions.get(field_type, f"Information for {field_type}")
    
    def _get_expected_format(self, field_type: str) -> str:
        """Get expected format for field"""
        formats = {
            "patient_dob": "YYYY-MM-DD or MM/DD/YYYY",
            "insurance_member_id": "Alphanumeric ID (varies by insurer)",
            "patient_phone": "XXX-XXX-XXXX or (XXX) XXX-XXXX",
            "provider_phone": "XXX-XXX-XXXX or (XXX) XXX-XXXX",
            "provider_fax": "XXX-XXX-XXXX or (XXX) XXX-XXXX",
            "patient_state": "2-letter state code (e.g., TX, CA)",
            "patient_zip": "5-digit ZIP code or ZIP+4",
            "provider_npi": "10-digit number"
        }
        return formats.get(field_type, "Free text")
    
    def _create_extraction_prompt(self, field_type: str) -> str:
        """Create specific extraction prompt for each field"""
        prompts = {
            "patient_first_name": "Extract the patient's first name from the referral document",
            "patient_last_name": "Extract the patient's last/family name from the referral document",
            "patient_dob": "Extract the patient's date of birth. Look for birth date, DOB, or similar terms",
            "insurance_member_id": "Extract the insurance member ID, subscriber ID, or policy number",
            "insurance_group": "Extract the insurance group number or plan ID",
            "patient_phone": "Extract the patient's primary phone number",
            "patient_address": "Extract the patient's street address (not including city/state/zip)",
            "patient_city": "Extract the patient's city of residence",
            "patient_state": "Extract the patient's state (return as 2-letter code)",
            "patient_zip": "Extract the patient's ZIP code",
            "provider_name": "Extract the referring/prescribing provider's full name",
            "provider_npi": "Extract the provider's NPI number (10-digit identifier)",
            "provider_phone": "Extract the provider's office/clinic phone number",
            "provider_fax": "Extract the provider's fax number",
            "diagnosis": "Extract the primary diagnosis or medical condition requiring this medication",
            "medication": "Extract the specific medication being requested for prior authorization",
            "dosage": "Extract the prescribed dosage of the medication",
            "frequency": "Extract how often the medication should be taken"
        }
        return prompts.get(field_type, f"Extract {field_type} from the referral document")
    
    def _get_validation_rules(self, field_type: str) -> Dict:
        """Get validation rules for field"""
        rules = {
            "patient_dob": {"regex": r"^\d{4}-\d{2}-\d{2}$|^\d{2}/\d{2}/\d{4}$", "required": True},
            "insurance_member_id": {"min_length": 5, "max_length": 20, "required": True},
            "patient_phone": {"regex": r"^\d{3}[-.\s]?\d{3}[-.\s]?\d{4}$", "required": False},
            "provider_phone": {"regex": r"^\d{3}[-.\s]?\d{3}[-.\s]?\d{4}$", "required": False},
            "patient_state": {"regex": r"^[A-Z]{2}$", "required": False},
            "patient_zip": {"regex": r"^\d{5}(-\d{4})?$", "required": False},
            "provider_npi": {"regex": r"^\d{10}$", "required": True}
        }
        return rules.get(field_type, {"required": False})
    
    def _get_fill_coordinates(self, form_type: str) -> Dict:
        """Get fill coordinates for this form type"""
        coordinates = {
            "medicare_rituxan_pa": {
                "insurance_member_id": (150, 250),
                "patient_first_name": (150, 280),
                "patient_last_name": (350, 280),
                "patient_dob": (150, 310),
                "patient_address": (150, 340),
                "patient_city": (150, 370),
                "patient_state": (350, 370),
                "patient_zip": (450, 370),
                "patient_phone": (150, 400),
                "provider_name": (150, 450),
                "provider_npi": (150, 480)
            },
            "aetna_skyrizi_pa": {
                "insurance_member_id": (120, 180),
                "insurance_group": (350, 180),
                "patient_first_name": (120, 220),
                "patient_last_name": (320, 220),
                "patient_dob": (120, 250),
                "patient_phone": (350, 250),
                "patient_address": (120, 280),
                "patient_city": (120, 310),
                "patient_state": (320, 310),
                "patient_zip": (420, 310),
                "provider_name": (120, 380),
                "provider_npi": (120, 410),
                "provider_phone": (120, 440),
                "provider_fax": (350, 440)
            }
        }
        return coordinates.get(form_type, {})

def test_pa_form_analyzer():
    """Test PA form analysis"""
    print("🚀 STEP 1: PA FORM ANALYSIS")
    print("=" * 60)
    
    analyzer = PAFormAnalyzer()
    
    # Test forms
    test_forms = [
        ("Abdullah's Medicare Form", "../Input Data/Adbulla/PA.pdf"),
        ("Akshay's Aetna Form", "../Input Data/Akshay/pa.pdf")
    ]
    
    analyses = []
    
    for form_name, form_path in test_forms:
        print(f"\n📋 {form_name}")
        print("-" * 40)
        
        if not Path(form_path).exists():
            print(f"❌ Form not found: {form_path}")
            continue
        
        # Analyze form
        analysis = analyzer.analyze_pa_form(Path(form_path))
        analyses.append(analysis)
        
        print(f"Form Type: {analysis['form_type']}")
        print(f"Required Fields: {len(analysis['required_fields'])}")
        print(f"Fill Coordinates: {len(analysis['fill_coordinates'])}")
        
        print(f"\n📝 Required Fields:")
        for field in analysis['required_fields']:
            priority_icon = "🔴" if field['priority'] == 1 else "🟡" if field['priority'] == 2 else "🟢"
            print(f"  {priority_icon} {field['field_type']:20s} - {field['label_text']}")
        
        # Save analysis
        analysis_file = f"analysis_{form_name.lower().replace(' ', '_').replace(chr(39), '')}.json"
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2)
        print(f"\n💾 Analysis saved: {analysis_file}")
    
    print(f"\n🎯 PA FORM ANALYSIS COMPLETE!")
    print(f"Analyzed {len(analyses)} forms")
    print(f"Next: Use these requirements to extract specific data from referrals")
    
    return analyses

if __name__ == "__main__":
    test_pa_form_analyzer()