{"form_path": "../Input Data/Adbulla/PA.pdf", "form_type": "medicare_rituxan_pa", "required_fields": [{"field_type": "medication", "label_text": "Medication Precertification Request", "label_position": [171.**************, 73.*************], "priority": 1, "pattern_matched": "medication|drug.*name"}, {"field_type": "insurance_member_id", "label_text": "call the number on the back of the member’s ID card to confirm routing information.", "label_position": [31.***************, 201.*************], "priority": 1, "pattern_matched": "member.*id|subscriber.*id|policy.*number"}, {"field_type": "patient_phone", "label_text": "Phone:", "label_position": [51.***************, 246.**************], "priority": 2, "pattern_matched": "phone|telephone"}, {"field_type": "provider_fax", "label_text": "Fax:", "label_position": [51.***************, 262.*************], "priority": 3, "pattern_matched": "fax"}], "extraction_requirements": {"form_type": "medicare_rituxan_pa", "required_extractions": {"medication": {"description": "Prescribed medication name", "expected_format": "Free text", "extraction_priority": 1, "pa_form_label": "Medication Precertification Request"}, "insurance_member_id": {"description": "Insurance member/subscriber ID number", "expected_format": "Alphanumeric ID (varies by insurer)", "extraction_priority": 1, "pa_form_label": "call the number on the back of the member’s ID card to confirm routing information."}, "patient_phone": {"description": "<PERSON><PERSON>'s phone number", "expected_format": "XXX-XXX-XXXX or (XXX) XXX-XXXX", "extraction_priority": 2, "pa_form_label": "Phone:"}, "provider_fax": {"description": "Provider's fax number", "expected_format": "XXX-XXX-XXXX or (XXX) XXX-XXXX", "extraction_priority": 3, "pa_form_label": "Fax:"}}, "extraction_prompts": {"medication": "Extract the specific medication being requested for prior authorization", "insurance_member_id": "Extract the insurance member ID, subscriber ID, or policy number", "patient_phone": "Extract the patient's primary phone number", "provider_fax": "Extract the provider's fax number"}, "validation_rules": {"medication": {"required": false}, "insurance_member_id": {"min_length": 5, "max_length": 20, "required": true}, "patient_phone": {"regex": "^\\d{3}[-.\\s]?\\d{3}[-.\\s]?\\d{4}$", "required": false}, "provider_fax": {"required": false}}}, "fill_coordinates": {"insurance_member_id": [150, 250], "patient_first_name": [150, 280], "patient_last_name": [350, 280], "patient_dob": [150, 310], "patient_address": [150, 340], "patient_city": [150, 370], "patient_state": [350, 370], "patient_zip": [450, 370], "patient_phone": [150, 400], "provider_name": [150, 450], "provider_npi": [150, 480]}}