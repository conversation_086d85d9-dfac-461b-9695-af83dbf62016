{"success": true, "filled_count": 11, "total_extractable": 11, "total_coordinates": 11, "output_path": "ab<PERSON><PERSON>_PA_DRIVEN_FILLED.pdf", "visual_proof": "a<PERSON><PERSON><PERSON>_PA_DRIVEN_FILLED_visual_proof.png", "filled_fields": [{"field": "insurance_member_id", "value": "A987654321", "position": [150, 250]}, {"field": "patient_first_name", "value": "<PERSON>", "position": [150, 280]}, {"field": "patient_last_name", "value": "<PERSON>", "position": [350, 280]}, {"field": "patient_dob", "value": "1975-08-23", "position": [150, 310]}, {"field": "patient_address", "value": "789 Oak Street", "position": [150, 340]}, {"field": "patient_city", "value": "Dallas", "position": [150, 370]}, {"field": "patient_state", "value": "TX", "position": [350, 370]}, {"field": "patient_zip", "value": "75201", "position": [450, 370]}, {"field": "patient_phone", "value": "************", "position": [150, 400]}, {"field": "provider_name", "value": "<PERSON>, MD", "position": [150, 450]}, {"field": "provider_npi", "value": "**********", "position": [150, 480]}], "errors": [], "form_type": "medicare_rituxan_pa", "pa_form_path": "../Input Data/Adbulla/PA.pdf", "structured_data_fields": 57, "mapped_fields": 11, "field_mapping": {"insurance_member_id": "A987654321", "patient_first_name": "<PERSON>", "patient_last_name": "<PERSON>", "patient_dob": "1975-08-23", "patient_address": "789 Oak Street", "patient_city": "Dallas", "patient_state": "TX", "patient_zip": "75201", "patient_phone": "************", "provider_name": "<PERSON>, MD", "provider_npi": "**********"}}