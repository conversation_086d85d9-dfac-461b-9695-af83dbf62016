"""
STEP 2: TARGETED REFERRAL EXTRACTOR
Extracts specific data from referrals based on PA form requirements
"""

import json
import fitz
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TargetedReferralExtractor:
    """Extracts specific fields from referrals based on PA requirements"""
    
    def __init__(self):
        self.extraction_patterns = self._build_extraction_patterns()
    
    def extract_for_pa_form(self, referral_path: Path, pa_requirements: Dict) -> Dict:
        """Extract specific data needed for a PA form"""
        logger.info(f"🎯 TARGETED EXTRACTION: {referral_path}")
        logger.info(f"📋 PA Form Type: {pa_requirements['form_type']}")
        
        # Read referral document
        referral_text = self._extract_referral_text(referral_path)
        
        # Extract required fields
        extracted_data = {}
        extraction_log = []
        
        required_extractions = pa_requirements['extraction_requirements']['required_extractions']
        
        for field_type, field_info in required_extractions.items():
            logger.info(f"🔍 Extracting: {field_type}")
            
            # Extract using multiple methods
            extracted_value = self._extract_field(
                field_type, 
                referral_text, 
                field_info
            )
            
            if extracted_value:
                # Validate extracted value
                validation_rules = pa_requirements['extraction_requirements']['validation_rules'].get(field_type, {})
                is_valid = self._validate_field(field_type, extracted_value, validation_rules)
                
                if is_valid:
                    extracted_data[field_type] = extracted_value
                    extraction_log.append({
                        "field": field_type,
                        "value": extracted_value,
                        "status": "success",
                        "validation": "passed"
                    })
                    logger.info(f"  ✅ {field_type}: {extracted_value}")
                else:
                    extraction_log.append({
                        "field": field_type,
                        "value": extracted_value,
                        "status": "failed",
                        "validation": "failed"
                    })
                    logger.warning(f"  ⚠️ {field_type}: {extracted_value} (validation failed)")
            else:
                extraction_log.append({
                    "field": field_type,
                    "value": None,
                    "status": "not_found",
                    "validation": "n/a"
                })
                logger.warning(f"  ❌ {field_type}: Not found")
        
        result = {
            "referral_path": str(referral_path),
            "pa_form_type": pa_requirements['form_type'],
            "extracted_data": extracted_data,
            "extraction_log": extraction_log,
            "success_rate": len(extracted_data) / len(required_extractions) * 100 if required_extractions else 0
        }
        
        logger.info(f"📊 Extraction complete: {len(extracted_data)}/{len(required_extractions)} fields ({result['success_rate']:.1f}%)")
        
        return result
    
    def _extract_referral_text(self, referral_path: Path) -> str:
        """Extract text from referral document"""
        doc = fitz.open(str(referral_path))
        full_text = ""
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            full_text += page.get_text() + "\n"
        
        doc.close()
        return full_text
    
    def _extract_field(self, field_type: str, text: str, field_info: Dict) -> Optional[str]:
        """Extract a specific field from text"""
        
        # Use field-specific extraction patterns
        if field_type in self.extraction_patterns:
            patterns = self.extraction_patterns[field_type]
            
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                if matches:
                    # Take the first match and clean it
                    value = matches[0]
                    if isinstance(value, tuple):
                        value = value[0]  # If pattern has groups, take first group
                    
                    cleaned_value = self._clean_extracted_value(field_type, str(value))
                    if cleaned_value:
                        return cleaned_value
        
        # Fallback: context-based extraction
        return self._context_based_extraction(field_type, text)
    
    def _build_extraction_patterns(self) -> Dict[str, List[str]]:
        """Build regex patterns for field extraction"""
        patterns = {
            "patient_first_name": [
                r"(?:patient|pt)(?:\s+name)?[:\s]+([A-Z][a-z]+)",
                r"first\s+name[:\s]+([A-Z][a-z]+)",
                r"^([A-Z][a-z]+)\s+[A-Z][a-z]+(?:\s+DOB)",  # Name before DOB
                r"name[:\s]+([A-Z][a-z]+)\s+[A-Z][a-z]+"
            ],
            
            "patient_last_name": [
                r"(?:patient|pt)\s+name[:\s]+[A-Z][a-z]+\s+([A-Z][a-z]+)",
                r"last\s+name[:\s]+([A-Z][a-z]+)",
                r"^[A-Z][a-z]+\s+([A-Z][a-z]+)(?:\s+DOB)",  # Name before DOB
                r"name[:\s]+[A-Z][a-z]+\s+([A-Z][a-z]+)"
            ],
            
            "patient_dob": [
                r"(?:DOB|date\s+of\s+birth|birth\s+date)[:\s]+(\d{1,2}[/\-]\d{1,2}[/\-]\d{4})",
                r"born[:\s]+(\d{1,2}[/\-]\d{1,2}[/\-]\d{4})",
                r"(\d{1,2}[/\-]\d{1,2}[/\-]\d{4})",  # Any date pattern
                r"age[:\s]+\d+[^\d]*(\d{1,2}[/\-]\d{1,2}[/\-]\d{4})"
            ],
            
            "insurance_member_id": [
                r"(?:member|subscriber|policy)\s+(?:id|number)[:\s]+([A-Z0-9\-]+)",
                r"insurance\s+id[:\s]+([A-Z0-9\-]+)",
                r"member[:\s]+([A-Z0-9]{5,20})",
                r"id[:\s]+([A-Z0-9]{8,20})"
            ],
            
            "insurance_group": [
                r"group\s+(?:number|id)[:\s]+([A-Z0-9\-]+)",
                r"plan\s+(?:number|id)[:\s]+([A-Z0-9\-]+)",
                r"group[:\s]+([A-Z0-9]{3,15})"
            ],
            
            "patient_phone": [
                r"(?:patient|pt)\s+phone[:\s]+([\(\d\)\-\.\s]{10,15})",
                r"home\s+phone[:\s]+([\(\d\)\-\.\s]{10,15})",
                r"phone[:\s]+([\(\d\)\-\.\s]{10,15})",
                r"(\(\d{3}\)\s*\d{3}[-\.\s]?\d{4})",
                r"(\d{3}[-\.\s]?\d{3}[-\.\s]?\d{4})"
            ],
            
            "patient_address": [
                r"(?:address|residence)[:\s]+([0-9]+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd))",
                r"lives\s+at[:\s]+([0-9]+\s+[A-Za-z\s]+)",
                r"address[:\s]+([0-9]+[^,\n]+)"
            ],
            
            "patient_city": [
                r"(?:city|town)[:\s]+([A-Z][a-z\s]+)(?:\s*,|\s+[A-Z]{2})",
                r"address[^,]+,\s*([A-Z][a-z\s]+)(?:\s*,|\s+[A-Z]{2})",
                r",\s*([A-Z][a-z\s]+),\s*[A-Z]{2}"
            ],
            
            "patient_state": [
                r"state[:\s]+([A-Z]{2})",
                r",\s*([A-Z]{2})\s+\d{5}",
                r"([A-Z]{2})\s+\d{5}(?:-\d{4})?"
            ],
            
            "patient_zip": [
                r"(?:zip|postal)\s+code[:\s]+(\d{5}(?:-\d{4})?)",
                r"zip[:\s]+(\d{5}(?:-\d{4})?)",
                r"([A-Z]{2})\s+(\d{5}(?:-\d{4})?)"
            ],
            
            "provider_name": [
                r"(?:provider|physician|doctor|prescriber)[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s*,?\s*MD)?)",
                r"referred\s+by[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+)",
                r"dr\.?\s+([A-Z][a-z]+\s+[A-Z][a-z]+)",
                r"physician[:\s]+([A-Z][a-z]+\s+[A-Z][a-z]+)"
            ],
            
            "provider_npi": [
                r"npi[:\s]+(\d{10})",
                r"provider\s+id[:\s]+(\d{10})",
                r"(\d{10})"  # Any 10-digit number
            ],
            
            "provider_phone": [
                r"(?:provider|office|clinic)\s+phone[:\s]+([\(\d\)\-\.\s]{10,15})",
                r"doctor.*phone[:\s]+([\(\d\)\-\.\s]{10,15})"
            ],
            
            "diagnosis": [
                r"(?:diagnosis|condition|icd)[:\s]+([A-Za-z\s]+)",
                r"diagnosed\s+with[:\s]+([A-Za-z\s]+)",
                r"condition[:\s]+([A-Za-z\s]+)"
            ],
            
            "medication": [
                r"(?:medication|drug|prescription)[:\s]+([A-Za-z\s]+)",
                r"prescribed[:\s]+([A-Za-z\s]+)",
                r"requesting[:\s]+([A-Za-z\s]+)"
            ]
        }
        
        return patterns
    
    def _context_based_extraction(self, field_type: str, text: str) -> Optional[str]:
        """Fallback extraction using context clues"""
        
        # Look for common patterns in medical documents
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            line_lower = line.lower().strip()
            
            if field_type == "patient_first_name":
                if "patient" in line_lower or "name" in line_lower:
                    # Look for capitalized words in this line or next few lines
                    for j in range(i, min(i+3, len(lines))):
                        words = lines[j].split()
                        cap_words = [w for w in words if w[0].isupper() and len(w) > 1 and w.isalpha()]
                        if cap_words:
                            return cap_words[0]
            
            elif field_type == "patient_dob":
                # Look for date patterns
                date_match = re.search(r'\d{1,2}[/\-]\d{1,2}[/\-]\d{4}', line)
                if date_match:
                    return date_match.group()
            
            elif field_type == "insurance_member_id":
                if "member" in line_lower or "id" in line_lower:
                    # Look for alphanumeric IDs
                    id_match = re.search(r'[A-Z0-9]{8,20}', line)
                    if id_match:
                        return id_match.group()
        
        return None
    
    def _clean_extracted_value(self, field_type: str, value: str) -> str:
        """Clean and format extracted values"""
        cleaned = value.strip()
        
        if field_type in ["patient_first_name", "patient_last_name"]:
            # Remove common suffixes and clean names
            cleaned = re.sub(r'\s*(Jr\.?|Sr\.?|III|II)$', '', cleaned)
            cleaned = re.sub(r'[^A-Za-z\s\-\']', '', cleaned)
            
        elif field_type == "patient_dob":
            # Standardize date format
            cleaned = re.sub(r'[^\d/\-]', '', cleaned)
            
        elif field_type == "patient_phone":
            # Clean phone numbers
            cleaned = re.sub(r'[^\d\(\)\-\.\s]', '', cleaned)
            cleaned = re.sub(r'\s+', ' ', cleaned)
            
        elif field_type == "patient_state":
            # Ensure 2-letter state code
            cleaned = cleaned.upper()[:2]
            
        elif field_type == "provider_npi":
            # Ensure 10 digits
            cleaned = re.sub(r'[^\d]', '', cleaned)
            
        return cleaned.strip()
    
    def _validate_field(self, field_type: str, value: str, validation_rules: Dict) -> bool:
        """Validate extracted field value"""
        if not value:
            return not validation_rules.get('required', False)
        
        # Check regex pattern
        if 'regex' in validation_rules:
            pattern = validation_rules['regex']
            if not re.match(pattern, value):
                return False
        
        # Check length constraints
        if 'min_length' in validation_rules:
            if len(value) < validation_rules['min_length']:
                return False
        
        if 'max_length' in validation_rules:
            if len(value) > validation_rules['max_length']:
                return False
        
        return True

def test_targeted_extraction():
    """Test targeted extraction on both forms"""
    print("🚀 STEP 2: TARGETED REFERRAL EXTRACTION")
    print("=" * 60)
    
    extractor = TargetedReferralExtractor()
    
    # Load PA requirements from Step 1
    test_cases = [
        {
            "name": "Abdullah",
            "pa_analysis": "analysis_abdullahs_medicare_form.json",
            "referral": "../Input Data/Adbulla/referral_package.pdf"
        },
        {
            "name": "Akshay", 
            "pa_analysis": "analysis_akshays_aetna_form.json",
            "referral": "../Input Data/Akshay/referral_package.pdf"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n👤 {test_case['name'].upper()}")
        print("-" * 40)
        
        # Load PA requirements
        if not Path(test_case['pa_analysis']).exists():
            print(f"❌ PA analysis not found: {test_case['pa_analysis']}")
            continue
            
        with open(test_case['pa_analysis'], 'r') as f:
            pa_requirements = json.load(f)
        
        # Check referral exists
        if not Path(test_case['referral']).exists():
            print(f"❌ Referral not found: {test_case['referral']}")
            continue
        
        # Extract targeted data
        result = extractor.extract_for_pa_form(
            Path(test_case['referral']),
            pa_requirements
        )
        
        results.append(result)
        
        print(f"PA Form Type: {result['pa_form_type']}")
        print(f"Success Rate: {result['success_rate']:.1f}%")
        print(f"Extracted: {len(result['extracted_data'])} fields")
        
        print(f"\n📋 Extracted Data:")
        for field, value in result['extracted_data'].items():
            print(f"  ✅ {field:20s} = {value}")
        
        print(f"\n📊 Extraction Log:")
        for log_entry in result['extraction_log']:
            status_icon = "✅" if log_entry['status'] == 'success' else "⚠️" if log_entry['status'] == 'failed' else "❌"
            print(f"  {status_icon} {log_entry['field']:20s} - {log_entry['status']} ({log_entry['validation']})")
        
        # Save results
        result_file = f"extraction_result_{test_case['name'].lower()}.json"
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"\n💾 Results saved: {result_file}")
    
    print(f"\n🎯 TARGETED EXTRACTION COMPLETE!")
    print(f"Processed {len(results)} cases")
    print(f"Next: Fill PA forms with extracted data")
    
    return results

if __name__ == "__main__":
    test_targeted_extraction()